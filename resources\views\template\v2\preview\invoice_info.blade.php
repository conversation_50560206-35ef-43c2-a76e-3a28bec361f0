@php
    $invoiceDate = now();
    $dueDate = now()->addDays(7);
@endphp
{{-- in case list style --}}
@if ($invoiceInfoLayout === 'stacked')
	<table class="text-sm border-separate border-spacing-y-1">
		<tbody>
			@if (!$headingInvoiceTitleOnly)
				<tr class="">
					<td class="pe-3">Invoice #</td>
					<td>:</td>
					<td class="text-end ps-1 {{ $invoiceInfoWeight }}">INV-MHU-10062025</td>
				</tr>
			@endif
			<tr>
				<td class="pe-3">Invoice Date</td>
				<td>:</td>
				<td class="text-end ps-1 {{ $invoiceInfoWeight }}">{{ $invoiceDate->format($invoiceDateStyle ?? 'd-m-Y') }}</td>
			</tr>
			<tr>
				<td class="pe-3">Due Date</td>
				<td>:</td>
				<td class="text-end ps-1 {{ $invoiceInfoWeight }}">{{ $dueDate->format($invoiceDateStyle ?? 'd-m-Y') }}</td>
			</tr>
		</tbody>
	</table>
@endif

{{-- in case table style --}}
<div class="flex {{ $invoiceInfoAlignment }}">
	@if ($invoiceInfoLayout === 'grid')
		<table class="text-sm">
			<tbody>
				<tr class="">
					@if (!$headingInvoiceTitleOnly)
						<td class="px-2 py-1 {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 text-center">Invoice No.</td>
					@endif
					<td class="px-2 py-1 {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 text-center">Invoice Date</td>
					<td class="px-2 py-1 {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 text-center">Due Date</td>
				</tr>
				<tr>
					@if (!$headingInvoiceTitleOnly)
						<td class="px-2 py-1 text-center {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 {{ $invoiceInfoWeight }}">INV-MHU-10062025</td>
					@endif
					<td class="px-2 py-1 text-center {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 {{ $invoiceInfoWeight }}">{{ $invoiceDate->format($invoiceDateStyle ?? 'd-m-Y') }}</td>
					<td class="px-2 py-1 text-center {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 {{ $invoiceInfoWeight }}">{{ $dueDate->format($invoiceDateStyle ?? 'd-m-Y') }}</td>
				</tr>
			</tbody>
		</table>
	@endif
	@if ($invoiceInfoLayout === 'flex')
		<div class="flex">
			<div class=" {{ $invoiceInfoIsBordered ? 'border' : '' }} px-2 py-1">
				<span class="me-2">Invoice No.:</span>
				<span class="{{ $invoiceInfoWeight }}" style="text-wrap: nowrap">INV-MHU-10062025</span>
			</div>
		</div>
		<div class="flex">
			<div class=" {{ $invoiceInfoIsBordered ? 'border' : '' }} px-2 py-1">
				<span class="me-2">Invoice Date:</span>
				<span class="{{ $invoiceInfoWeight }}" style="text-wrap: nowrap">{{ $invoiceDate->format($invoiceDateStyle ?? 'd-m-Y') }}</span>
			</div>
			<div class=" {{ $invoiceInfoIsBordered ? 'border' : '' }} px-2 py-1">
				<span class="me-2">Due Date:</span>
				<span class="{{ $invoiceInfoWeight }}" style="text-wrap: nowrap">{{ $dueDate->format($invoiceDateStyle ?? 'd-m-Y') }}</span>
			</div>
		</div>
	@endif
</div>

