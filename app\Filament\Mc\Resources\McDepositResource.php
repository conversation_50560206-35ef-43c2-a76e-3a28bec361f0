<?php

namespace App\Filament\Mc\Resources;

use App\Filament\Mc\Resources\McDepositResource\Pages;
use App\Filament\Mc\Resources\McDepositResource\RelationManagers;
use App\Models\McBank;
use App\Models\McCustomer;
use App\Models\McDeposit;
use Dom\Text;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\QueryBuilder;
use Filament\Tables\Filters\QueryBuilder\Constraints\BooleanConstraint;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class McDepositResource extends Resource
{
    protected static ?string $model = McDeposit::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	protected static ?string $title = 'MC Deposits';
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	protected static bool $shouldRegisterNavigation = false;

	public static function getNavigationLabel(): string
	{
		return 'Deposits/Payments';
	}
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                /*
					'trx_code',
					'trx_type',
					'mc_customer_id',
					'bank_id',
					'amount',
					'origin_bank',
					'origin_account_name',
					'origin_account_no',
					'slip_date',
					'ref_code',
					'attachment',
					'evidence',
					'status',
					'validation',
				*/
				Hidden::make('trx_type')->default('incoming'),
				Select::make('mc_customer_id')
					->relationship('customer', 'name')
					->required()
					->options(McCustomer::query()->get()->pluck('name', 'id'))
					->searchable(),
				Select::make('bank_id')
					->relationship('bank', 'name')
					->options(McBank::query()->get()->pluck('name', 'id'))
					->searchable(),
				DatePicker::make('slip_date')
					->default(now())
					->required()
					->maxDate(today()),
				TextInput::make('ref_code'),
				TextInput::make('amount')
					->prefix('IDR')
					->required()
					->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
			->modifyQueryUsing(function (Builder $query) {
				$query->where('trx_type', 'incoming');
			})
            ->columns([
				ToggleColumn::make('validation')
					->label('Verified')
					->tooltip('switch to ON when transaction is verified')
					->sortable(),
                TextColumn::make('trx_code')
					->searchable(),
                TextColumn::make('customer.name')
					->searchable(),
				TextColumn::make('bank.name')
					->searchable(),
				TextColumn::make('slip_date')
					->date()
					->searchable(),
				TextColumn::make('ref_code')
					->searchable(),
				TextColumn::make('trx_type')
					->badge()
					->color(fn ($state) => Str::contains(strtolower($state), 'incoming') ? 'success' : 'danger'),
				TextColumn::make('amount')
					->money('IDR')
					->alignEnd()
					->searchable(),
            ])
            ->filters([
				SelectFilter::make('validation')
					->label('Verification Status')
					->default(0)
					->options([
						0 => 'Unverified',
						1 => 'Verified',
					]),
				Filter::make('slip_date')
					->form([
						DatePicker::make('slip_date')->default(today()),
					])
					->query(function (Builder $query, array $data): Builder {
						return $query
							->when(
								$data['slip_date'],
								fn(Builder $query, $date): Builder => $query->whereDate('slip_date', '=', $date),
							);
					}),
            ], layout: FiltersLayout::AboveContent)->hiddenFilterIndicators()
            ->actions([

                ViewAction::make()
					->iconButton()
					->tooltip('View')
					->modalHeading(fn ($record) => $record->trx_code)
					->modalDescription(function ($record) {
						//jika mengandung cls ''
						if (str_contains($record->trx_code, 'CLS')) {
							return new HtmlString('<span class="text-sm text-danger-500">This is a Closing Transaction (summary)</span>');
						}
					})
					->modalFooterActionsAlignment(Alignment::Right),
                EditAction::make()
					->iconButton()
					->tooltip('Edit')
					->modalHeading(fn ($record) => $record->trx_code)
					->modalDescription(function ($record) {
						//jika mengandung cls ''
						if (str_contains($record->trx_code, 'CLS')) {
							return new HtmlString('<span class="text-sm text-danger-500">This is a Closing Transaction (summary)</span>');
						}
					})
					->modalFooterActionsAlignment(Alignment::Right)
					->visible(fn ($record) => $record->trx_type === 'incoming'),
                DeleteAction::make()
					->iconButton()
					->tooltip('Delete')
					->visible(fn ($record) => $record->trx_type === 'incoming')
					->requiresConfirmation()
					->modalHeading('Delete Transaction')
					->modalDescription('Are you sure you want to delete this transaction?')
					->modalSubmitActionLabel('Yes, delete it'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMcDeposits::route('/'),
            // 'create' => Pages\CreateMcDeposit::route('/create'),
            // 'edit' => Pages\EditMcDeposit::route('/{record}/edit'),
        ];
    }
}
