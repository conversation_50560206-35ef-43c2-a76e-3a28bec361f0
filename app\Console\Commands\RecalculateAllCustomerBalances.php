<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\McCustomer;
use App\Services\RecalculateCustomerBalanceService;

class RecalculateAllCustomerBalances extends Command
{
    protected $signature = 'recalc:customers';
    protected $description = 'Recalculate balances for all McCustomers';

    public function handle()
    {
        $this->info('Starting recalculation...');

        McCustomer::pluck('id')->each(function ($id) {
            RecalculateCustomerBalanceService::run($id);
            $this->line("Customer {$id} recalculated.");
        });

        $this->info('Finished.');
    }
}
