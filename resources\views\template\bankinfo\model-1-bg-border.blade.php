@php
    // Define bank fields with their labels and special handling
    $bankFields = [
        'bank_acc_name' => 'Account Name',
        'bank_acc_no' => 'Account Number',
        'bank_acc_address' => 'Account Address',
        'bank_name' => 'Bank Name',
        'bank_address' => 'Bank Address',
        'bank_code' => 'Bank Code',
        'swift' => 'Swift Code',
        'swift_correspondent' => 'Swift Correspondent',
        'routing_no' => 'Routing No',
        'transit' => 'Transit',
        'tt_charge' => 'TT Charge',
        'iban' => 'IBAN',
        'bsb' => 'BSB',
        'branch_code' => 'Branch Code',
        'branch_bank' => 'Branch Bank',
        'institution' => 'Institution Code',
        'sort_code' => 'Sort Code',
        'ABA' => 'ABA',
        'IFSC' => 'IFSC Code',
        'bank_correspondent' => 'Bank Correspondent',
    ];

    // Fields that need text wrapping
    $wrapFields = ['bank_acc_address', 'bank_address'];

    // Fields that need strip_tags
    $stripTagsFields = ['bank_acc_address', 'bank_address', 'swift'];
@endphp

<span class="fw-bold">Bank Informations</span>
<div class="row align-items-md-stretch">
    @if(isset($payload['banks']) && $payload['banks']->isNotEmpty())
        @foreach ($payload['banks'] as $bank)
            <div class="h-100 mb-2">
                <div class="h-100 p-2 bg-light border rounded-3">
                    <ul class="list-unstyled row">
                        @foreach($bankFields as $field => $label)
                            @if(isset($bank[$field]))
                                <li class="d-flex justify-content-start {{ $field === 'swift' ? 'mt-3' : '' }} {{ $field === 'swift_correspondent' ? 'mb-3' : '' }}">
                                    <span class="col-3">{{ $label }}:</span>
                                    <span class="{{ in_array($field, $wrapFields) ? 'col-6 text-wrap' : 'col-7' }}">
                                        @if(in_array($field, $stripTagsFields))
                                            {{ strip_tags($bank[$field]) }}
                                        @else
                                            {{ $bank[$field] }}
                                        @endif
                                    </span>
                                </li>
                            @endif
                        @endforeach

                        {{-- Custom Columns Support --}}
                        @if(isset($bank['custom_columns']) && is_array($bank['custom_columns']))
                            @foreach($bank['custom_columns'] as $label => $field)
                                @if(is_array($field) && isset($field['value']) && !empty($field['value']))
                                    <li class="d-flex justify-content-start">
                                        <span class="col-3">{{ ucwords(str_replace('_', ' ', $label)) }}:</span>
                                        <span class="col-7">
                                            @if(isset($field['type']) && $field['type'] === 'richtext')
                                                {!! strip_tags($field['value'], '<p><br><strong><b><em><i><u><ul><ol><li>') !!}
                                            @else
                                                {{ $field['value'] }}
                                            @endif
                                        </span>
                                    </li>
                                @endif
                            @endforeach
                        @endif
                    </ul>
                </div>
            </div>
        @endforeach
    @endif
</div>
