<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Browsershot\Browsershot;

class BriSlipKliringController extends Controller
{
	public function payload()
	{
		$data = [
            'send_to' => 'PT. ABC Indonesia',
            'address1' => 'Jl. Mawar No. 123',
            'address2' => 'Jakarta Selatan',
            'bank' => 'BANK MANDIRI',
            'no_rekening' => '**********',
            'kota' => 'Jakarta',
            'negara' => 'Indonesia',
            'kode_bank' => '008',
            'via_branch' => 'KCP Jakarta Pusat',
            'sender' => '<PERSON>',
            'sender_address1' => 'Jl. Melati No. 45',
            'sender_address2' => 'Bandung',
            'source_fund' => 'Hasil Penjualan',
            'purpose' => 'Pembayaran invoice',
            'occupation' => 'Wiraswasta',
            'position' => 'Direktur',
            'birth' => 'Bandung, 01/01/1980',
            'ktp' => '****************',
            'amount_sent' => '10.000.000',
            'charge' => '5.000',
            'amount_total' => '10.005.000',
            'amount_words' => 'Sepuluh juta lima ribu rupiah',
            'berita' => 'Pembayaran invoice #001',
            'ttd' => 'John Doe',
        ];

		return $data;
	}

	public function slipPdf()
	{
		$data = $this->payload();
		// dd($payload);
		$html = view('template.briKliringSlip', compact('data'))->render();

        $pdf = Browsershot::html($html)
            ->format('A4') // mm to inch
            ->margins(0, 0, 0, 0)
            // ->showBackground()
            ->pdf(); // langsung return binary PDF

        return response($pdf, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="slip-kliring.pdf"',
        ]);
	}
}
