# Invoice Template Maker
## Batasan
backward compatibility dengan template invoice yang sudah ada. ini berarti template invoice yang sudah ada tidak perlu diubah, setiap perusahaan yang sudah ada tetap dapat menggunakan template invoice yang sudah ada sebelumnya atau membuat template baru jika ingin melalui tool ini.
mudah untuk diintegrasikan dengan filament builder.
mudah digunakan oleh user yang tidak terlalu teknis/awam.

## Fitur yang ingin dikembangkan.
Setiap kali Staff invoice mendapatkan perusahaan baru, mereka harus membuat template invoice baru untuk perusahaan tersebut. yang selama ini dibuat menggunakan blade html secara manual oleh developer. ini tentu akan sangat menghambat karena proses pembuatan secara manual akan sangat lambat dan tidak efisien.

## Tujuan
menyediakan tool untuk membuat template invoice secara otomatis berdasarkan inputan user. editable deletable replicable.
component yang dibutuhkan dalam template invoice:
- kop surat
- company dan bill to
- table
- inword
- footer

desain dan tata letak yang sangat dinamis. dapat dibatasi dengan delivered section, misal:
Section Header = Kop surat, tata letak dan design yang sangat dinamis
Section Company = Company dan bill to, tata letak dan design yang sangat dinamis
Section Body Message = Message, tata letak dan design yang sangat dinamis.
Section Table = Table, tata letak dan design yang sangat dinamis
Section Inword = Inword, tata letak dan design yang sangat dinamis
Section Footer = Footer, tata letak dan design yang sangat dinamis

