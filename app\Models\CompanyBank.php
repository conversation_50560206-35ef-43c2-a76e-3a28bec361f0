<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyBank extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'bank_acc_name',
		'bank_code',
        'bank_acc_no',
        'bank_acc_address',
        'bank_name',
        'bank_address',
		'bank_correspondent',
        'swift',
        'swift_correspondent',
		'routing_no',
		'transit',
		'tt_charge',
		'institution',
		'iban',
		'bsb',
		'branch_code',
		'sort_code',
		'branch_bank',
		'ABA',
		'IFSC',
		'is_default',
		'include_in_invoice',
		'custom_columns',
    ];

    protected $casts = [
        'custom_columns' => 'array',
		'is_default' => 'boolean',
		'include_in_invoice' => 'boolean',
    ];

    /**
     * Get all delivered (existing) column names
     *
     * @return array
     */
    public static function getDeliveredColumns(): array
    {
        return [
            'company_id', 'bank_acc_name', 'bank_code', 'bank_acc_no', 'bank_acc_address',
            'bank_name', 'bank_address', 'bank_correspondent', 'swift', 'swift_correspondent',
            'routing_no', 'transit', 'tt_charge', 'institution', 'iban', 'bsb',
            'branch_code', 'sort_code', 'branch_bank', 'ABA', 'IFSC', 'is_default', 'include_in_invoice', 'custom_columns',
            'created_at', 'updated_at', 'deleted_at'
        ];
    }

	public function scopeWithDepositSummary(Builder $query): Builder
	{
		return $query
			->leftJoin('view_company_deposit_summary', 'company_banks.id', '=', 'view_company_deposit_summary.bank_id')
			->select([
				'company_banks.*',
				'view_company_deposit_summary.total_in',
				'view_company_deposit_summary.total_out',
				'view_company_deposit_summary.total_order',
				'view_company_deposit_summary.balance',
			]);
	}

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

	public function order()
    {
        return $this->hasMany(Order::class, 'bank_id', 'id');
    }

	public function deposit()
    {
        return $this->hasMany(CompanyDeposito::class, 'bank_id', 'id');
    }

	public function depositSummary()
    {
        return $this->hasMany(CompanyDepositSummary::class, 'bank_id', 'id');
    }

    /**
     * Get existing custom column keys for this record
     *
     * @return array
     */
    public function getExistingCustomKeys(): array
    {
        return array_keys($this->custom_columns ?? []);
    }

    /**
     * Check if a key already exists (in delivered columns or custom columns)
     *
     * @param string $key
     * @return bool
     */
    public function keyExists(string $key): bool
    {
        return in_array($key, self::getDeliveredColumns()) ||
               in_array($key, $this->getExistingCustomKeys());
    }
}
