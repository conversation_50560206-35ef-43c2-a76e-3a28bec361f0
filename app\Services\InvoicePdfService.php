<?php

namespace App\Services;

use App\Models\McCustomer;
use App\Models\McDeposit;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Spatie\Browsershot\Browsershot;

class InvoicePdfService
{
	public function generatePdf(array $payload, array $layoutConfig): string
	{
		try {
			$invoice = $payload['invoice'];
			$templateVersion = $payload['templateVersion'] ?? 'v2';

			$paperSize = strtolower($layoutConfig['paper_size'] ?? 'legal');
			$isF4 = $paperSize === 'f4';
			$paperFormat = match ($paperSize) {
				'a4' => 'A4',
				'letter' => 'Letter',
				'legal' => 'Legal',
				'f4' => 'custom',
				default => 'a4'
			};

			$marginTop = max($layoutConfig['margin_top'] ?? 10, $isF4 ? 10 : 5);
			$marginRight = max($layoutConfig['margin_right'] ?? 10, $isF4 ? 10 : 5);
			$marginBottom = max($layoutConfig['margin_bottom'] ?? 10, $isF4 ? 10 : 5);
			$marginLeft = max($layoutConfig['margin_left'] ?? 10, $isF4 ? 10 : 5);

			$html = View::make($templateVersion === 'v1'
				? 'invoice.' . $invoice->company->template
				: 'template.v2.printPreview', compact('payload'))->render();

			$browsershot = Browsershot::html($html);

			if ($isF4) {
				$browsershot->paperSize(210, 330, 'mm');
			} else {
				$browsershot->format($paperFormat);
			}

			$optimalScale = $this->calculateOptimalScale($html, $paperSize, $isF4);

			$pdfContent = $browsershot
				->margins($marginTop, $marginRight, $marginBottom, $marginLeft)
				->noSandbox()
				->showBackground()
				->waitUntilNetworkIdle()
				->scale($optimalScale)
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', false)
				->pdf();

			return $pdfContent;
		} catch (\Exception $e) {
			Log::error('Gagal generate PDF', [
				'invoice_id' => $payload['invoice']->id ?? null,
				'message' => $e->getMessage(),
				'trace' => $e->getTraceAsString(),
				'timestamp' => now()->toDateTimeString(),
			]);

			throw $e; // atau return fallback sesuai kebutuhan
		}
	}

	protected function calculateOptimalScale(string $html, string $paperSize, bool $isF4): float
	{
		// kalkulasi skala optimal di sini (misal heuristik tetap)
		return 0.94;
	}
}
