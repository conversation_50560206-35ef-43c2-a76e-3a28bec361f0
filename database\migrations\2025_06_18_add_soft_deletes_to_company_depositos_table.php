<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_depositos', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Update the view to exclude soft deleted records
        DB::statement("
            CREATE OR REPLACE VIEW view_company_deposit_summary AS
            SELECT
                bank_id,
                SUM(CASE WHEN trx_type = 'in' THEN amount ELSE 0 END) AS total_in,
                SUM(CASE WHEN trx_type = 'out' THEN amount ELSE 0 END) AS total_out,
                SUM(CASE WHEN trx_type = 'order' THEN amount ELSE 0 END) AS total_order,
                SUM(CASE
                    WHEN trx_type = 'in' THEN amount
                    WHEN trx_type IN ('out', 'order') THEN -amount
                    ELSE 0
                END) AS balance
            FROM
                company_depositos
            WHERE
                deleted_at IS NULL
            GROUP BY
                bank_id;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_depositos', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Revert the view to original version
        DB::statement("
            CREATE OR REPLACE VIEW view_company_deposit_summary AS
            SELECT
                bank_id,
                SUM(CASE WHEN trx_type = 'in' THEN amount ELSE 0 END) AS total_in,
                SUM(CASE WHEN trx_type = 'out' THEN amount ELSE 0 END) AS total_out,
                SUM(CASE WHEN trx_type = 'order' THEN amount ELSE 0 END) AS total_order,
                SUM(CASE
                    WHEN trx_type = 'in' THEN amount
                    WHEN trx_type IN ('out', 'order') THEN -amount
                    ELSE 0
                END) AS balance
            FROM
                company_depositos
            GROUP BY
                bank_id;
        ");
    }
};
