@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$client = $payload['invoice']->client;

	// Bill to configuration
	$billToContact = $layoutConfig['billtoContact'] ?? 'inline';
@endphp

{{-- Bill To Section --}}
<div class="bill-to-section">
	<div class="flex items-start">
		<span class="pe-4 text-compact font-medium">Bill To:</span>
		<div class="flex-grow space-y-2">
			<div class="text-compact font-semibold">
				{{ $client->name }}
			</div>
			<div class="text-compact text-gray-700 mt-1">
				@if($client->address)
					<div class="break-words max-w-xs">{!! $client->address !!}</div>
				@endif

				@if($billToContact === 'stacked')
					@if($client->phone || $client->email)
						<div class="mt-1">
							@if($client->phone){{ $client->phone }}@endif
							@if($client->phone && $client->email) - @endif
							@if($client->email){{ $client->email }}@endif
						</div>
					@endif
				@else
					{{-- Inline contact info --}}
					@if($client->phone || $client->email)
						<div class="mt-1">
							@if($client->phone){{ $client->phone }}@endif
							@if($client->phone && $client->email) - @endif
							@if($client->email){{ $client->email }}@endif
						</div>
					@endif
				@endif
			</div>
		</div>
	</div>
</div>
