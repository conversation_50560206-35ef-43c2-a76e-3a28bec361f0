<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class CompanyTransactionDetails extends Page
{
    protected static string $resource = CompanyResource::class;
    protected static string $view = 'filament.admin.resources.deposit-resource.pages.company-transaction-details';
    protected static ?string $title = 'Company Transaction Details';

    // public function mount(): mixed
    // {
    //     if (Auth::check() && Auth::user()->hasRole('Staff Invoice')) {
    //         return redirect(CompanyDepositoResource::getUrl('index'));
    //     }
    //     return null;
    // }
}
