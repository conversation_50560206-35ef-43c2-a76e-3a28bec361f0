import { defineConfig } from 'vite';
import laravel, { refreshPaths } from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js', 'resources/css/theme.css'],
            refresh: [
				true,
				...refreshPaths,
				'**/views/**/*.blade.php',
				'**/Filament/**/*.php',
				'**/Livewire/**/*.php',
				'**/Components/**/*.php',
				'**/Pages/**/*.php',
				'**/Layouts/**/*.php',
				'**/Forms/**/*.php',
				'**/Widgets/**/*.php',
				'**/Tables/**/*.php',
				'**/Infolists/**/*.php',
				'**/Notifications/**/*.php',
				'**/Actions/**/*.php',
				'**/Resources/**/*.php',

			],
        }),
    ],
});
