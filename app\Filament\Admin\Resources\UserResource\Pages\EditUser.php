<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use TomatoPHP\FilamentApi\Traits\InteractWithAPI;

class EditUser extends EditRecord
{
    // use InteractWithAPI;

    public static function getFilamentAPIMiddleware(): array
    {
        return [];
    }
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (empty($data['username']) && !empty($data['name'])) {
            $username = strtolower(str_replace(' ', '_', $data['name']));
            $originalUsername = $username;
            $counter = 1;

            while (\App\Models\User::where('username', $username)
                ->where('id', '!=', $this->record->id)
                ->exists()) {
                $username = $originalUsername . '_' . $counter;
                $counter++;
            }

            $data['username'] = $username;
        }

        return $data;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
