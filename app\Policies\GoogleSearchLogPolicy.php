<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\GoogleSearchLog;
use App\Models\User;

class GoogleSearchLogPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any GoogleSearchLog');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, GoogleSearchLog $googlesearchlog): bool
    {
        return $user->checkPermissionTo('view GoogleSearchLog');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create GoogleSearchLog');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, GoogleSearchLog $googlesearchlog): bool
    {
        return $user->checkPermissionTo('update GoogleSearchLog');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, GoogleSearchLog $googlesearchlog): bool
    {
        return $user->checkPermissionTo('delete GoogleSearchLog');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any GoogleSearchLog');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, GoogleSearchLog $googlesearchlog): bool
    {
        return $user->checkPermissionTo('restore GoogleSearchLog');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any GoogleSearchLog');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, GoogleSearchLog $googlesearchlog): bool
    {
        return $user->checkPermissionTo('replicate GoogleSearchLog');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder GoogleSearchLog');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, GoogleSearchLog $googlesearchlog): bool
    {
        return $user->checkPermissionTo('force-delete GoogleSearchLog');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any GoogleSearchLog');
    }
}
