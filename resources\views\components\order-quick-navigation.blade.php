@props(['order', 'mode' => 'view'])

@php
    $chainData = [];

    // Tambahkan order sebagai root awal
    $chainData[] = $order;

    // Ambil invoice dari order
    $invoice = $order->invoice;

    if ($invoice) {
        $current = $invoice;

        // Naik ke root invoice
        while ($current?->parent_invoice_id) {
            $current = $current->parentInvoice;
        }

        // Tambahkan invoice chaining dari root
        if ($current) {
            $chainData[] = $current;
            while ($current->childInvoice()->exists()) {
                $current = $current->childInvoice()->first();
                $chainData[] = $current;
            }
        }

        // cari index invoice saat ini (skip order)
        $currentInvoiceId = $invoice->id ?? null;
        $currentIndex = array_search(
            $currentInvoiceId,
            array_map(function ($item) {
                return $item instanceof \App\Models\Invoice ? $item->id : null;
            }, $chainData),
        );
    } else {
        // tidak ada invoice sama sekali
        $currentInvoiceId = null;
        $currentIndex = null;
    }
@endphp

<div>
    <p class="text-md text-gray-500 mb-6">Order details to invoice chainings included when available</p>
</div>
@if ($invoice)
<div class="bg-white dark:bg-transparent border border-gray-200 rounded-lg p-2 space-y-3">
    <div class="items-center justify-between space-y-2">
        <div class="flex items-center space-x-5">
            <div class="flex items-center space-x-1 me-5">
                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1">
                    </path>
                </svg>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-200 ms-2 pe-3">Chain Navigation:</span>
            </div>
            <div class="flex items-center space-x-3">
                @foreach ($chainData as $index => $chainItem)
                    @php
                        $isOrder = $chainItem instanceof \App\Models\Order;
                    @endphp

                    @if ($isOrder)
                        {{-- Order selalu ditampilkan sebagai aktif (tidak diklik) --}}
                        <span class="px-3 py-1 text-xs font-bold rounded border-2"
                            style="background-color: #ecfccb; border-color: #65a30d; color: #365314;">
                            Order
                        </span>
                    @else
                        @php
                            $label = $index === 1 ? 'Main' : 'L' . ($index);
                            $url = route('filament.admin.resources.invoices.' . $mode, $chainItem->id);
                            $companyName = $chainItem->company->name ?? 'Unknown';
                        @endphp

                        <a href="{{ $url }}"
                            class="px-3 py-1 text-xs font-medium rounded border transition-colors"
                            style="background-color: #f3f4f6; border-color: #d1d5db; color: #374151;"
                            onmouseover="this.style.backgroundColor='#e5e7eb'"
                            onmouseout="this.style.backgroundColor='#f3f4f6'"
                            title="Go to {{ $label }} - {{ $companyName }}">
                            {{ $label }}
                        </a>
                    @endif

                    @if (!$loop->last)
                        <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    @endif
                @endforeach

            </div>

        </div>

        <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-200 mt-2">
            @if ($currentIndex !== false)
                <span>Position: 1 of {{ count($chainData) }}</span>
                @if (count($chainData) > 2)
                    <span class="text-gray-300">|</span>
                    <span>Total Value: {{ $chainData[1]->currency->symbol ?? '$' }}
                        {{ number_format($chainData[1]->invoice_amount ?? 0, 2) }}</span>
                @endif
            @endif

        </div>
    </div>

    {{-- Quick Actions --}}
    <div class="mt-2 pt-3 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex space-x-1">

                {{-- Next --}}
                @if ($currentIndex < count($chainData) - 1)
                    <x-filament::button tag="a" size="xs" icon="heroicon-m-chevron-right"
                        icon-position="after"
                        href="{{ route('filament.admin.resources.invoices.' . $mode, $chainData[$currentIndex + 1]->id) }}">
                        Next
                    </x-filament::button>
                @endif

                {{-- Jump to Main --}}
                @if ($currentIndex > 1)
                    <x-filament::button outlined tag="a" size="xs" color="warning" icon="heroicon-o-home"
                        href="{{ route('filament.admin.resources.invoices.' . $mode, $chainData[1]->id) }}">
                        Jump to Main Invoice
                    </x-filament::button>
                @endif
            </div>

            {{-- Chain Summary --}}
            <div>
                @if (count($chainData) === 2)
                    <x-filament::badge color="warning" icon="heroicon-o-bolt">
                        Single Invoice
                    </x-filament::badge>
                @else
                    <x-filament::badge color="success" icon="heroicon-o-link">
                        {{ count($chainData) }} Chains
                    </x-filament::badge>
                @endif
            </div>
        </div>
    </div>
</div>
@endif
