<?php

namespace App\Filament\Admin\Resources\InvoiceResource\Pages;

use App\Filament\Admin\Resources\InvoiceResource;
use App\Models\Currency;
use App\Models\Invoice;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{DatePicker, Group, Hidden, Placeholder, RichEditor, Section, Select, TextInput};
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class CreateInvoice extends CreateRecord
{
    protected static string $resource = InvoiceResource::class;

    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'New Invoice';

    public function getHeading(): string
	{
        return 'New Invoice';
	}

	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-md"></span>');
    }

	// protected function mutateFormDataBeforeCreate(array $data): array
	// {
	// 	$data['status'] = 'Draft';

	// 	return $data;
	// }
}
