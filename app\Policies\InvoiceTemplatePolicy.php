<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\InvoiceTemplate;
use App\Models\User;

class InvoiceTemplatePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any InvoiceTemplate');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, InvoiceTemplate $invoicetemplate): bool
    {
        return $user->checkPermissionTo('view InvoiceTemplate');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create InvoiceTemplate');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, InvoiceTemplate $invoicetemplate): bool
    {
        return $user->checkPermissionTo('update InvoiceTemplate');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, InvoiceTemplate $invoicetemplate): bool
    {
        return $user->checkPermissionTo('delete InvoiceTemplate');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any InvoiceTemplate');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, InvoiceTemplate $invoicetemplate): bool
    {
        return $user->checkPermissionTo('restore InvoiceTemplate');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any InvoiceTemplate');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, InvoiceTemplate $invoicetemplate): bool
    {
        return $user->checkPermissionTo('replicate InvoiceTemplate');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder InvoiceTemplate');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, InvoiceTemplate $invoicetemplate): bool
    {
        return $user->checkPermissionTo('force-delete InvoiceTemplate');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any InvoiceTemplate');
    }
}
