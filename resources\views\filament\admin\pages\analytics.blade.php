<x-filament-panels::page>
    @if (empty($this->activeWidget))
        <div class="flex flex-col items-center justify-center py-16 space-y-4">
            <div class="w-24 h-24 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                <x-heroicon-o-chart-bar class="w-12 h-12 text-gray-400" />
            </div>
            <div class="text-center space-y-2">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Select Analytics Widget
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 max-w-md">
                    Choose one of the analytics widgets from the buttons above to view detailed insights and data visualizations.
                </p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 justify-center mt-6 max-w-4xl mx-auto">
                <button
                    wire:click="showWidget('revenue')"
                    class="inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                    <x-heroicon-o-currency-dollar class="w-5 h-5 mr-2" />
                    Revenue
                </button>
                <button
                    wire:click="showWidget('cashflow')"
                    class="inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                    <x-heroicon-o-banknotes class="w-5 h-5 mr-2" />
                    Cash Flow
                </button>
                <button
                    wire:click="showWidget('trends')"
                    class="inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                    <x-heroicon-o-chart-bar class="w-5 h-5 mr-2" />
                    Trends
                </button>
                <button
                    wire:click="showWidget('deposits')"
                    class="inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                    <x-heroicon-o-building-library class="w-5 h-5 mr-2" />
                    Deposits
                </button>
                <button
                    wire:click="showWidget('rejections')"
                    class="inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                    <x-heroicon-o-x-circle class="w-5 h-5 mr-2" />
                    Rejections
                </button>
                <button
                    wire:click="showWidget('profit')"
                    class="inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                    <x-heroicon-o-chart-pie class="w-5 h-5 mr-2" />
                    Profit
                </button>
                <button
                    wire:click="showWidget('shortcuts')"
                    class="inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
                >
                    <x-heroicon-o-command-line class="w-5 h-5 mr-2" />
                    Shortcuts
                </button>
            </div>
        </div>
    @else
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    @switch($this->activeWidget)
                        @case('revenue')
                            <x-heroicon-o-currency-dollar class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Revenue Analytics</h2>
                            @break
                        @case('cashflow')
                            <x-heroicon-o-banknotes class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Cash Flow Analysis</h2>
                            @break
                        @case('trends')
                            <x-heroicon-o-chart-bar class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Trends Analysis</h2>
                            @break
                        @case('deposits')
                            <x-heroicon-o-building-library class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Deposit Summary</h2>
                            @break
                        @case('rejections')
                            <x-heroicon-o-x-circle class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Rejection Analytics</h2>
                            @break
                        @case('profit')
                            <x-heroicon-o-chart-pie class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Profit Overview</h2>
                            @break
                        @case('shortcuts')
                            <x-heroicon-o-command-line class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Shortcuts</h2>
                            @break
                    @endswitch
                </div>
                <button
                    wire:click="showWidget('')"
                    class="inline-flex items-center px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
                >
                    <x-heroicon-o-x-mark class="w-4 h-4 mr-1" />
                    Close
                </button>
            </div>

            <!-- Widget Container -->
            <div class="widget-container">
                @foreach ($this->getWidgets() as $widget)
                    @livewire($widget, [], key($widget))
                @endforeach
            </div>
        </div>
    @endif
</x-filament-panels::page>
