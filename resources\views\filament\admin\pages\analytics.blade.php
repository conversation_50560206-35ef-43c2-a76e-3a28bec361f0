<x-filament-panels::page>
    <style>
        @keyframes fade-in {
            from { opacity: 0; transform: translateY(20px) translateX(-50%); }
            to { opacity: 1; transform: translateY(0) translateX(-50%); }
        }
        .animate-fade-in {
            animation: fade-in 0.3s ease-out;
        }
        .floating-button {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .floating-button:hover {
            transform: scale(1.1);
        }
        .floating-button.active {
            transform: scale(1.05);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
    @if (empty($this->activeWidget))
        <div class="flex flex-col items-center justify-center py-16 space-y-4">
            <div class="w-24 h-24 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                <x-heroicon-o-chart-bar class="w-12 h-12 text-gray-400" />
            </div>
            <div class="text-center space-y-2">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Select Analytics Widget
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 max-w-md">
                    Choose one of the analytics widgets from the floating buttons below to view detailed insights and data visualizations.
                </p>
            </div>
        </div>
    @else
        <div class="space-y-6 pb-20">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    @switch($this->activeWidget)
                        @case('revenue')
                            <x-heroicon-o-currency-dollar class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Revenue Analytics</h2>
                            @break
                        @case('cashflow')
                            <x-heroicon-o-banknotes class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Cash Flow Analysis</h2>
                            @break
                        @case('trends')
                            <x-heroicon-o-chart-bar class="w-6 h-6 text-primary-600" />
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Trends Analysis</h2>
                            @break
                    @endswitch
                </div>
                <button
                    wire:click="showWidget('')"
                    class="inline-flex items-center px-3 py-1.5 text-sm text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-200 transition-colors duration-200"
                >
                    <x-heroicon-o-x-mark class="w-4 h-4 mr-1" />
                    Close
                </button>
            </div>

            <!-- Widget Container -->
            <div class="widget-container">
                @foreach ($this->getWidgets() as $widget)
                    @livewire($widget, [], key($widget))
                @endforeach
            </div>
        </div>
    @endif

    <!-- Floating Bottom Widget Selection -->
    <div class="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 animate-fade-in">
        <div class="bg-white dark:bg-gray-800 rounded-full shadow-xl border border-gray-200 dark:border-gray-700 p-2 backdrop-blur-sm bg-opacity-95 dark:bg-opacity-95">
            <div class="flex items-center space-x-2">
                <!-- Revenue Button -->
                <x-filament::button
                    wire:click="showWidget('revenue')"
                    class="floating-button relative group p-3 rounded-full {{ $this->activeWidget === 'revenue' ? 'active bg-primary-600 text-white shadow-lg' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                    tooltip="Revenue Analytics"
                    color="{{ $this->activeWidget === 'revenue' ? 'primary' : 'gray' }}"
                    size="xs"
                    :outlined="$this->activeWidget !== 'revenue'"
                >
                    <x-heroicon-o-currency-dollar class="w-5 h-5" />
                    @if($this->activeWidget === 'revenue')
                        <div class="absolute -top-2 -right-2 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                    @endif
                </x-filament::button>

                <!-- Cash Flow Button -->
                <x-filament::button
                    wire:click="showWidget('cashflow')"
                    class="floating-button relative group p-3 rounded-full {{ $this->activeWidget === 'cashflow' ? 'active bg-primary-600 text-white shadow-lg' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                    tooltip="Cash Flow Analysis"
                    color="{{ $this->activeWidget === 'cashflow' ? 'primary' : 'gray' }}"
                    size="xs"
                    :outlined="$this->activeWidget !== 'cashflow'"
                >
                    <x-heroicon-o-banknotes class="w-5 h-5" />
                    @if($this->activeWidget === 'cashflow')
                        <div class="absolute -top-2 -right-2 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                    @endif
                </x-filament::button>

                <!-- Trends Button -->
                <x-filament::button
                    wire:click="showWidget('trends')"
                    class="floating-button relative group p-3 rounded-full {{ $this->activeWidget === 'trends' ? 'active bg-primary-600 text-white shadow-lg' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                    tooltip="Trends Analysis"
                    color="{{ $this->activeWidget === 'trends' ? 'primary' : 'gray' }}"
                    size="xs"
                    :outlined="$this->activeWidget !== 'trends'"
                >
                    <x-heroicon-o-chart-bar class="w-5 h-5" />
                    @if($this->activeWidget === 'trends')
                        <div class="absolute -top-2 -right-2 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
                    @endif
                </x-filament::button>
            </div>
        </div>
    </div>
</x-filament-panels::page>
