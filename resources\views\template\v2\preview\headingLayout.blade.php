@if ($headingStyle === 'grid')
	<div class="flex flex-row {{ $verticalAlign[$headingVerticalAlign] ?? 'items-center' }} space-x-4 gap-2">
		@foreach($flexOrder as $item)
			@if($item === 'logo' && $headingShowLogo)
				<div class="w-32 h-32 p-1 box-border border border-gray-300 rounded overflow-hidden flex items-center justify-center gap-2">
					<img src="{{ asset('storage/'. $companyLogo) }}" alt="Company Logo" class="w-32 h-auto object-contain">
				</div>
			@elseif($item === 'company-info')
				<div class="{{ $horizontalAlign[$headingHorizontalAlign] ?? 'text-start' }} flex-grow">
					<span class="{{ $sizeCompanyClasses[$headingSize] ?? 'text-base' }} mt-2 {{ $headingWeight }}" style="color: {{ $headingColor }};">
						{{ $companyName }}
					</span>

					@if($headingShowAddress)
						@if ($headingAddressPosition !== 'far')
							<ul class="text-sm text-gray-700 leading-snug mt-1 space-y-0">
								<li>{{ strip_tags($companyAddress) }}. {{ $companyPhone }} @if ($companyEmail) - {{ $companyEmail }} @endif </li>
							</ul>
						@endif
					@endif
				</div>
				@if ($headingShowAddress && $headingAddressPosition === 'far')
					<div class="text-end text-wrap break-words w-full max-w-md">
						<ul class="text-xs text-gray-700 leading-snug mt-1 space-y-0">
							<li>{{ strip_tags($companyAddress) }}</li>
							<li>{{ $companyPhone }}</li>
							<li>{{ $companyEmail }}</li>
						</ul>
					</div>

				@endif
				@if ($headingShowInvoiceNumber && $headingInvoiceTitlePosition === 'right')
					<div>
						<table class="text-end">
							<tr class="text-end">
								<td>
									<span class="{{ $sizeInvoiceClasses[$headingInvoiceTitleSize] ?? 'text-base' }} {{ $headingInvoiceTitleWeight }}" style="text-transform: uppercase;"">
										Invoice
									</span>
								</td>
							</tr>
							@if (!$headingInvoiceTitleOnly)
								<tr class="text-end">
									<td>
										<span class="text-end {{ $headingInvoiceNumberWeight }}" style="">
											INV-MHU-10062025
										</span>
									</td>
								</tr>
							@endif
						</table>
					</div>
				@endif
			@endif
		@endforeach
	</div>
@elseif ($headingStyle === 'stacked')
	<div class="flex flex-col items-center text-center space-y-2">
		@if($headingShowLogo)
			<img src="{{ asset('images/logolight.png') }}" alt="Company Logo" class="w-32 h-auto object-contain">
		@endif
		<span class="{{ $sizeClasses[$headingSize] ?? 'text-base' }} mt-2 {{ $headingWeight }}"  style="color: {{ $headingColor }};">
			{{ $companyName }}
		</span>

		@if($headingShowAddress)
			<ul class="text-xs text-gray-700 space-y-0 leading-snug">
				<li>{{ strip_tags($companyAddress) }}. {{ $companyPhone }} @if ($companyEmail) - {{ $companyEmail }}@endif</li>
			</ul>
		@endif
	</div>
@endif
@if ($headingSingleLineSpacer)
	<div class="mb-6">
		<div style="width: 100%; border-top: 3px solid #555; margin-top: 10px;"></div>
		@if ($headingDoubleLineSpacer)
		<div style="width: 100%; border-top: 2px solid #555; margin-top: 3px;"></div>
		@endif
	</div>
@endif

@if ($headingShowInvoiceNumber && $headingInvoiceTitlePosition === 'first')
	<div class="text-center mt-6">
		<div class="underline {{ $sizeInvoiceClasses[$headingInvoiceTitleSize] ?? 'text-base' }} {{ $headingInvoiceTitleWeight }}" style="text-transform: uppercase;">
			<span>Invoice</span>
		</div>
		@if (!$headingInvoiceTitleOnly)
			<div class=" {{ $headingInvoiceNumberWeight }}">
				INV-MHU-10062025
			</div>
		@endif
	</div>
@endif

