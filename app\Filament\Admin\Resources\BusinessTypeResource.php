<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\BusinessTypeResource\Pages;
use App\Models\BusinessType;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\{Repeater, RichEditor, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BusinessTypeResource extends Resource
{
    protected static ?string $model = BusinessType::class;
	protected static ?string $navigationGroup = 'Masters Data';

    protected static ?string $navigationIcon = 'heroicon-o-briefcase';
	protected static ?string $activeNavigationIcon = 'heroicon-s-briefcase';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->maxLength(191)
					->required(),
				TableRepeater::make('invoiceDetails')
					->label('Invoice Details')
					->addable()
					->deletable()
					->reactive()
					->relationship('details')
					->addActionLabel('Add new detail')
					->headers([
						Header::make('Description')->width('70%'),
						Header::make('Quantity')->width('15%'),
						Header::make('Price')->width('15%'),
					])
					->schema([
						RichEditor::make('description')
							->extraInputAttributes([
								'style' => 'resize: vertical;min-height: 2rem; max-height: auto; overflow-y: auto;'
								])
							->toolbarButtons(['bulletList', 'orderedlist'])
							->columnSpanFull(),
						TextInput::make('quantity')->numeric()->live(onBlur:true)
							->extraInputAttributes(['class'=>'text-end']),
						TextInput::make('price')->numeric()->live(onBlur:true)
							->extraInputAttributes(['class'=>'text-end']),
					])
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBusinessTypes::route('/'),
            // 'create' => Pages\CreateBusinessType::route('/create'),
            // 'view' => Pages\ViewBusinessType::route('/{record}'),
            // 'edit' => Pages\EditBusinessType::route('/{record}/edit'),
        ];
    }
}
