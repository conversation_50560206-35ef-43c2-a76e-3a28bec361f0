<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mc_orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_code');
            $table->bigInteger('mc_customer_id')->nullable();
            $table->integer('currency_id')->nullable();
            $table->integer('bank_group_id')->nullable();
            $table->integer('bank_id')->nullable();
			$table->decimal('amount', 18, 2)->nullable();
			$table->decimal('buy_rates', 11, 2)->nullable();
			$table->decimal('sell_rates', 11, 2)->nullable();
			$table->decimal('charges', 11, 2)->nullable();
			$table->decimal('total_order', 11, 2)->nullable();
			$table->boolean('is_cancelled')->nullable();
			$table->integer('status')->nullable();
            $table->timestamps();
			$table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mc_orders');
    }
};
