# PDFShift API Key Setup

## Problem: API Key Not Found

The current API key `sk_5c461d1273623eb7110803e89530d0994ab76894` is returning "The provided API Key was not found" error.

## Solutions

### Option 1: Get New API Key

1. **Visit PDFShift Dashboard**: https://app.pdfshift.io/
2. **Sign up/Login** to your account
3. **Generate new API key** from dashboard
4. **Add to .env file**:
   ```env
   PDFSHIFT_API_KEY=your_new_api_key_here
   ```

### Option 2: Use Test Mode (Recommended for Development)

Add this to your `.env` file to simulate successful PDF generation:

```env
PDFSHIFT_TEST_MODE=true
```

When test mode is enabled:
- ✅ No actual API calls are made
- ✅ Returns mock PDF content
- ✅ Logs success simulation
- ✅ Perfect for development/testing

### Option 3: Try Free Trial

PDFShift offers free trial with limited conversions:
1. Create new account at https://pdfshift.io/
2. Get free API key
3. Update `.env` with new key

## Testing the Implementation

### Test with Mock Mode
```bash
# Add to .env
PDFSHIFT_TEST_MODE=true

# Test URL
http://localhost/{record_id}/printInvoicePdfShift
```

### Test with Real API
```bash
# Add to .env
PDFSHIFT_API_KEY=sk_your_real_api_key
PDFSHIFT_TEST_MODE=false

# Test URL  
http://localhost/{record_id}/printInvoicePdfShift
```

## Current Implementation Features

✅ **Environment Configuration**: Uses `.env` for API key
✅ **Test Mode Support**: Mock PDF generation for development
✅ **Detailed Logging**: API key validation and request tracking
✅ **Error Handling**: Graceful fallback with informative error pages
✅ **Security**: API key prefix logging (not full key)

## Log Examples

### Test Mode Success
```json
{
  "message": "PDFShift Test Mode - Simulating Success",
  "record_id": "2"
}
```

### API Key Error
```json
{
  "message": "PDFShift Test API Error",
  "status": 401,
  "error": "The provided API Key was not found."
}
```

## Next Steps

1. **For Development**: Enable `PDFSHIFT_TEST_MODE=true`
2. **For Production**: Get valid API key from PDFShift
3. **Test Both Modes**: Verify functionality works as expected

## API Key Validation

The implementation now includes:
- Environment variable support
- API key prefix logging for security
- Test mode for development
- Detailed error reporting
