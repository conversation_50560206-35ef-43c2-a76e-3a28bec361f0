<?php

namespace App\Filament\Mc\Resources;

use App\Filament\Mc\Resources\McCustomerResource\Pages;
use App\Filament\Mc\Resources\McCustomerResource\RelationManagers;
use App\Models\McAgent;
use App\Models\McCustomer;
use App\Models\McDeposit;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class McCustomerResource extends Resource
{
    protected static ?string $model = McCustomer::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	protected static ?string $recordTitleAttribute = 'name';
	protected static ?string $title = 'Customers';
	protected static bool $shouldRegisterNavigation = false;

	public static function getNavigationLabel(): string
	{
		return 'Customers';
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('agent_id')
					->label('Agent')
					->searchable()
					->options(McAgent::query()->get()->pluck('name', 'id')),
				TextInput::make('name')
					->required()
					->label('Customer Name'),
				TextInput::make('phone')
					->label('Customer Phone')
					->tel(),
				Textarea::make('note')
					->label('Customer Note'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // TextColumn::make('agent.name')->sortable()->searchable(),
                TextColumn::make('customer_code')->sortable()->searchable(),
                TextColumn::make('name')->sortable()->searchable(),
                TextColumn::make('balance')
					->sortable()
					->width('20%')
					->color(fn($state) => $state >= 0 ? 'success' : 'danger')
					->money('IDR')
					->alignEnd()
					->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
				Action::make('view_details')
					->label('Order Details')
					->icon('heroicon-o-eye')
					->iconButton()
					->tooltip('View Bank Details')
					->modalWidth('4xl')
					->modalSubmitAction(false)
					->modalCancelActionLabel('Close')
					->modalFooterActionsAlignment('right')
					->modalHeading('Customer Balance Standing')
					// ->modalContent(fn ($record): View => view(
					// 	'components.global-mcbalance-modalWidget',
					// 	[
					// 		'record' => $record,
					// 		'amount' => number_format($record->amount ?? 0, 2, ',', '.'),
					// 		'charges' => number_format($record->charges ?? 0, 2, ',', '.'),
					// 		'rates' => number_format($record->sell_rates ?? 0, 2, ',', '.'),
					// 		'totalOrder' => number_format($record->total_order ?? 0, 2, ',', '.'),
					// 		'orderDate' => Carbon::parse($record->order_date)->format('d F Y'),
					// 	]
					// ))
					->modalContent(function ($record) {
						$order = McDeposit::where('mc_customer_id', $record->id)
							->select('id', 'mc_customer_id', 'slip_date', 'amount')
							->orderBy('slip_date', 'desc')
							->where('trx_type', 'order')
							->first();
						$payment = McDeposit::where('mc_customer_id', $record->id)
							->select('id', 'mc_customer_id', 'slip_date', 'amount')
							->orderBy('slip_date', 'desc')
							->where('trx_type', 'incoming')
							->first();

						$transactions = McDeposit::where('mc_customer_id', $record->id)
							->select('id', 'mc_customer_id', 'slip_date', 'amount', 'trx_type')
							->orderBy('slip_date', 'desc')
							->take(7)
							->get();

						$lastOrderAt = $order?->slip_date
							? Carbon::parse($order->slip_date)->format('d F Y')
							: 'No Data';

						$lastPaymentAt = $payment?->slip_date
							? Carbon::parse($payment->slip_date)->format('d F Y')
							: 'No Data';

						$lastOrder = number_format($order->amount ?? 0, 2, ',', '.');
						$lastPayment = number_format($payment->amount ?? 0, 2, ',', '.');
						$totalOrder = number_format($record->total_orders ?? 0, 2, ',', '.');
						$totalPayment = number_format($record->total_deposits ?? 0, 2, ',', '.');
						$balance = number_format($record->balance ?? 0, 2, ',', '.');

						return view('components.global-mcbalance-modalWidget', compact('record','order', 'payment', 'lastOrderAt', 'lastPaymentAt', 'lastOrder', 'lastPayment', 'totalOrder', 'totalPayment', 'balance', 'transactions'));
					}),
                EditAction::make()->iconButton()->modalHeading('Edit Customer'),
                ViewAction::make()->iconButton()->modalHeading('Customer Data'),
				Action::make('Recalculate Balance')
					->icon('heroicon-m-arrow-path')
						->iconButton()
					->color('primary')
					->tooltip('Recalculate Customer Balance')
					->requiresConfirmation()
					->action(function ($record) {
						// Jalankan service
						\App\Services\RecalculateCustomerBalanceService::run($record->id);

						Notification::make()
							->title('Balance recalculate successfully.')
							->success()
							->send();
					}),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
					BulkAction::make('Recalculate Balances')
						->icon('heroicon-m-calculator')
						->action(function (Collection $records) {
							$customerIds = $records->pluck('id')->toArray();
							\App\Services\RecalculateCustomerBalanceService::run($customerIds);

							Notification::make()
								->title('All customer balances recalculate successfully.')
								->success()
								->send();
						})
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMcCustomers::route('/'),
            // 'create' => Pages\CreateMcCustomer::route('/create'),
            // 'edit' => Pages\EditMcCustomer::route('/{record}/edit'),
        ];
    }
}
