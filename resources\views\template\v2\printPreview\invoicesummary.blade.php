@php
    // Extract configuration
    $invoiceTemplate = $payload['invoice']->invoicetemplate;
    $layoutConfig = $invoiceTemplate?->layout_config ?? [];
    $invoice = $payload['invoice'];
    $currency = $payload['invoice']->currency;
    $company = $payload['invoice']->company;
    $details = $payload['invoice']->invoiceDetails;

    // Configuration options
    $tableShowSummaryDetails = $layoutConfig['tableShowSummaryDetails'] ?? false;
    $tableShowInword = $layoutConfig['tableShowInword'] ?? false;
    $isIndonesianCompany = $company->type == 2;
    $currencySymbol = $currency->symbol;

    // Calculate totals
    $subTotal = $details->sum('sub_total');
    $totalItems = $details->count();
    $totalQuantity = $details->sum('quantity');
@endphp

<div class="w-full">
    <h4 class="text-sm font-medium mb-2 px-2">Summary</h4>
    <table class="w-full table-auto">
        <tbody>
            @if ($tableShowSummaryDetails)
                <tr>
                    <td class="px-2 py-1">Total Items:</td>
                    <td class="px-2 py-1 text-end">{{ $totalItems }}</td>
                </tr>
                <tr>
                    <td class="px-2 py-1">Total Quantity:</td>
                    <td class="px-2 py-1 text-end">{{ number_format($totalQuantity, 2, '.', ',') }}</td>
                </tr>
            @endif
            <tr>
                <td class="px-2 py-1">Subtotal:</td>
                <td class="px-2 py-1 text-end">{{ $currencySymbol }} {{ number_format($subTotal, 2, '.', ',') }}</td>
            </tr>
            @if ($invoice->booking_fee > 0)
                <tr>
                    <td class="px-2 py-1">Booking Fee:</td>
                    <td class="px-2 py-1 text-end">{{ $currencySymbol }}
                        {{ number_format($invoice->booking_fee, 2, '.', ',') }}</td>
                </tr>
            @endif
            @if ($isIndonesianCompany && $invoice->rates > 0)
                <tr>
                    <td class="px-2 py-1">Rates:</td>
                    <td class="px-2 py-1 text-end">Rp {{ number_format($invoice->rates, 2, '.', ',') }}</td>
                </tr>
            @endif
            <tr class="font-bold">
                <td class="px-2 py-1 pt-3 border-t">Invoice Total:</td>
                <td class="px-2 py-1 pt-3 text-end border-t" width="50%">
                    {{ $isIndonesianCompany ? 'Rp' : $currencySymbol }}
                    {{ number_format($invoice->invoice_amount, 2, '.', ',') }}
                </td>
            </tr>
            @if ($tableShowInword && $invoice->amount_inword)
                <tr>
                    <td class="px-2 py-1 pt-3" colspan="2">
						<div class="w-full flex flex-wrap">
							<span
								{{--  --}}
								class="whitespace-nowrap">{{ $company->type == 1 ? 'Amount in words' : 'Terbilang' }}:
							</span>
							<span class="font-bold">{{ $invoice->amount_inword }}</span>
						</div>
                    </td>
                </tr>
            @endif
        </tbody>
    </table>
</div>
