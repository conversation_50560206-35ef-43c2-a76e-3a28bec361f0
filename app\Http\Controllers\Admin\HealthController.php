<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Spatie\Health\Commands\RunHealthChecksCommand;
use Spatie\Health\ResultStores\ResultStore;

class HealthController extends Controller
{
    /**
     * Display the health dashboard.
     */
    public function index(ResultStore $resultStore)
    {
        try {
            // Run health checks
            Artisan::call(RunHealthChecksCommand::class);

            // Get the latest check results
            $checkResults = $resultStore->latestResults();

            // Ensure all status values are properly formatted
            if ($checkResults && property_exists($checkResults, 'storedCheckResults')) {
                foreach ($checkResults->storedCheckResults as $result) {
                    if (!is_object($result->status) && !is_string($result->status)) {
                        $result->status = 'unknown';
                    }
                }
            }

            return view('admin.health.index', [
                'checkResults' => $checkResults,
                'error' => null,
            ]);
        } catch (\Exception $e) {
            return view('admin.health.index', [
                'checkResults' => null,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Run health checks and return the results.
     */
    public function runChecks(ResultStore $resultStore)
    {
        try {
            // Run health checks
            Artisan::call(RunHealthChecksCommand::class);

            // Get the latest check results
            $checkResults = $resultStore->latestResults();

            // Ensure all status values are properly formatted
            if ($checkResults && property_exists($checkResults, 'storedCheckResults')) {
                foreach ($checkResults->storedCheckResults as $result) {
                    if (!is_object($result->status) && !is_string($result->status)) {
                        $result->status = 'unknown';
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Health checks completed successfully.',
                'results' => $checkResults,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to run health checks: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get the current health check settings.
     */
    public function getSettings()
    {
        $settings = config('health-check-settings.enabled_checks', []);

        return response()->json([
            'success' => true,
            'settings' => $settings,
        ]);
    }

    /**
     * Toggle a health check setting.
     */
    public function toggleSetting(Request $request)
    {
        $checkName = $request->input('check_name');

        if (!$checkName) {
            return response()->json([
                'success' => false,
                'message' => 'Check name is required.',
            ], 400);
        }

        try {
            // Get current settings
            $configPath = config_path('health-check-settings.php');
            $settings = config('health-check-settings.enabled_checks', []);

            // Toggle the setting
            if (isset($settings[$checkName])) {
                $settings[$checkName] = !$settings[$checkName];

                // Update the config file
                $content = "<?php\n\nreturn [\n    /*\n     * Enable or disable specific health checks.\n     * These settings can be modified in the admin interface.\n     */\n    'enabled_checks' => [\n";

                foreach ($settings as $name => $enabled) {
                    $enabledStr = $enabled ? 'true' : 'false';
                    $content .= "        '$name' => $enabledStr,\n";
                }

                $content .= "    ],\n];";

                File::put($configPath, $content);

                // Clear config cache if it exists
                if (file_exists(base_path('bootstrap/cache/config.php'))) {
                    Artisan::call('config:clear');
                }

                return response()->json([
                    'success' => true,
                    'message' => "Health check '$checkName' has been " . ($settings[$checkName] ? 'enabled' : 'disabled') . ".",
                    'settings' => $settings,
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => "Health check '$checkName' not found.",
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle health check setting: ' . $e->getMessage(),
            ], 500);
        }
    }
}
