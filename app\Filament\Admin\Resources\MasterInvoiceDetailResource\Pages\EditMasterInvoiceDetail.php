<?php

namespace App\Filament\Admin\Resources\MasterInvoiceDetailResource\Pages;

use App\Filament\Admin\Resources\MasterInvoiceDetailResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMasterInvoiceDetail extends EditRecord
{
    protected static string $resource = MasterInvoiceDetailResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
