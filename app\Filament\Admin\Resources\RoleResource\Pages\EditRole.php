<?php

namespace App\Filament\Admin\Resources\RoleResource\Pages;

use App\Filament\Admin\Resources\RoleResource;
use Althinect\FilamentSpatieRolesPermissions\Resources\RoleResource\Pages\EditRole as BaseEditRole;
use Filament\Actions;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class EditRole extends BaseEditRole
{
    protected static string $resource = RoleResource::class;

    public function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Mulai transaksi database untuk memastikan semua operasi berhasil atau gagal bersama
        DB::beginTransaction();

        try {
            // Simpan data dasar role
            $record->update([
                'name' => $data['name'],
                'guard_name' => $data['guard_name'] ?? config('filament-spatie-roles-permissions.default_guard_name', 'web'),
            ]);

            // Ambil semua permission yang dipilih dari form
            $selectedPermissions = collect();

            // Kumpulkan semua permission yang dipilih dari berbagai checkbox list
            foreach ($data as $key => $value) {
                if ((str_starts_with($key, 'permissions_web_') || str_starts_with($key, 'permissions_api_')) && is_array($value)) {
                    $selectedPermissions = $selectedPermissions->merge($value);
                }
            }

            // Jika ada permission yang dipilih langsung (tidak melalui grup)
            if (isset($data['permissions']) && is_array($data['permissions'])) {
                $selectedPermissions = $selectedPermissions->merge($data['permissions']);
            }

            // Sync permission dengan role
            if ($selectedPermissions->isNotEmpty()) {
                $record->syncPermissions($selectedPermissions->unique()->toArray());
            }

            DB::commit();

            Notification::make()
                ->title('Role berhasil diperbarui')
                ->success()
                ->send();

            return $record;
        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->title('Gagal memperbarui role')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();

            throw $e;
        }
    }
}
