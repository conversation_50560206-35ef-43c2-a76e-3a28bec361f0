<x-admin-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Health Dashboard') }}
            </h2>
            <button id="refresh-health" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {{ __('Refresh') }}
            </button>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div id="health-results">
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900">{{ __('System Health Status') }}</h3>
                            <p class="mt-1 text-sm text-gray-500">{{ __('Overview of your application\'s health status.') }}</p>
                            <p class="mt-1 text-sm text-gray-500">{{ __('Current Environment:') }} <span class="font-semibold">{{ env('APP_ENV') }}</span> | {{ __('Debug Mode:') }} <span class="font-semibold">{{ env('APP_DEBUG') ? 'On' : 'Off' }}</span></p>
                        </div>

                        <!-- Health Check Settings -->
                        <div class="bg-white p-4 rounded-lg shadow border mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ __('Health Check Settings') }}</h3>
                            <p class="text-sm text-gray-500 mb-4">{{ __('Enable or disable specific health checks based on your environment needs.') }}</p>

                            <div id="health-check-toggles" class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                <div class="flex items-center justify-between p-2 border rounded">
                                    <span class="text-sm font-medium text-gray-700">{{ __('Loading settings...') }}</span>
                                </div>
                            </div>
                        </div>

                        @if($error)
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                                <strong class="font-bold">{{ __('Error!') }}</strong>
                                <span class="block sm:inline">{{ $error }}</span>
                                <p class="mt-2">{{ __('Please make sure you have run the migrations for Laravel Health:') }}</p>
                                <pre class="mt-2 bg-gray-800 text-white p-2 rounded">php artisan vendor:publish --provider="Spatie\Health\HealthServiceProvider" --tag="migrations"<br>php artisan migrate</pre>
                            </div>
                        @elseif($checkResults)
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                                <!-- Overall Status Card -->
                                <div class="bg-white p-4 rounded-lg shadow border">
                                    <div class="flex items-center">
                                        <div class="p-3 rounded-full {{ $checkResults->allChecksOk() ? 'bg-green-100 text-green-500' : 'bg-red-100 text-red-500' }} mr-4">
                                            @if($checkResults->allChecksOk())
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            @else
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                            @endif
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500 font-medium">{{ __('Overall Status') }}</p>
                                            <p class="text-xl font-bold {{ $checkResults->allChecksOk() ? 'text-green-500' : 'text-red-500' }}">
                                                {{ $checkResults->allChecksOk() ? __('Healthy') : __('Unhealthy') }}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Checks Summary Card -->
                                <div class="bg-white p-4 rounded-lg shadow border">
                                    <div class="flex items-center">
                                        <div class="p-3 rounded-full bg-blue-100 text-blue-500 mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500 font-medium">{{ __('Total Checks') }}</p>
                                            <p class="text-xl font-bold">{{ count($checkResults->storedCheckResults) }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Last Run Card -->
                                <div class="bg-white p-4 rounded-lg shadow border">
                                    <div class="flex items-center">
                                        <div class="p-3 rounded-full bg-purple-100 text-purple-500 mr-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500 font-medium">{{ __('Last Run') }}</p>
                                            <p class="text-xl font-bold">{{ $checkResults->finishedAt?->diffForHumans() ?? __('Never') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Health Checks Table -->
                            <div class="bg-white shadow overflow-hidden sm:rounded-md border">
                                <ul class="divide-y divide-gray-200">
                                    @foreach($checkResults->storedCheckResults as $result)
                                        <li>
                                            <div class="px-4 py-4 sm:px-6">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center">
                                                        @if((is_object($result->status) && $result->status === \Spatie\Health\Enums\Status::ok()) || (is_string($result->status) && $result->status === 'ok'))
                                                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                                                                <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                                </svg>
                                                            </div>
                                                        @elseif((is_object($result->status) && $result->status === \Spatie\Health\Enums\Status::warning()) || (is_string($result->status) && $result->status === 'warning'))
                                                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                                                                <svg class="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                                </svg>
                                                            </div>
                                                        @elseif((is_object($result->status) && ($result->status === \Spatie\Health\Enums\Status::failed() || $result->status === \Spatie\Health\Enums\Status::crashed())) || (is_string($result->status) && ($result->status === 'failed' || $result->status === 'crashed')))
                                                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                                                                <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                                </svg>
                                                            </div>
                                                        @elseif((is_object($result->status) && $result->status === \Spatie\Health\Enums\Status::skipped()) || (is_string($result->status) && $result->status === 'skipped'))
                                                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                                                                <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                                </svg>
                                                            </div>
                                                        @else
                                                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                                                                <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                                </svg>
                                                            </div>
                                                        @endif
                                                        <div class="ml-4">
                                                            <div class="text-sm font-medium text-gray-900">{{ $result->label }}</div>
                                                            <div class="text-sm text-gray-500">
                                                                @if($result->notificationMessage)
                                                                    {{ $result->notificationMessage }}
                                                                @else
                                                                    {{ $result->shortSummary }}
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="ml-2 flex-shrink-0 flex">
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                                            @if(is_object($result->status) && $result->status === \Spatie\Health\Enums\Status::ok()) bg-green-100 text-green-800
                                                            @elseif(is_object($result->status) && $result->status === \Spatie\Health\Enums\Status::warning()) bg-yellow-100 text-yellow-800
                                                            @elseif(is_object($result->status) && ($result->status === \Spatie\Health\Enums\Status::failed() || $result->status === \Spatie\Health\Enums\Status::crashed())) bg-red-100 text-red-800
                                                            @elseif(is_object($result->status) && $result->status === \Spatie\Health\Enums\Status::skipped()) bg-gray-100 text-gray-800
                                                            @elseif(is_string($result->status) && $result->status === 'ok') bg-green-100 text-green-800
                                                            @elseif(is_string($result->status) && $result->status === 'warning') bg-yellow-100 text-yellow-800
                                                            @elseif(is_string($result->status) && ($result->status === 'failed' || $result->status === 'crashed')) bg-red-100 text-red-800
                                                            @elseif(is_string($result->status) && $result->status === 'skipped') bg-gray-100 text-gray-800
                                                            @else bg-gray-100 text-gray-800
                                                            @endif">
                                                            @if(is_object($result->status))
                                                                {{ $result->status->value }}
                                                            @else
                                                                {{ $result->status }}
                                                            @endif
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @else
                            <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative mb-6" role="alert">
                                <strong class="font-bold">{{ __('No health check results!') }}</strong>
                                <span class="block sm:inline">{{ __('No health check results are available. Please run the health checks.') }}</span>
                                <div class="mt-4">
                                    <button id="refresh-health" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        {{ __('Run Health Checks') }}
                                    </button>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const refreshButton = document.getElementById('refresh-health');
            const resultsContainer = document.getElementById('health-results');

            // Handle refresh button
            if (refreshButton) {
                refreshButton.addEventListener('click', function() {
                    refreshButton.disabled = true;
                    refreshButton.innerHTML = `
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Refreshing...
                    `;

                    fetch('/management/health/run-checks', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('Failed to run health checks. Please try again.');
                            refreshButton.disabled = false;
                            refreshButton.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Refresh
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                        refreshButton.disabled = false;
                        refreshButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Refresh
                        `;
                    });
                });
            }

            // Load settings for toggle switches
            fetch('/management/health/settings')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const settings = data.settings;
                        const toggles = document.querySelectorAll('.toggle-check');
                        const togglesContainer = document.getElementById('health-check-toggles');

                        // Clear loading message
                        togglesContainer.innerHTML = '';

                        // Create toggle switches for all available checks
                        Object.keys(settings).forEach(checkName => {
                            const isEnabled = settings[checkName];

                            // Create toggle switch element
                            const toggleItem = document.createElement('div');
                            toggleItem.className = 'flex items-center justify-between p-2 border rounded';
                            toggleItem.innerHTML = `
                                <span class="text-sm font-medium text-gray-700">${checkName}</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="toggle-check sr-only peer" data-check-name="${checkName}" ${isEnabled ? 'checked' : ''}>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                </label>
                            `;

                            togglesContainer.appendChild(toggleItem);
                        });

                        // Add event listeners to the newly created toggle switches
                        document.querySelectorAll('.toggle-check').forEach(toggle => {
                            toggle.addEventListener('change', handleToggleChange);
                        });

                        // Update existing toggle switches in the results list
                        toggles.forEach(toggle => {
                            const checkName = toggle.dataset.checkName;
                            if (settings[checkName] !== undefined) {
                                toggle.checked = settings[checkName];
                            }
                        });
                    }
                })
                .catch(error => console.error('Error loading settings:', error));

            // Function to handle toggle change
            function handleToggleChange() {
                const checkName = this.dataset.checkName;

                fetch('/management/health/toggle-setting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        check_name: checkName
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert(data.message);

                        // Update all toggle switches with the same check name
                        const allToggles = document.querySelectorAll(`.toggle-check[data-check-name="${checkName}"]`);
                        allToggles.forEach(toggle => {
                            toggle.checked = !toggle.checked;
                        });

                        // Refresh the page to show updated checks
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        // Show error message
                        alert(data.message);

                        // Revert toggle state
                        this.checked = !this.checked;
                    }
                })
                .catch(error => {
                    console.error('Error toggling setting:', error);

                    // Revert toggle state
                    this.checked = !this.checked;

                    // Show error message
                    alert('Failed to toggle setting. Please try again.');
                });
            }

            // Add event listeners to existing toggle switches
            document.querySelectorAll('.toggle-check').forEach(toggle => {
                toggle.addEventListener('change', handleToggleChange);
            });
        });
    </script>
    @endpush
</x-admin-layout>
