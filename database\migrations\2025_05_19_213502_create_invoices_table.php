<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->unsignedBigInteger('company_id')->nullable(); //set when invoice is created
            $table->unsignedBigInteger('client_id')->nullable(); //set when invoice is edit
            $table->unsignedBigInteger('parent_invoice_id')->nullable(); //set when invoice is created
            $table->unsignedBigInteger('invoice_create_by')->nullable(); //Auth::user()->id
            $table->string('invoice_no')->nullable();
            $table->date('invoice_date')->nullable(); //default null on create, today on edit
            $table->date('due_date')->nullable(); //default null on create, today + 7 days on edit
            $table->integer('currency_id')->nullable()->default(null);
            $table->decimal('order_amount', 18, 2)->nullable();
            $table->decimal('booking_fee', 10, 2)->nullable();
            $table->decimal('invoice_amount', 18, 2)->nullable(); //updated when invoice details is updated
            $table->string('status')->nullable(); //Draft, Issued, Closed
			$table->decimal('inv_sub_total', 18, 2)->nullable();
			$table->decimal('rates', 10, 2)->nullable();
			$table->text('amount_inword')->nullable();
			$table->text('client_address')->nullable();
			$table->string('bank_acc_name')->nullable();
			$table->string('bank_code')->nullable();
			$table->string('bank_acc_no')->nullable();
			$table->text('bank_acc_address')->nullable();
			$table->string('bank_name')->nullable();
			$table->text('bank_address')->nullable();
			$table->string('bank_correspondent')->nullable();
			$table->string('swift')->nullable();
			$table->string('swift_correspondent')->nullable();
			$table->text('remarks')->nullable();
			$table->string('routing_no')->nullable();
			$table->string('transit')->nullable();
			$table->string('tt_charge')->nullable();
			$table->string('iban')->nullable();
			$table->string('institution')->nullable();
			$table->string('bsb')->nullable();
			$table->string('branch_code')->nullable();
			$table->string('sort_code')->nullable();
			$table->string('branch_bank')->nullable();
			$table->boolean('back2back')->nullable();
			$table->string('ABA')->nullable();
			$table->string('IFSC')->nullable();
			$table->integer('verified_by')->nullable();
			$table->integer('supervised_by')->nullable();
			$table->integer('approved_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
