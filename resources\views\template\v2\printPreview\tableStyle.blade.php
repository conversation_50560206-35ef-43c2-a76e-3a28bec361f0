@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$invoice = $payload['invoice'];
	$currency = $payload['invoice']->currency;
	$company = $payload['invoice']->company;
	$details = $payload['invoice']->invoiceDetails;

	// Table configuration
	$tableStyle = $layoutConfig['tableStyle'] ?? 'bordered';
	$tableHeaderShading = $layoutConfig['tableHeaderShading'] ?? '#f3f4f6';
	$tableBodyBorderStyle = $layoutConfig['tableBodyBorderStyle'] ?? 'full';
	$tableRowShading = $layoutConfig['tableRowShading'] ?? 'none';
	$tableRowColor = $layoutConfig['tableRowColor'] ?? '#f9fafb';
	$tableColumnShading = $layoutConfig['tableColumnShading'] ?? 'none';
	$tableLastColumnColor = $layoutConfig['tableLastColumnColor'] ?? '#f3f4f6';
	$tableSummaryBg = $layoutConfig['tableSummaryBg'] ?? 'transparent';
	$tableSummaryOffGrid = $layoutConfig['tableSummaryOffGrid'] ?? 'ongrid';
	$tableShowSummaryDetails = $layoutConfig['tableShowSummaryDetails'] ?? false;
	$tableShowInword = $layoutConfig['tableShowInword'] ?? false;
	$footerBorderStyle = $layoutConfig['footerBorderStyle'] ?? 'full';

	// Column configuration (following preview pattern)
	$tableVisibleColumns = $layoutConfig['tableVisibleColumns'] ?? ['item', 'description', 'qty', 'price', 'total'];

	$columns = collect([
		['key' => 'item', 'label' => 'Item'],
		['key' => 'description', 'label' => 'Description'],
		['key' => 'qty', 'label' => 'Qty'],
		['key' => 'price', 'label' => 'Price'],
		['key' => 'total', 'label' => 'Total']
	])->map(function ($col) use ($tableVisibleColumns) {
		$col['show'] = in_array($col['key'], $tableVisibleColumns) || $col['key'] === 'total';
		return $col;
	});

	$visibleColumnsFiltered = $columns->filter(fn($c) => $c['show'])->values();
	$visibleCount = $visibleColumnsFiltered->count();
	$lastVisibleIndex = $visibleCount - 1;
	$colspan = $visibleCount - 1;

	// Convert invoice details to rows format
	$rows = $details->map(function($item, $index) use ($currency) {
		return [
			'item' => $index + 1, // Item number (1, 2, 3, ...)
			'description' => $item->description,
			'qty' => number_format($item->quantity, 2, '.', ','),
			'price' => number_format($item->price, 2, '.', ','),
			'total' => $currency->symbol . ' ' . number_format($item->sub_total, 2, '.', ',')
		];
	})->toArray();

	// Helper functions
	if (!function_exists('getColumnWidthStyle')) {
		function getColumnWidthStyle($key) {
			return match ($key) {
				'item' => 'width: 5%; text-align: center; vertical-align: top;',
				'description' => 'width: 40%; text-align: left;',
				'price' => 'width: 10%; text-align: right;',
				'qty' => 'width: 15%; text-align: right;',
				'total' => 'width: 20%; text-align: right;',
				default => 'width: 15%; text-align: left;',
			};
		}
	}

	if (!function_exists('getBorderClasses')) {
		function getBorderClasses($style, $isTh = false) {
			return match ($style) {
				'none' => '',
				'row' => $isTh ? 'border-b border-gray-300' : 'border-b border-gray-300',
				'column' => 'border-s border-gray-300',
				'full' => 'border border-gray-300',
				default => '',
			};
		}
	}

	if (!function_exists('getBorderClassesWithPosition')) {
		function getBorderClassesWithPosition($style, $isFirst = false, $isLast = false, $isTh = false) {
			$classes = '';

			switch ($style) {
				case 'none':
					return '';
				case 'row':
					return $isTh ? 'border-b border-gray-300' : 'border-b border-gray-300';
				case 'column':
					$classes = 'border-s border-gray-300';
					if ($isLast) {
						$classes .= ' border-e border-gray-300';
					}
					return $classes;
				case 'full':
					return 'border border-gray-300';
				default:
					return '';
			}
		}
	}

	// Text color classes (use parent isDarkColor function)
	$headerTextColorClass = isDarkColor($tableHeaderShading) ? 'text-white' : 'text-black';
	$tableTextColorClass = isDarkColor($tableSummaryBg) ? 'text-white' : 'text-black';

	// Calculate summary data
	$subTotal = $details->sum('sub_total');
	// Use the correct variables that were already calculated above
	// $visibleCount, $lastVisibleIndex, $colspan are already defined

	// Summary calculations
	$summaryMain = [
		'Sub Total' => $currency->symbol . ' ' . number_format($subTotal, 2, '.', ','),
	];

	if ($invoice->booking_fee > 0) {
		$summaryMain['Booking Fee'] = $currency->symbol . ' ' . number_format($invoice->booking_fee, 2, '.', ',');
	}

	if ($company->type == 2 && $invoice->rates > 0) {
		$summaryMain['Rates'] = 'Rp ' . number_format($invoice->rates, 2, '.', ',');
	}

	$summaryMain['Total'] = ($company->type == 2 ? 'Rp ' : $currency->symbol . ' ') . number_format($invoice->invoice_amount, 2, '.', ',');

	// Summary details (if enabled)
	$summaryDetails = [
		'Total Items' => $details->count(),
		'Total Quantity' => number_format($details->sum('quantity'), 2, '.', ','),
	];
@endphp

{{-- Invoice Table --}}
<div class="invoice-table-section">
	<table class="w-full invoice-table">
		<thead>
			<tr style="background-color: {{ $tableHeaderShading }};">
				@foreach ($visibleColumnsFiltered as $colIndex => $col)
					@php
						$isFirstCol = $colIndex === 0;
						$isLastCol = $colIndex === $lastVisibleIndex;
						$headerClass = getBorderClassesWithPosition($tableBodyBorderStyle, $isFirstCol, $isLastCol, true);
					@endphp
					<th class="px-2 {{ $headerClass }} {{ $headerTextColorClass }}"
						style="{{ getColumnWidthStyle($col['key']) }}">
						{{ $col['label'] }}
					</th>
				@endforeach
			</tr>
		</thead>
		<tbody>
			@foreach ($rows as $rowIndex => $row)
				@php
					$rowStyle = '';
					if ($tableRowShading === 'zebra' && $rowIndex % 2 === 1) {
						$rowStyle = "background-color: {$tableRowColor};";
					}
				@endphp

				<tr style="{{ $rowStyle }}" >
					@foreach ($visibleColumnsFiltered as $colIndex => $col)
						@php
							$cellStyle = getColumnWidthStyle($col['key']);
							$isFirstCol = $colIndex === 0;
							$isLastCol = $colIndex === $lastVisibleIndex;
							$cellClass = getBorderClassesWithPosition($tableBodyBorderStyle, $isFirstCol, $isLastCol);

							// Add border-t on first row
							if ($rowIndex === 0) {
								$cellClass .= ' border-t border-gray-300';
							}

							// Add border-b on last row
							if ($rowIndex === count($rows) - 1) {
								$cellClass .= ' border-b border-gray-300';
							}

							// Column shading (zebra)
							if ($tableColumnShading === 'zebra' && $colIndex % 2 === 1) {
								$cellClass .= ' bg-gray-100';
							}

							// Last column shading
							if ($tableColumnShading === 'last' && $colIndex === $lastVisibleIndex) {
								$cellStyle .= "background-color: {$tableLastColumnColor};";
								$cellClass .= isDarkColor($tableLastColumnColor) ? ' text-white' : ' text-black';
							}
						@endphp

						<td class="px-2 {{ $cellClass }}" style="{{ $cellStyle }} padding-top: 1rem; padding-bottom: 1rem;">
							@if($col['key'] === 'description')
								{!! $row[$col['key']] !!}
							@else
								{{ $row[$col['key']] }}
							@endif
						</td>
					@endforeach
				</tr>
			@endforeach
		</tbody>

		{{-- Table Footer (Summary) --}}
		@if ($tableSummaryOffGrid === 'ongrid')
			<tfoot>
				@php
					if (!function_exists('computeFooterClasses')) {
						function computeFooterClasses($loopLast, $style) {
							$baseLeft = getBorderClasses($style);
							$baseRight = getBorderClasses($style);

							if ($style === 'column') {
								$baseLeft .= ' border-s';
								$baseRight .= ' border-e';

								if ($loopLast) {
									$baseLeft .= ' border-b';
									$baseRight .= ' border-b';
								}
							}

							return [$baseLeft, $baseRight];
						}
					}
				@endphp

				{{-- Summary Details (if enabled) --}}
				@if ($tableShowSummaryDetails)
					@foreach ($summaryDetails as $label => $value)
						@php
							// For summaryDetails, never apply border-b since mainSummary follows
							[$footerClassLeft, $footerClassRight] = computeFooterClasses(false, $footerBorderStyle);
							$isHighlight = in_array($label, ['Total']);
						@endphp
						<tr style="">
							<td colspan="{{ $colspan }}" class="text-right px-2 py-1 {{ $isHighlight ? 'font-bold' : 'font-medium' }}">
								{{ $label }}:
							</td>
							<td class="text-right px-2 py-1 {{ $isHighlight ? 'font-bold' : 'font-medium' }} {{ $footerClassRight }}"
								style="
									background-color: {{ $tableColumnShading === 'last' ? $tableLastColumnColor : 'transparent' }};
									{{ isDarkColor($tableLastColumnColor) ? 'color:white;' : 'color:black;' }}
								">
								{{ $value }}
							</td>
						</tr>
					@endforeach
				@endif

				{{-- Main Summary --}}
				@foreach ($summaryMain as $label => $value)
					@php
						[$footerClassLeft, $footerClassRight] = computeFooterClasses($loop->last, $footerBorderStyle);
						$isHighlight = in_array($label, ['Total']);
					@endphp
					<tr style="">
						<td colspan="{{ $colspan }}" class="text-right px-2 py-1 {{ $isHighlight ? 'font-bold' : '' }}">
							{{ $label }}:
						</td>
						<td class="text-right px-2 py-1 {{ $isHighlight ? 'font-bold' : 'font-medium' }} {{ $footerClassRight }}"
							style="
								background-color: {{ $tableColumnShading === 'last' ? $tableLastColumnColor : 'transparent' }};
								{{ isDarkColor($tableLastColumnColor) ? 'color:white;' : 'color:black;' }}
							">
							{{ $value }}
						</td>
					</tr>
				@endforeach

				@if ($tableShowInword && $invoice->amount_inword)
					@php
						$terbilangClass = getBorderClasses($footerBorderStyle);
						if ($footerBorderStyle === 'column') {
							$terbilangClass .= ' border-s border-e';
						} elseif ($footerBorderStyle === 'row') {
							$terbilangClass .= ' border-b';
						} elseif ($footerBorderStyle === 'full') {
							$terbilangClass .= ' border';
						}
					@endphp
					<tr>
						<td colspan="{{ $colspan + 1 }}" class="border text-center px-2 py-4 {{ $terbilangClass }}" style="background-color: {{ $tableSummaryBg }};{{ isDarkColor($tableSummaryBg) ? 'color:white;' : 'color:black;' }} ">
							{{ $company->type == 1 ? 'Amount in words' : 'Terbilang' }}: <strong>{{ $invoice->amount_inword }}</strong>
						</td>
					</tr>
				@endif
			</tfoot>
		@endif
	</table>
</div>
