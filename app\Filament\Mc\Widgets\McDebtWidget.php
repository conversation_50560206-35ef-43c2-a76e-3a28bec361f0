<?php

namespace App\Filament\Mc\Widgets;

use App\Models\McCustomer;
use App\Models\McDeposit;
use App\Models\McOrder;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\View\TablesRenderHook;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class McDebtWidget extends BaseWidget
{
	protected static ?string $heading = 'Customer Balance';
	protected int | string | array $columnSpan = [
		'sm' => 1,
		'lg' => 2,
	];

	public array $selectedMcCustomerIds = [];

	protected $listeners = ['deposit-created' => '$refresh'];

	public function mount(): void
	{
		// Initialize with empty selection to show default data
		if (empty($this->selectedMcCustomerIds)) {
			$this->selectedMcCustomerIds = [];
		}
	}


	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}

	protected function paginateTableQuery(Builder $query): \Illuminate\Contracts\Pagination\Paginator
	{
		$perPage = $this->getTableRecordsPerPage();

		$records = $query->paginate(
			$perPage === 'all'
				? $query->count()
				: $perPage,
			['*'],
			$this->getTablePaginationPageName(),
		);

		return $records->onEachSide(0);
	}


	public function table(Table $table): Table
	{
		return $table
			->query(McCustomer::query())
			->modifyQueryUsing(function (Builder $query) {
				$query->where('balance', '!=', 0);
			})
			->queryStringIdentifier('balance-widget')
			->columns([
				TextColumn::make('name')
					->label('Customer')
					->sortable()
					->searchable()
					->limit(15),
				TextColumn::make('balance')
					->alignEnd()
					->money('IDR')
					->sortable()
					->color(fn($state) => ($state ?? 0) >= 0 ? 'success' : 'danger')
					->weight('bold')
					->default(0),
			])
			->actions([
				Action::make('view_details')
					->label('Order Details')
					->icon('heroicon-o-eye')
					->iconButton()
					->tooltip('View Bank Details')
					->modalWidth('4xl')
					->modalSubmitAction(false)
					->modalCancelActionLabel('Close')
					->modalFooterActionsAlignment('right')
					->modalHeading('Customer Balance Standing')
					// ->modalContent(fn ($record): View => view(
					// 	'components.global-mcbalance-modalWidget',
					// 	[
					// 		'record' => $record,
					// 		'amount' => number_format($record->amount ?? 0, 2, ',', '.'),
					// 		'charges' => number_format($record->charges ?? 0, 2, ',', '.'),
					// 		'rates' => number_format($record->sell_rates ?? 0, 2, ',', '.'),
					// 		'totalOrder' => number_format($record->total_order ?? 0, 2, ',', '.'),
					// 		'orderDate' => Carbon::parse($record->order_date)->format('d F Y'),
					// 	]
					// ))
					->modalContent(function ($record) {
						$order = McDeposit::where('mc_customer_id', $record->id)
							->select('id', 'mc_customer_id', 'slip_date', 'amount')
							->orderBy('slip_date', 'desc')
							->where('trx_type', 'order')
							->first();
						$payment = McDeposit::where('mc_customer_id', $record->id)
							->select('id', 'mc_customer_id', 'slip_date', 'amount')
							->orderBy('slip_date', 'desc')
							->where('trx_type', 'incoming')
							->first();

						$transactions = McDeposit::where('mc_customer_id', $record->id)
							->select('id', 'mc_customer_id', 'slip_date', 'amount', 'trx_type')
							->orderBy('slip_date', 'desc')
							->take(7)
							->get();

						$lastOrderAt = Carbon::parse($order?->slip_date)->format('d F Y');
						$lastPaymentAt = Carbon::parse($payment->slip_date)->format('d F Y');
						$lastOrder = number_format($order->amount ?? 0, 2, ',', '.');
						$lastPayment = number_format($payment->amount ?? 0, 2, ',', '.');
						$totalOrder = number_format($record->total_orders ?? 0, 2, ',', '.');
						$totalPayment = number_format($record->total_deposits ?? 0, 2, ',', '.');
						$balance = number_format($record->balance ?? 0, 2, ',', '.');

						return view('components.global-mcbalance-modalWidget', compact('record','order', 'payment', 'lastOrderAt', 'lastPaymentAt', 'lastOrder', 'lastPayment', 'totalOrder', 'totalPayment', 'balance', 'transactions'));
					}),
			])
			->headerActions([
				Action::make('view_list')
					->hiddenLabel()
					->tooltip('View all deposits/payments')
					->icon('heroicon-o-list-bullet')
					->color('primary')
					->size('sm')
					->url(fn() => route('filament.mc.resources.mc-deposits.index'))
					->visible(fn() => Auth::user()->hasAnyRole(['admin', 'Super Admin'])),
				Action::make('new_payment')
					->hiddenLabel()
					->icon('icon-piggy-bank')
					->tooltip('Add new Payment')
					->color('warning')
					->size('sm')
					->url('#mcdeposit-modal')
					->visible(fn() => Auth::user()->hasAnyRole(['admin', 'Super Admin'])),
				Action::make('recalc')
					->hiddenLabel()
					->icon('heroicon-o-arrow-path')
					->tooltip('Recalculate Customers Balance')
					->color('danger')
					->size('sm')
					->visible(fn() => Auth::user()->hasAnyRole(['admin', 'Super Admin']))
					->action(function () {
						DB::beginTransaction();
						try {
							// Ambil semua deposit yang dibutuhkan sekaligus
							$deposits = McDeposit::select('mc_customer_id', 'trx_type', 'amount')
								->get()
								->groupBy('mc_customer_id');

							// Ambil semua customer yang ada di deposit
							$customerIds = $deposits->keys();
							$customers = McCustomer::whereIn('id', $customerIds)->get();
							foreach ($customers as $customer) {
								$customerDeposits = $deposits->get($customer->id, collect());

								$totalOut = $customerDeposits
									->filter(fn($d) => str_contains($d->trx_type, 'out') || str_contains($d->trx_type, 'order'))
									->sum('amount');

								$totalIn = $customerDeposits
									->filter(fn($d) => str_contains($d->trx_type, 'incoming'))
									->sum('amount');

								$balance = $totalIn - $totalOut;

								$customer->update([
									'total_orders'   => $totalOut,
									'total_deposits' => $totalIn,
									'balance'        => $balance,
								]);
							}

							$zeroBalanceCustomerIds = McCustomer::where('balance', 0)->pluck('id');
							McDeposit::whereIn('mc_customer_id', $zeroBalanceCustomerIds)->forceDelete();

							DB::commit();
							Notification::make()
								->title('Success')
								->success()
								->body('Customer balance recalculation completed successfully.')
								->send();
						} catch (\Exception $e) {
							DB::rollBack();
							Notification::make()
								->title('Error')
								->danger()
								->body('Unable to complete customer balance recalculation.')
								->send();
							Log::error($e);
						}
					}),
			])
			->striped()
			->paginated([6])
			->defaultPaginationPageOption(3)
			->emptyStateHeading('No Data Available')
			->defaultSort('balance', 'asc')
			->emptyStateDescription('no customer has any debt.');
	}


	public function getTableRenderHooks(): array
	{
		return [
			TablesRenderHook::HEADER_BEFORE => fn() => null,
			TablesRenderHook::HEADER_AFTER => fn() => null,
		];
	}
}
