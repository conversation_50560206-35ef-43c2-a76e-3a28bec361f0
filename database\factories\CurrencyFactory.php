<?php

namespace Database\Factories;

use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

class CurrencyFactory extends Factory
{
    protected $model = Currency::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->currencyCode(),
            'symbol' => $this->faker->randomElement(['$', '€', '£', '¥', 'Rp']),
            'country' => $this->faker->country(),
            'suffix' => $this->faker->randomElement(['', 'K', 'M']),
        ];
    }
}
