<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetParentInvoiceId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:set-parent-invoice-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
public function handle()
    {
        DB::transaction(function () {
            $this->info('Proses dimulai...');

            $childInvoices = Invoice::whereNotNull('prev_invoice')->get();

            foreach ($childInvoices as $child) {
                $parent = Invoice::where('invoice_no', $child->prev_invoice)->first();

                if ($parent) {
                    $child->parent_invoice_id = $parent->id;
                    $child->save();

                    $this->info("Invoice {$child->invoice_no} -> parent ID {$parent->id}");
                } else {
                    $this->warn("Parent tidak ditemukan untuk invoice_no: {$child->invoice_no} (prev: {$child->prev_invoice})");
                }
            }

            $this->info('Proses selesai.');
        });
    }
}
