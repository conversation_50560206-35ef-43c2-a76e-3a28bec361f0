<?php

namespace Database\Factories;

use App\Models\CompanyDeposito;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyDepositoFactory extends Factory
{
    protected $model = CompanyDeposito::class;

    public function definition(): array
    {
        return [
            'company_id' => 1, // Will be overridden in tests
            'order_id' => null, // Can be null for manual deposits
            'bank_id' => 1, // Will be overridden in tests
            'description' => $this->faker->sentence(),
            'trx_date' => $this->faker->date(),
            'trx_type' => $this->faker->randomElement(['in', 'out', 'order']),
            'amount' => $this->faker->randomFloat(2, 1000, 100000),
        ];
    }
}
