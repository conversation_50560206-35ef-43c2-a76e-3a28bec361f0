<x-filament-widgets::widget>
    <x-filament::section class="mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3 w-full">
            <div class="col-span-1 lg:col-span-3 space-y-2">
                <span class="text-lg font-semibold px-4 mb-4">Transaction Insight</span>
                <div class="grid grid:cols-1 lg:grid-cols-3 lg:border-dashed lg:border-t lg:border-b">
                    <div class="col-span-1 p-4 text-center flex flex-col">
                        <span class="font-semibold">IDR {{ number_format($transactionInsight['lastWeekPurchase'], 2, ',', '.') }}</span>
                        <span class="text-sm text-gray-400 dark:text-gray-200">Previous Week</span>
                    </div>
                    <div class="col-span-1 lg:border-dashed lg:border-s lg:border-e p-4 text-center flex flex-col">
                        <span class="font-semibold">IDR {{ number_format($transactionInsight['thisWeekPurchase'], 2, ',', '.') }}</span>
                        <span class="text-sm text-gray-400 dark:text-gray-200">This Week</span>
                    </div>
                    <div class="col-span-1 p-4 text-center flex flex-col">
                        <span class="font-semibold">IDR {{ number_format($transactionInsight['thisMonthPurchase'], 2, ',', '.') }}</span>
                        <span class="text-sm text-gray-400 dark:text-gray-200">This Month</span>
                    </div>
                </div>
				{{-- order trends widget here --}}
				@livewire(\App\Filament\Admin\Widgets\OrderTrendsWidget::class)
				<span class="text-xs italic text-gray-500 dark:text-gray-400">Weekly Purchase Transaction Trends</span>
            </div>
            <div class="col-span-1 lg:col-span-2 lg:border-s px-4 space-y-6 w-full">
                <div class="flex justify-between align-center">
                    <div class="flex align-top">
                        <x-filament::icon icon="heroicon-s-currency-dollar"
                            class="h-8 w-8 text-gray-500 dark:text-gray-400" />
                        <div class="flex flex-col">
                            <span class="text-lg font-semibold">Orders</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">Today Orders Performance</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="text-end">
                            <x-filament::icon-button icon="heroicon-s-share" size="lg" label="Share"
                                color="info" />
                        </span>
                    </div>
                </div>

                {{-- Most Traded Currency --}}
				@livewire(\App\Filament\Admin\Widgets\OrderSummaryWidget::class)
				<span class="text-xs italic text-gray-500 dark:text-gray-400">Today orders by currency.</span>
                <div class="flex items-center gap-2 mb-2">
                    <span
                        class="w-7 h-7 flex items-center justify-center rounded-full border dark:border-none dark:bg-gray-700 text-gray-50 font-medium">
                        🏆
                    </span>
                    <span class="font-bold">Most Traded</span>
                </div>
                <div class="mb-4 text-gray-500 dark:text-gray-400">
                    <p>
                        With a total valuation of IDR {{ number_format($leadingTradedCurrency['totalSell'], 0, ',', '.') }} from {{ $leadingTradedCurrency['orderCount'] }} sales transactions, the <strong class="dark:text-teal-500">{{ $leadingTradedCurrency['name'] }} ({{ $leadingTradedCurrency['symbol'] }})</strong> has become the <strong class="dark:text-teal-500">MOST TRADED</strong> currency this month.
                    </p>
                </div>

                {{-- Most Transactioned Currency --}}
                {{-- <div class="flex items-center gap-2 mb-2">
                    <span
                        class="w-7 h-7 flex items-center justify-center rounded-full border dark:border-none dark:bg-gray-700 text-white font-medium">
                        🏆
                    </span>
                    <span class="font-bold">Most Transactioned</span>
                </div>
                <div class="text-gray-500 dark:text-gray-400">
                    <p>
                        With {{ $leadingTransactionedCurrency['orderCount'] }} total transactions totaling IDR
                        {{ number_format($leadingTransactionedCurrency['totalSell'], 0, ',', '.') }},
                        the <strong class="dark:text-teal-500">{{ $leadingTransactionedCurrency['name'] }}
                            ({{ $leadingTransactionedCurrency['symbol'] }})</strong> has become the <strong class="dark:text-teal-500">MOST
                            TRANSACTIONED</strong> currency this month.
                    </p>
                </div> --}}
				{{-- <hr>
                <div class="flex justify-center gap-3">
                    <x-filament::button color="info" href="#deposit-modal" tag="a">
                        New Deposit
                    </x-filament::button>

                    <x-filament::button color="info" href="#order-modal" tag="a">
                        New Order
                    </x-filament::button>

                    <x-filament::button color="warning" href="admin/invoices" tag="a">
                        New Invoice
                    </x-filament::button>

                    <x-filament::button color="success" href="#report-modal" tag="a">
                        Reports
                    </x-filament::button>

                </div> --}}
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
{{-- <div class="space-y-6 grid-cols-1">
		<x-filament::section
			style="background-color: #fff4f4">
			<div class="grid gap-y-2">
				<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-500 dark:text-gray-200">Total Balance</span>
				<span class="text-3xl font-semibold">
					Rp {{ $totalBalance }}
				</span>
			</div>
		</x-filament::section>
		<x-filament::section
			style="background-color: #fffbf4">
			<div class="grid gap-y-2">
				<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-500 dark:text-gray-200">Cash Flow</span>
				<span class="text-3xl font-semibold">
					Rp {{ $cashFlowTotal }}
				</span>
			</div>
		</x-filament::section>
		<x-filament::section
			style="background-color: #f5f4ff">
			<div class="grid gap-y-2">
				<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-500 dark:text-gray-200">Low Balance Banks</span>
				<span class="text-3xl font-semibold">
					{{ $lowBalanceBanks }}
				</span>
				<span class="fi-section-header-description overflow-hidden break-words text-sm text-gray-500 dark:text-gray-400">Banks below IDR 1M threshold</span>
			</div>
		</x-filament::section>
</div> --}}
{{-- @livewire(\App\Filament\Admin\Widgets\DepositSummaryWidget::class) --}}
