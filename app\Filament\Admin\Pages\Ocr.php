<?php

namespace App\Filament\Admin\Pages;

use Filament\Forms\Components\{Actions, CheckboxList, ColorPicker, Fieldset, FileUpload, Group, Hidden, Placeholder, Radio, Repeater, RichEditor, Select, Textarea, TextInput, Toggle};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use thiagoalessio\TesseractOCR\TesseractOCR;

class Ocr extends Page
{
    protected static ?string $navigationIcon = 'icon-file-medical';
    protected static ?string $navigationLabel = 'OCR';
    protected static ?string $title = 'OCR';
    protected static ?int $navigationSort = 4;
    protected static string $view = 'filament.admin.pages.ocr';
	protected static bool $shouldRegisterNavigation = false;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	public function getHeading(): string|Htmlable
    {
        return new HtmlString('<span class="text-sm italic">Optical Character Recognition</span>');
    }

	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-sm italic">Optical Character Recognition</span>');
    }

    public ?array $formData = [];
	public $ocrUpload;
    public function form(Form $form): Form
    {
        return $form
            ->schema([
				FileUpload::make('ocrUpload')
					->reactive()
					->label('OCR')
					->image()
					->directory('ocr-temp')
					->multiple(false)
					->disk('local')
					->preserveFilenames()
					->columnSpan(1)
					->panelAspectRatio('9:16')
					->panelLayout('integrated')
					// ->imagePreviewHeight('50')
					->afterStateUpdated(function ($state, $set) {
						if (!$state) {
							$set('ocr_text', '');
							return;
						}
						if ($state instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
							$tmpPath = $state->getRealPath();
						} elseif (is_string($state)) {
							$tmpPath = storage_path('app/' . $state);
						} else {
							$set('ocr_text', '[File tidak dikenali]');
							return;
						}

						if (!file_exists($tmpPath)) {
							$set('ocr_text', '[File belum tersedia]');
							return;
						}

						// Salin file ke penyimpanan permanen
						$filename = uniqid('ocr_') . '.' . pathinfo($tmpPath, PATHINFO_EXTENSION);
						$newPath = 'ocr-temp/' . $filename;
						Storage::disk('local')->put($newPath, file_get_contents($tmpPath));
						$ocrPath = Storage::disk('local')->path($newPath);

						// Jalankan OCR
						$text = (new TesseractOCR($ocrPath))
							->lang('eng')
							->run();

						// Set hasil OCR ke field lain
						$set('ocr_text', $text);

						$set('ocr_final_path', $newPath);
					})
					->hintAction(
						Action::make('reset_ocr')
							->label('Reset OCR')
							->color('danger')
							->icon('heroicon-o-trash')
							->action(function ($get, $set) {
								$ocrPath = $get('ocrUpload');
								if ($ocrPath && is_string($ocrPath) && Storage::disk('local')->exists($ocrPath)) {
									Storage::disk('local')->delete($ocrPath);
								}

								$set('ocrUpload', null);
								$set('ocr_text', null);
							}),
					),
				Hidden::make('ocr_final_path'),
				Textarea::make('ocr_text')
					->label('Output')
					->rows(10)
					->columnSpan(2)
					->reactive()
					->helperText(function ($state) {
						return blank($state)
							? null
							: new HtmlString('<span class="text-danger-500 text-sm">Please check the OCR output before paste them to the appropriate fields.</span>');
					})
					->placeholder(function ($state) {
						return blank($state)
							? 'You can copy the OCR output and paste them to the appropriate fields.'
							: null;
					}),
            ])
			->columns(3)
			->statePath('formData');
    }
}
