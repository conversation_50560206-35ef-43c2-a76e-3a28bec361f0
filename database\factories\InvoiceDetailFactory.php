<?php

namespace Database\Factories;

use App\Models\InvoiceDetail;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceDetailFactory extends Factory
{
    protected $model = InvoiceDetail::class;

    public function definition(): array
    {
        $quantity = $this->faker->randomFloat(2, 1, 10);
        $price = $this->faker->randomFloat(2, 100, 1000);
        
        return [
            'invoice_id' => 1, // Will be overridden in tests
            'company_id' => 1,
            'client_id' => 1,
            'description' => $this->faker->sentence(),
            'quantity' => $quantity,
            'unit' => $this->faker->randomElement(['pcs', 'kg', 'liter', 'hour']),
            'price' => $price,
            'rates_details' => $this->faker->randomFloat(2, 14000, 16000),
            'details_date' => $this->faker->date(),
            'sub_total' => $quantity * $price,
            'status' => 'active',
        ];
    }
}
