<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\ProfitSummary;
use App\Models\User;

class ProfitSummaryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any ProfitSummary');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProfitSummary $profitsummary): bool
    {
        return $user->checkPermissionTo('view ProfitSummary');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create ProfitSummary');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProfitSummary $profitsummary): bool
    {
        return $user->checkPermissionTo('update ProfitSummary');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProfitSummary $profitsummary): bool
    {
        return $user->checkPermissionTo('delete ProfitSummary');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any ProfitSummary');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProfitSummary $profitsummary): bool
    {
        return $user->checkPermissionTo('restore ProfitSummary');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any ProfitSummary');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, ProfitSummary $profitsummary): bool
    {
        return $user->checkPermissionTo('replicate ProfitSummary');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder ProfitSummary');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProfitSummary $profitsummary): bool
    {
        return $user->checkPermissionTo('force-delete ProfitSummary');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any ProfitSummary');
    }
}
