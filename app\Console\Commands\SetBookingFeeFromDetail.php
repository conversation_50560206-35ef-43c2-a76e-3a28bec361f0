<?php

namespace App\Console\Commands;

use App\Models\InvoiceDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetBookingFeeFromDetail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:set-booking-fee-from-detail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::transaction(function () {
            $this->info('Memulai pencarian detail Booking Fee...');

            // Jika menggunakan MySQL:
            $details = InvoiceDetail::whereRaw('LOWER(description) LIKE ?', ['%booking fee%'])->get();

            // Jika menggunakan PostgreSQL, gunakan:
            // $details = InvoiceDetail::where('description', 'ILIKE', '%booking fee%')->get();

            $counter = 0;

            foreach ($details as $detail) {
                $invoice = $detail->invoice;

                if ($invoice) {
                    $invoice->booking_fee = $detail->price;
                    $invoice->save();
                    $this->line("✔ Invoice ID {$invoice->id} di-set booking_fee = {$detail->price}");
                    $counter++;
                } else {
                    $this->warn("⚠ Invoice tidak ditemukan untuk invoice_detail ID: {$detail->id}");
                }
            }

            $this->info("Selesai. Total invoice diupdate: $counter");
        });

        return Command::SUCCESS;
    }
}
