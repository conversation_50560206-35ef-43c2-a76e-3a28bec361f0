<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceApprovalLog extends Model
{
    protected $fillable = [
        'invoice_id',
        'user_id',
        'creator_id',
        'approval_type',
        'old_status',
        'new_status',
        'reason',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * Invoice yang di-approve/reject
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * User yang melakukan approval action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * User yang membuat invoice (creator)
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * Scope untuk rejection logs
     */
    public function scopeRejections($query)
    {
        return $query->where('new_status', '2');
    }

    /**
     * Scope untuk approval logs
     */
    public function scopeApprovals($query)
    {
        return $query->where('new_status', '1');
    }

    /**
     * Scope untuk bulan tertentu
     */
    public function scopeInMonth($query, $year, $month)
    {
        return $query->whereYear('created_at', $year)
                    ->whereMonth('created_at', $month);
    }

    /**
     * Scope untuk creator tertentu
     */
    public function scopeByCreator($query, $creatorId)
    {
        return $query->where('creator_id', $creatorId);
    }

    /**
     * Scope untuk approval type tertentu
     */
    public function scopeByApprovalType($query, $type)
    {
        return $query->where('approval_type', $type);
    }
}
