<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
		@page {
			size: A4 portrait;
			margin: 0;
		}

        body {
            margin: 0;
            padding: 0;
            font-family: 'Courier New', monospace;
			font-weight: bold;
            font-size: 12px;
        }

		.container {
			position: relative;
			top: 50px;
			width: 794px;  /* 210mm */
			height: 1123px; /* 297mm */
		}


        .field {
            position: absolute;
            white-space: nowrap;
        }

        /* Kiri */
        .send_to       { top: 108px; left: 100px; }
        .address1      { top: 122px; left: 80px; }
        .address2      { top: 142px; left: 80px; }
        .bank          { top: 162px; left: 80px; }
        .no_rekening   { top: 182px; left: 80px; }
        .kota          { top: 202px; left: 80px; }
        .negara        { top: 202px; left: 400px; }
        .kode_bank     { top: 222px; left: 80px; }
        .via_branch    { top: 242px; left: 80px; }
        .sender        { top: 262px; left: 80px; }
        .sender_address1 { top: 282px; left: 80px; }
        .sender_address2 { top: 302px; left: 80px; }
        .source_fund   { top: 322px; left: 80px; }
        .purpose       { top: 342px; left: 80px; }
        .occupation    { top: 362px; left: 80px; }
        .position      { top: 382px; left: 80px; }
        .birth         { top: 402px; left: 80px; }
        .ktp           { top: 422px; left: 80px; }

        /* Kanan */
        .date   { top: 38px; left: 630px; }
        .amount_sent   { top: 100px; left: 560px; }
        .charge        { top: 122px; left: 560px; }
        .amount_total  { top: 144px; left: 560px; }
        .amount_words  { top: 172px; left: 430px; }

        .tunai_check   { top: 208px; left: 470px; }
        .debet_check   { top: 208px; left: 550px; }
        .cek_check     { top: 208px; left: 630px; }

        .berita        { top: 260px; left: 430px; }

        .ttd           { top: 590px; left: 730px; }

        .slip-debug {
            outline: 1px dashed red;
        }
    </style>
</head>
<body>
    <div class="container">
        {{-- Kiri --}}
        <div class="field send_to">{{ $data['send_to'] ?? '' }}</div>
        {{-- <div class="field address1">{{ $data['address1'] ?? '' }}</div>
        <div class="field address2">{{ $data['address2'] ?? '' }}</div>
        <div class="field bank">{{ $data['bank'] ?? '' }}</div>
        <div class="field no_rekening">{{ $data['no_rekening'] ?? '' }}</div>
        <div class="field kota">{{ $data['kota'] ?? '' }}</div>
        <div class="field negara">{{ $data['negara'] ?? '' }}</div>
        <div class="field kode_bank">{{ $data['kode_bank'] ?? '' }}</div>
        <div class="field via_branch">{{ $data['via_branch'] ?? '' }}</div>
        <div class="field sender">{{ $data['sender'] ?? '' }}</div>
        <div class="field sender_address1">{{ $data['sender_address1'] ?? '' }}</div>
        <div class="field sender_address2">{{ $data['sender_address2'] ?? '' }}</div>
        <div class="field source_fund">{{ $data['source_fund'] ?? '' }}</div>
        <div class="field purpose">{{ $data['purpose'] ?? '' }}</div>
        <div class="field occupation">{{ $data['occupation'] ?? '' }}</div>
        <div class="field position">{{ $data['position'] ?? '' }}</div>
        <div class="field birth">{{ $data['birth'] ?? '' }}</div>
        <div class="field ktp">{{ $data['ktp'] ?? '' }}</div> --}}

        {{-- Kanan --}}
        <div class="field date" style="color: #000000">31/01/2025</div>
        {{-- <div class="field amount_sent" style="color: #000000">{{ $data['amount_sent'] ?? '' }}</div>
        <div class="field charge">{{ $data['charge'] ?? '' }}</div>
        <div class="field amount_total">{{ $data['amount_total'] ?? '' }}</div>
        <div class="field amount_words">{{ $data['amount_words'] ?? '' }}</div>

        <div class="field tunai_check">✔</div>
        <div class="field debet_check"></div>
        <div class="field cek_check"></div>

        <div class="field berita">{{ $data['berita'] ?? '' }}</div>

        <div class="field ttd">{{ $data['ttd'] ?? '' }}</div> --}}
    </div>
</body>
</html>
