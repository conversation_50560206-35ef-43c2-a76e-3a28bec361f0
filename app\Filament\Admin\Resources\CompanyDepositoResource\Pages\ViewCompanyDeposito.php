<?php

namespace App\Filament\Admin\Resources\CompanyDepositoResource\Pages;

use App\Filament\Admin\Resources\CompanyDepositoResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCompanyDeposito extends ViewRecord
{
    protected static string $resource = CompanyDepositoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
