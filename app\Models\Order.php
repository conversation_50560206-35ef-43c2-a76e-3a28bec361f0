<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class Order extends Model
{
	use HasFactory, SoftDeletes;

	protected $table = 'orders';

	protected $fillable = [
		'order_num',
		'order_no',
		'order_create_by',
		'agent_id',
		'company_id',
		'bank_id',
		'currency_id',
		'order_amount',
		'order_date',
		'booking_fee',
		'rates',
		'sell_rates',
		'charges',
		'total',
		'status', //Draft, Forwarded, Invoiced
	];
	public static bool $skipBootedEvent = false;
	protected static function boot()
	{
		parent::boot();

		static::addGlobalScope('order', function ($builder) {
			$query = $builder->getQuery();

			// <PERSON>ya tambahkan ORDER BY kalau bukan query agregat
			if (empty($query->orders) && empty($query->aggregate)) {
				$builder->orderBy('created_at', 'desc');
			}
		});

		static::creating(function ($order) {
			$monthMapping = [
				'01' => 'A',
				'02' => 'B',
				'03' => 'C',
				'04' => 'D',
				'05' => 'E',
				'06' => 'F',
				'07' => 'G',
				'08' => 'H',
				'09' => 'I',
				'10' => 'J',
				'11' => 'K',
				'12' => 'L',
			];
			$tc = time();
			$currentMonth = now()->format('m');
			$mappedMonth = $monthMapping[$currentMonth];
			$yearNumber = now()->format('y');
			$userId = Auth::user()->id;

			$prefix = "TRX{$yearNumber}{$mappedMonth}-{$tc}U{$userId}-";
			$lastCode = Order::where('order_no', 'like', "{$prefix}%")
				->orderByDesc('order_no')
				->value('order_no');

			// Ambil 5 digit terakhir dari kode sebelumnya
			$lastNumber = 0;
			if ($lastCode && preg_match('/(\d{5})$/', $lastCode, $matches)) {
				$lastNumber = (int) $matches[1];
			}

			$nextNumber = $order->order_num;
			$order->order_no = $prefix . str_pad($order->order_num, 5, '0', STR_PAD_LEFT);

		});

		/* Create deposit after order is created
			1. hanya create ketika total, rates, dan bank_id ada isinya
		*/
		static::created(function ($order) {
			if (self::$skipBootedEvent) return;
			if ($order->total && $order->rates && $order->bank_id) {
				CompanyDeposito::create([
					'company_id' => $order->company_id,
					'order_id' => $order->id,
					'bank_id' => $order->bank_id,
					'description' => 'Payment for order ' . $order->order_no,
					'trx_date' => $order->order_date,
					'trx_type' => 'order',
					'amount' => $order->total * $order->rates, // Convert to IDR
				]);
			}
			self::invalidateRevenueCache();
		});

		// Handle deposit when order is updated
		static::updating(function ($order) {
			// Soft delete existing deposit for this order
			if ($order->isDirty(['company_id','total', 'rates', 'bank_id', 'order_date'])) {
				CompanyDeposito::where('order_id', $order->id)
					->where('trx_type', 'order')
					->delete();
			}
		});

		// Create new deposit after order is updated
		static::updated(function ($order) {
			if ($order->wasChanged(['company_id','total', 'rates', 'bank_id', 'order_date'])) {
				if ($order->total && $order->rates && $order->bank_id) {
					CompanyDeposito::create([
						'company_id' => $order->company_id,
						'order_id' => $order->id,
						'bank_id' => $order->bank_id,
						'description' => 'Payment for order ' . $order->order_no,
						'trx_date' => $order->order_date,
						'trx_type' => 'order',
						'amount' => $order->total * $order->rates, // Convert to IDR
					]);
				}
			}
			self::invalidateRevenueCache();
		});

		// Handle deposit and invoices when order is deleted
		static::deleting(function ($order) {
			// Soft delete related deposit
			CompanyDeposito::where('order_id', $order->id)
				->where('trx_type', 'order')
				->delete();

			// Soft delete all related invoices
			$order->invoices()->delete();
		});
		static::deleted(function ($order) {
			self::invalidateRevenueCache();
		});
	}

	protected static function invalidateRevenueCache(): void
	{
		Cache::forget('dashboard.revenue.summary');
		Cache::forget('dashboard.cashflow');
		Cache::forget('dashboard.low-balance-banks');
		Cache::forget('dashboard.total-balance');
		Cache::forget('dashboard.view-data.summary');
		Cache::forget('order_trends_widget');
	}


	public function user()
	{
		return $this->belongsTo(User::class, 'order_create_by', 'id');
	}

	public function invoices()
	{
		return $this->hasMany(Invoice::class, 'order_id', 'id');
	}

	// Keep backward compatibility for existing code
	public function invoice()
	{
		return $this->hasOne(Invoice::class, 'order_id', 'id');
	}

	public function company()
	{
		return $this->belongsTo(Company::class, 'company_id', 'id');
	}

	public function currency()
	{
		return $this->belongsTo(Currency::class, 'currency_id', 'id');
	}

	public function bank()
	{
		return $this->belongsTo(CompanyBank::class, 'bank_id', 'id');
	}

	public function deposits()
	{
		return $this->hasMany(CompanyDeposito::class, 'order_id', 'id');
	}
	public function agent()
	{
		return $this->belongsTo(McAgent::class, 'agent_id', 'id');
	}
}
