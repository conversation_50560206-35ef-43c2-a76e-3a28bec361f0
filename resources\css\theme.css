@import "/vendor/filament/filament/resources/css/theme.css";
/* @import "/vendor/awcodes/filament-table-repeater/resources/css/plugin.css"; */
@import "tooltip.css";

@config './tailwind.config.js';

.fi-input-right {
	text-align: right;
}

.placeholder-bordered {
	border: 10px #888 !important;
}

.fi-input-bg-normal {
	background-color: white !important;
}

.text-input-xl {
	font-size: 4rem !important;
}

.fi-input-inactive {
	@apply py-0.5 bg-gray-50 dark:text-gray-300 dark:bg-gray-900/60 pointer-events-none cursor-not-allowed;
}

.fi-input-hidden {
	display: none;
	visibility: hidden;
}

/* .choices__list.choices__list--single {
    @apply w-full;

}

.choices:focus-visible {
    outline: none;
}

.choices__group {
    @apply text-gray-900 dark:text-white font-semibold;
}

.choices[data-type="select-one"] .choices__inner {
    line-height: 1.5;
    display: flex;
    align-items: center;
    min-height: 2.25rem;
    box-sizing: border-box;
}

.choices:not(.is-disabled) .choices__item {
    cursor: pointer;
} */
/* 
.table-repeater-container {
	@apply rounded-none ring-1;
}

.table-repeater-component {
	@apply space-y-2;
}

.table-repeater-component ul {
	@apply justify-start;
}

.table-repeater-row {
	@apply divide-x-0 !important;
}

.table-repeater-column {
	@apply px-0.5 !important;
}

.table-repeater-column .fi-input-wrp {
	@apply rounded-none !important;
}

.table-repeater-header {
	@apply rounded-t-none !important;
}

.table-repeater-rows-wrapper {
	@apply divide-gray-300 last:border-b last:border-gray-300 dark:divide-white/20 dark:last:border-white/20;
}

.table-repeater-header tr {
	@apply divide-x-0 text-base sm:text-sm sm:leading-6 !important;
}

.table-repeater-header-column {
	@apply ps-3 pe-3 font-semibold bg-gray-200 dark:bg-gray-800 rounded-none !important;
} */

.es-report-card {
	@apply md:!grid-cols-2;

	.fi-fo-component-ctn {
		@apply divide-y divide-gray-200 dark:divide-white/10 !gap-0;
	}

	.fi-section-content-ctn {
		@apply md:!col-span-1;
	}

	.fi-section-content {
		@apply !p-0;
	}
}

.fi-modal.fi-width-screen {
	.fi-modal-header {
		@apply xl:px-80;

		.absolute.end-4.top-4 {
			@apply xl:end-80;
		}
	}

	.fi-modal-content {
		@apply xl:px-80;
	}

	.fi-modal-footer {
		@apply xl:px-80;
	}
}

.es-table__header-ctn,
.es-table__footer-ctn {
	@apply divide-y divide-gray-200 dark:divide-white/10 h-12;
}

.es-table__row {
	@apply [@media(hover:hover)]:transition [@media(hover:hover)]:duration-75 hover:bg-gray-50 dark:hover:bg-white/5;
}

.es-table .es-table__rowgroup td:first-child {
	padding-left: 3rem;
}

.es-table .es-table__rowgroup .es-table__row > td:nth-child(2) {
	word-wrap: break-word;
	word-break: break-word;
	white-space: normal;
}

.es-table .es-table__rowgroup .es-table__row > td:nth-child(3) {
	word-wrap: break-word;
	word-break: break-word;
	white-space: normal;
}

.es-table .es-table__rowgroup .es-table__row > td:nth-child(4) {
	white-space: nowrap;
}

.es-table .es-table__rowgroup .es-table__row > td:last-child {
	padding-right: 3rem;
}
/*
.fi-ta-empty-state-icon-ctn {
    @apply bg-platinum;
 }

.fi-body {
    position: relative;
    background-color: #FFFFFF;
    z-index: 1;
}

.fi-body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(99.6deg,
    rgba(232, 233, 235, 1) 10.6%,
    rgba(240, 241, 243, 1) 32.9%,
    rgba(248, 249, 251, 0.7) 50%,
    rgba(240, 241, 243, 1) 67.1%,
    rgba(232, 233, 235, 1) 83.4%);
    pointer-events: none;
    z-index: -1;
}

:is(.dark .fi-body) {
    position: relative;
    background-color: rgb(3, 7, 18);
    z-index: 1;
}

:is(.dark .fi-body)::before {
    content: '';
    position: fixed;
    top: 0;
    right: 0;
    background-image: radial-gradient(
        ellipse at top right,
        rgba(var(--primary-950), 1) 0%,
        rgba(var(--primary-950), 0.9) 15%,
        rgba(var(--primary-900), 0.7) 30%,
        rgba(var(--primary-900), 0.5) 45%,
        rgba(var(--primary-950), 0.3) 60%,
        rgba(var(--primary-950), 0.1) 75%,
        rgba(3, 7, 18, 0) 100%
    );
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
} */

/* .fi-topbar > nav, .fi-sidebar-header {
    @apply bg-transparent ring-0 shadow-none !important;
    transition: background-color 0.3s, top 0.3s;
}

.fi-topbar > nav.topbar-hovered, .fi-sidebar-header.topbar-hovered {
    background-color: rgba(255, 255, 255, 0.75) !important;
} */

/* :is(.dark .fi-topbar > nav.topbar-hovered, .dark .fi-sidebar-header.topbar-hovered) {
    background-color: rgba(10, 16, 33, 0.75) !important;
}

.fi-topbar > nav.topbar-scrolled, .fi-sidebar-header.topbar-scrolled {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

:is(.dark .fi-topbar > nav.topbar-scrolled, .dark .fi-sidebar-header.topbar-scrolled) {
    background-color: rgba(10, 16, 33, 0.5) !important;
} */

.fi-input,
.choices__inner,
.fi-fo-date-time-picker-display-text-input {
	padding-top: 0.125rem !important; /* 2px */
	padding-bottom: 0.125rem !important; /* 2px */
}

.fi-section-content {
	padding: 1rem !important;
}

.fi-section-header {
	padding: 0.5rem !important;
}

.fi-ta-text {
	padding: 0.5rem !important;
}

.font-semibold {
	font-weight: 500 !important;
}

.input[type="checkbox"] {
	border: 1px solid var(--gray-500);
	box-sizing: border-box;
}

.fi-main {
	padding-left: 1.5rem !important;
	padding-right: 1.5rem !important;
	width: 100% !important;
}

.fi-sidebar-nav {
	padding-left: 1rem !important;
	padding-right: 1rem !important;
	padding-top: 1rem !important;
	padding-bottom: 1rem !important;
}
.fi-sidebar-open {
	width: 15rem !important;
}

/* .choices__list .choices--databranches-item-choice-1 {
    padding: 0.125rem !important;
} */

.fi-fo-component-ctn {
	gap: 1rem !important;
}

::-webkit-scrollbar {
	width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
	background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
	background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
	background: #555;
}

.fi-fo-field-wrp-helper-text {
	font-size: 0.7rem !important;
	line-height: 0.9rem !important;
}

.fi-section-header-description {
	font-size: 0.8rem !important;
	line-height: 0.9rem !important;
}

.fi-ta-text p.text-sm {
	font-size: 0.8rem !important;
	line-height: 0.9rem !important;
	color: rgb(188, 188, 188);
	font-style: italic;
}

.fi-fo-component-ctn {
	gap: 0.5rem !important;
}

.table-repeater-component > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.fi-input-wrp {
	@apply focus-within:ring-2 !important;
	@apply focus-within:ring-primary-400 !important;
}

.fi-sidebar-nav-groups {
	@apply gap-y-4 !important;
}
