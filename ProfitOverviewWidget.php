<?php

namespace App\Filament\Admin\Widgets;

use App\Models\ProfitSummary;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class ProfitOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';
    // protected static ?int $sort = 0;
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
    protected function getStats(): array
    {
        $summary = ProfitSummary::first();

        return [
            Stat::make('Today Revenue', $this->formatRupiah($summary->today ?? 0))->color('success'),
            Stat::make('This Week Revenue', $this->formatRupiah($summary->this_week ?? 0))->color('warning'),
            Stat::make('This Month Revenue', $this->formatRupiah($summary->this_month ?? 0))->color('info'),
            Stat::make('This Year Revenue', $this->formatRupiah($summary->this_year ?? 0))->color('primary'),
        ];
    }

    protected function formatRupiah($value): string
    {
        return 'Rp ' . number_format($value, 2, ',', '.');
    }
}
