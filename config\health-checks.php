<?php

// Removed all class imports to avoid serialization issues with config:cache

return [
    /*
     * A list of checks to run for the application.
     */
    'checks' => [
        // Checks are now registered in HealthServiceProvider instead of here
        // to avoid serialization issues with config:cache
    ],

    /*
     * A check group is a collection of checks.
     * You can register your own check groups if needed.
     */
    'check_groups' => [
        'default' => [
            'checks' => [
                'Debug Mode',
                'Environment',
                'Optimized App',
                'Database',
                'Cache',
                // 'Queue', // Disabled as queue is not used
                // 'Schedule', // Disabled as schedule is not used
                'Disk Space',
            ],
        ],
        'web' => [
            'checks' => [
                'Database',
                'Cache',
                'Disk Space',
            ],
        ],
        'api' => [
            'checks' => [
                'Database',
                // 'Queue', // Disabled as queue is not used
            ],
        ],
    ],
];
