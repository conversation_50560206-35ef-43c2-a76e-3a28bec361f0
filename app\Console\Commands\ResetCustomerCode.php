<?php

namespace App\Console\Commands;

use App\Models\McAgent;
use App\Models\McCustomer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetCustomerCode extends Command
{
    protected $signature = 'app:reset-customer-code';
    protected $description = 'Mengatur ulang customer_code untuk pelanggan yang belum memiliki kode, berdasarkan agent_code dan tanggal saat ini.';

    public function handle()
    {
        $monthMapping = [
            '01' => 'A', '02' => 'B', '03' => 'C', '04' => 'D',
            '05' => 'E', '06' => 'F', '07' => 'G', '08' => 'H',
            '09' => 'I', '10' => 'J', '11' => 'K', '12' => 'L',
        ];

        $month = now()->format('m');
        $yearCode = now()->format('y');
        $monthCode = $monthMapping[$month];

        $customers = McCustomer::whereNull('customer_code')->get();

        foreach ($customers as $customer) {
            $agent = McAgent::find($customer->agent_id);

            if (! $agent || ! filled($agent->agent_code)) {
                $this->warn("Lewati ID {$customer->id} karena agent tidak valid.");
                continue;
            }

            $prefix = "{$agent->agent_code}{$yearCode}{$monthCode}";

            $lastCustomer = McCustomer::where('customer_code', 'like', "{$prefix}%")
                ->orderByDesc('customer_code')
                ->first();

            $nextSequence = 1;
            if ($lastCustomer && preg_match('/(\d{4})$/', $lastCustomer->customer_code, $match)) {
                $nextSequence = (int) $match[1] + 1;
            }

            $sequenceFormatted = str_pad($nextSequence, 4, '0', STR_PAD_LEFT);
            $finalCode = "{$prefix}{$sequenceFormatted}";

            DB::table('mc_customers')
                ->where('id', $customer->id)
                ->update(['customer_code' => $finalCode]);

            $this->info("Updated ID {$customer->id} => {$finalCode}");
        }

        $this->info("Selesai memproses customer.");
        return Command::SUCCESS;
    }
}
