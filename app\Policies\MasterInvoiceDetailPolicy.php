<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\MasterInvoiceDetail;
use App\Models\User;

class MasterInvoiceDetailPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MasterInvoiceDetail $masterinvoicedetail): bool
    {
        return $user->checkPermissionTo('view MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MasterInvoiceDetail $masterinvoicedetail): bool
    {
        return $user->checkPermissionTo('update MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MasterInvoiceDetail $masterinvoicedetail): bool
    {
        return $user->checkPermissionTo('delete MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MasterInvoiceDetail $masterinvoicedetail): bool
    {
        return $user->checkPermissionTo('restore MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, MasterInvoiceDetail $masterinvoicedetail): bool
    {
        return $user->checkPermissionTo('replicate MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MasterInvoiceDetail $masterinvoicedetail): bool
    {
        return $user->checkPermissionTo('force-delete MasterInvoiceDetail');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any MasterInvoiceDetail');
    }
}
