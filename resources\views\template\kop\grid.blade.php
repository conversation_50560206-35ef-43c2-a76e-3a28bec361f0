{{--
	align-items-dynamic
--}}

@php
	//data

	//style
	$font = \App\Models\Font::where('name', 'Poppins')->first();
	$verticalAlign = 'items-center';
	$logo_position = 'logo-left';
	$horizontalAlign = 'text-start';
	$sizeCompanyClasses = 'text-xl';
	$heading_color = 'red';
	$heading_weight = 'bold';
	$show_address = true;
	$invoiceTitling = 'header';
	$sizeInvoiceClasses = 'text-xl';
	$invoiceTitleWeight = 'bold';
	$invoiceNumberWeight = 'bold';
	$titleOnly = false;
	$single_line_spacer = true;
	$double_line_spacer = true;
	$flexOrder = $logo_position === 'logo-right' ? ['company-info', 'logo'] : ['logo', 'company-info'];
@endphp

<div class="flex flex-row {{ $verticalAlign }} gap-2">
	@foreach($flexOrder as $item)
		@if($item === 'logo')
			<div class="p-1 flex items-center justify-center gap-2">
				<img src="{{ asset('images/alur.png') }}" alt="Company Logo" style="max-width: 180px; max-height: 100px;">
			</div>
		@elseif($item === 'company-info')
			<div class="{{ $horizontalAlign }} flex-grow">
				<span class="{{ $sizeCompanyClasses }} mt-2" style="color: {{ $heading_color }}; font-weight: {{ $heading_weight }};">
					Company Name
				</span>

				@if($show_address)
					<ul class="text-xs text-gray-700 leading-snug mt-1 space-y-0 max-w-xs">
						<li>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum</li>
					</ul>
				@endif
			</div>

			@if ($invoiceTitling === 'header')
				<div>
					<table class="text-end">
						<tr class="text-end">
							<td>
								<span class="{{ $sizeInvoiceClasses }}" style="font-weight: {{ $invoiceTitleWeight }};">
									Invoice
								</span>
							</td>
						</tr>
						@if (!$titleOnly)
							<tr class="text-end">
								<td>
									<span class="text-end" style="font-weight: {{ $invoiceNumberWeight }};">
										106/V/RGT-TB/2025
									</span>
								</td>
							</tr>
						@endif
					</table>
				</div>
			@endif
		@endif
	@endforeach
</div>
@if($single_line_spacer)
	<div style="width: 100%; border-top: 3px solid #555; margin-top: 10px;"></div>
	@if($double_line_spacer)
	<div style="width: 100%; border-top: 2px solid #555; margin-top: 3px;"></div>
	@endif
@endif
<span>Hello</span>

