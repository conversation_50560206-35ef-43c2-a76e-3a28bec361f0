@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];

	// Footer visibility check
	$showFooter = $layoutConfig['showFooter'] ?? false;

	// Footer configuration (following preview pattern)
	$footerContent = $layoutConfig['footerContent'] ?? 'This is a digitally generated invoice, no authorization signature is required';
	$footerAlign = $layoutConfig['footerAlign'] ?? 'justify-center';
	$footerFontSize = $layoutConfig['footerFontSize'] ?? 11; // px value
	$footerFontWeight = $layoutConfig['footerFontWeight'] ?? 'font-normal';
	$footerFontFam = $layoutConfig['footerFontFam'] ?? 'inherit';
	$footerBorder = $layoutConfig['footerBorder'] ?? false;
	$footerDecor = $layoutConfig['footerDecor'] ?? 'none'; // decorative options

	// Font family mapping
	$fontFamilyStyle = match($footerFontFam) {
		'Courier New' => "'Courier New', monospace",
		'Arial' => "'Arial', sans-serif",
		'Times New Roman' => "'Times New Roman', serif",
		'Helvetica' => "'Helvetica', sans-serif",
		default => 'inherit'
	};

	// Decorative classes
	$decorativeClass = match($footerDecor) {
		'italic' => 'italic',
		'underline' => 'underline',
		'line-through' => 'line-through',
		default => ''
	};
@endphp

{{-- Footer Section - Always at bottom of page --}}
@if($showFooter)
	<div class="footer-section fixed bottom-0 left-0 right-0 {{ $footerBorder ? 'border-t border-gray-300 pt-2' : '' }} bg-white z-10"
		 style="font-family: {{ $fontFamilyStyle }}; font-size: {{ $footerFontSize }}px;">
		<p class="{{ $footerFontWeight }} {{ $decorativeClass }} text-gray-500 w-full px-4 py-2 {{ str_replace('justify-', 'text-', $footerAlign) }}">
			{{ $footerContent }}
		</p>
	</div>
@endif
