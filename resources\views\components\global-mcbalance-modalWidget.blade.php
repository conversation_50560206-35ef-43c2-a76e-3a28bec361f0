<div class='space-y-3'>
    <div
        class='flex justify-between items-center gap-2 px-4 py-2 bg-gray-50 dark:bg-transparent border dark:border-gray-600 rounded'>
        <div class='flex flex-col'>
            <span class='text-xs'>Customer</span>
            <span class='font-bold text-lg'>{{ $record->customer_code }} | {{ $record->name }}</span>
        </div>
    </div>
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
		<div class="col-span-1 lg:col-span-2 space-y-2">
			<x-filament::section>
				<x-slot name="heading">
					Transaction Summary
				</x-slot>
				<x-slot name="description">
					Transaction summary for this account.
				</x-slot>
				<table class='w-full mb-6 text-xs'>
					<tbody>
						<tr class="border-b py-4">
							<td class="text-start py-2 px-4 text-gray-400">Updated</td>
							<td class="text-end py-2 px-4">{{ today()->format('d F Y') }}</td>
						</tr>
						<tr class="border-t border-b">
							<td class="text-start py-2 px-4 text-gray-400 align-top">Last Order</td>
							@if ($lastOrderAt == 'No Data')
								<td class="text-end py-2 px-4 flex flex-col ">
									<span>-</span>
								</td>
							@else
								<td class="text-end py-2 px-4 flex flex-col ">
									<span>{{ $lastOrderAt }}</span>
									<span>IDR {{ $lastOrder }}</span>
								</td>
							@endif
						</tr>
						<tr class="border-t border-b">
							<td class="text-start py-2 px-4 text-gray-400 align-top">Last Deposit/Payment</td>
							@if ($lastPaymentAt == 'No Data')
							<td class="text-end py-2 px-4 flex flex-col ">
								<span>-</span>
							</td>
							@else
								<td class="text-end py-2 px-4 flex flex-col ">
									<span>{{ $lastPaymentAt }}</span>
									<span>IDR {{ $lastPayment }}</span>
								</td>
							@endif
						</tr>
						<tr class="border-t">
							<td class="text-start py-2 px-4 text-gray-400 align-top">Balance</td>
							<td class="text-end py-2 px-4 flex flex-col ">
								@php
									$balanceColor = ($balance ?? 0) >= 0 ? 'text-success-500' : 'text-danger-500';
								@endphp
								<span class="font-bold {{ $balanceColor }}">IDR {{ $balance }}</span>
							</td>
						</tr>
					</tbody>
				</table>
			</x-filament::section>
		</div>
		<div class="col-span-1 lg:col-span-3 space-y-2">
			<x-filament::section>
				<x-slot name="heading">
					Transaction History
				</x-slot>
				<x-slot name="description">
					Last 7 transactions.
				</x-slot>
				@if ($transactions->count() == 0)
					<p class="p-4 text-center">No transactions found.</p>
				@else
					<table class="w-full text-xs">
						<thead class="border">
							<th class="p-1">Date</th>
							<th class="p-1 border-s border-e">Type</th>
							<th class="p-1">Amount</th>
						</thead>
						<tbody>
							@foreach ($transactions as $trx)
								<tr class="border-t border-b">
									<td class="p-2 border-s">{{ $trx->slip_date->format('d F Y') ?? $trx->created_at->format('d F Y') }}</td>
									<td class="p-2 text-center border-s border-e flex justify-center">
										@php
											$color = match ($trx->trx_type) {
												'incoming' => 'success',
												'outgoing' => 'danger',
												'order' => 'warning',
											};
										@endphp
										<x-filament::badge size="sm" :color="$color">
											{{ $trx->trx_type }}
										</x-filament::badge>
									</td>
									<td class="p-2 text-end border-e">IDR {{ number_format($trx->amount, 2, ',', '.') }}</td>
								</tr>
							@endforeach
						</tbody>
					</table>
				@endif
			</x-filament::section>
		</div>
	</div>
</div>
