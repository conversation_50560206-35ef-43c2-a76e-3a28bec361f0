<?php

namespace App\Filament\Admin\Resources\CompanyTransactionResource\Pages;

use App\Filament\Admin\Resources\CompanyTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCompanyTransaction extends EditRecord
{
    protected static string $resource = CompanyTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
