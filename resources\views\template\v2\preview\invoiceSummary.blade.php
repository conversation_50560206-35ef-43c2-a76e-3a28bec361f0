<div class="w-full text-sm">
    <h4 class="text-sm font-medium mb-2 px-2">Summary</h4>
    <table class="w-full table-auto">
        <tbody>
			@if ($tableShowSummaryDetails)
            <tr>
                <td class="px-2 py-1">Total Items:</td>
                <td class="px-2 py-1 text-end">8</td>
            </tr>
            <tr>
                <td class="px-2 py-1">Total Quantity:</td>
                <td class="px-2 py-1 text-end">76</td>
            </tr>
			@endif
            <tr>
                <td class="px-2 py-1">Subtotal:</td>
                <td class="px-2 py-1 text-end">{{ $currencySymbol ?? '$' }} {{ $isIndonesianCompany ? '1,270,000' : '127,600.00' }}</td>
            </tr>
            <tr>
                <td class="px-2 py-1">Booking Fee:</td>
                <td class="px-2 py-1 text-end">{{ $currencySymbol ?? '$' }} {{ $isIndonesianCompany ? '300,000' : '30.00' }}</td>
            </tr>
            @if($isIndonesianCompany)
                <tr>
                    <td class="px-2 py-1">Rates:</td>
                    <td class="px-2 py-1 text-end">Rp. 157,000</td>
                </tr>
            @endif
            <tr>
                <td class="px-2 py-1 pt-3 border-t ">Invoice Total:</td>
                <td class="px-2 py-1 pt-3 text-end border-t " width="50%">{{ $currencySymbol ?? '$' }} {{ $isIndonesianCompany ? '1,727,000' : '1,914,450,000.00' }}</td>
            </tr>
			@if ($tableShowInword)
				<tr>
					<td class="px-2 py-1 pt-3 grid w-full" colspan="2">
						<span class="grow whitespace-nowrap me-2">{{ ($companyType ?? null) == 1 ? 'Amount in words' : 'Terbilang' }}: </span>
						<span class="font-bold">
							@if($isIndonesianCompany)
								Satu Juta Tujuh Ratus Dua Puluh Tujuh Ribu Rupiah
							@else
								One Billion Nine Hundred Fourteen Million Four Hundred Fifty Thousand Dollars
							@endif
						</span>
					</td>
				</tr>
			@endif
        </tbody>
    </table>
</div>


{{-- <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
	<div class="rounded-lg p-4 {{ $tableTextColorClass }}" style="background-color: {{ $tableSummaryBg }};"">
		<h4 class="text-sm font-medium mb-2">Summary</h4>
		<div class="space-y-1 text-sm">
			@if ($tableShowSummaryDetails)
				<div class="flex justify-between">
					<span class=""></span>
					<span class="font-medium">8</span>
				</div>
				<div class="flex justify-between">
					<span class="">:</span>
					<span class="font-medium">76</span>
				</div>
			@endif
			<div class="flex justify-between">
				<span class="">:</span>
				<span class="font-medium">$ 127,600.00</span>
			</div>
			<div class="flex justify-between">
				<span class="">:</span>
				<span class="font-medium">$ 30.00</span>
			</div>
			<div class="flex justify-between py-2 px-1 border-t border-gray-300">
				<span class="font-semibold ">:</span>
				<span class="font-bold">$ 1,914,450,000.00</span>
			</div>
			@if ($tableShowInword)
				<div class="flex justify-between pt-2">
					<span class="grow whitespace-nowrap me-2">{{ ($companyType ?? null) == 1 ? 'Amount in words' : 'Terbilang' }}: </span>
					<span class="flex-none grow-0 font-bold">Satu Miliar Sembilan Ratus Empat Belas Juta Empat Ratus Lima Puluh Ribu Rupiah</span>
				</div>
			@endif
		</div>
	</div>
</div> --}}
