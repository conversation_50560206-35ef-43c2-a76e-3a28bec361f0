<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_banks', function (Blueprint $table) {
			$table->boolean('include_in_invoice')->default(false)->after('IFSC');
			$table->boolean('is_default')->default(false)->after('IFSC');
            $table->json('custom_columns')->nullable()->after('IFSC');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_banks', function (Blueprint $table) {
            //
        });
    }
};
