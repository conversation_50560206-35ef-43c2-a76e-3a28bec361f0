<?php

namespace App\Filament\Admin\Resources\CompanyTransactionResource\RelationManagers;

use App\Models\Company;
use App\Models\CompanyBank;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;

class DepositsRelationManager extends RelationManager
{
    protected static string $relationship = 'deposits';
    protected ?string $heading = 'Custom Page Heading';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('company_id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('company.name')
			->modifyQueryUsing(function (Builder $query) {
				$query
					->whereDate('trx_date', today())
					->orderBy('trx_date', 'desc');
			})
            ->columns([
                TextColumn::make('trx_no')->searchable(),
                TextColumn::make('trx_date')->date(),
                TextColumn::make('bank.bank_name')->searchable(),
				TextColumn::make('trx_type')
					->badge()
					->color(fn ($state) => match ($state) {
						'in' => 'success',
						'out' => 'danger',
						'order' => 'warning',
					})
					->formatStateUsing(fn ($state) => match ($state) {
						'in' => 'Incoming',
						'out' => 'Outgoing',
						'order' => 'Order (Outgoing)',
					}),
                TextColumn::make('amount')
					->money('IDR')
					->searchable()
					->alignEnd()
					->summarize([
						Sum::make()
							->label('Total In')
							->query(fn ($query) => $query->where('trx_type', 'in')),

						Sum::make()
							->label('Total Out')
							->query(fn ($query) => $query->where('trx_type', 'out')),

						Sum::make()
							->label('Total Order')
							->query(fn ($query) => $query->where('trx_type', 'order')),
					]),
            ])
            ->filters([
				SelectFilter::make('bank_id')
					->label('Bank')
					->options(function () {
						$companyId = $this->getOwnerRecord()->id;
						return CompanyBank::query()
							->where('company_id', $companyId)
							->orderBy('bank_name')
							->get()
							->mapWithKeys(fn ($b) => [
								$b->id => (string) ($b->bank_name ?? '—')
							])
							->toArray();
					})
					->preload(),
                SelectFilter::make('trx_type')
                    ->options([
                        'in' => 'Incoming',
                        'out' => 'Outgoing',
                        'order' => 'Order (Outgoing)',
                    ]),
            ])
            // ->headerActions([
            //     Tables\Actions\CreateAction::make(),
            // ])
			->heading('Transaction History')
			->description('only show today transaction')
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
