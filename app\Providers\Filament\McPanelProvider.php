<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Navigation\NavigationItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class McPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('mc')
            ->path('mc')
            ->breadcrumbs(false)
            ->sidebarCollapsibleOnDesktop()
			->topNavigation()
			->brandLogo('/assets/logo/logolight.png')
			->darkModeBrandLogo('/assets/logo/logodark.png')
            ->viteTheme('resources/css/theme.css')
            ->colors([
				'gray' => Color::Gray,
				'primary' => Color::Sky, // lebih soft dari Indigo
				'info' => Color::Cyan,
				'success' => Color::Lime,
				'warning' => Color::Amber,
				'danger' => Color::Pink,
				'critical' => 'rgb(220, 38, 38)', // merah terang
			])
            ->discoverResources(in: app_path('Filament/Mc/Resources'), for: 'App\\Filament\\Mc\\Resources')
            ->discoverPages(in: app_path('Filament/Mc/Pages'), for: 'App\\Filament\\Mc\\Pages')
            ->pages([
                // Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Mc/Widgets'), for: 'App\\Filament\\Mc\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
			->userMenuItems([
				MenuItem::make()
					->label('Calculator (Ctrl+Shift+K)')
					->url('#simple-calculator')
                    ->icon('heroicon-o-calculator')
                    ->sort(0),
				MenuItem::make()
					->label('Main Panel')
					->url('/admin')
                    ->icon('heroicon-o-home')
                    ->visible(fn () => Auth::check() && Auth::user()->hasRole('Super Admin')),
			])
			->navigationItems([
                // NavigationItem::make('Main Panel')
                //     ->url('/admin')  // URL atau route yang dituju
                //     ->icon('heroicon-o-arrow-left-end-on-rectangle')
                //     ->sort(-3)
                //     ->visible(fn () => Auth::check() && Auth::user()->hasRole('Super Admin')),
            ]);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
}
