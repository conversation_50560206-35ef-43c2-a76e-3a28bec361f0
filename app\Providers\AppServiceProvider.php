<?php

namespace App\Providers;

use App\Filament\Search\GoogleSearchProvider;
use App\Models\User;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Laravel\Pulse\Facades\Pulse;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Policies\RolePolicy;
use App\Policies\PermissionPolicy;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Support\Facades\FilamentView;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class AppServiceProvider extends ServiceProvider
{
	/**
	 * Register any application services.
	 */
	public function register(): void
	{
		Gate::policy(Role::class, RolePolicy::class);
		Gate::policy(Permission::class, PermissionPolicy::class);
	}

	/**
	 * Bootstrap any application services.
	 */
	public function boot(): void
	{
		Schema::defaultStringLength(191);
		Gate::before(function (User $user, string $ability) {
			return $user->isSuperAdmin() ? true : null;
		});
		Gate::define('viewPulse', function (User $user) {
			return $user->isAdmin();
		});
		Pulse::user(fn($user) => [
			'name' => $user->name,
			'extra' => $user->email,
			'avatar' => $user->profile_photo_url,
		]);

		// Inject calculator modal into Filament panels
		Filament::serving(function () {
			$user = Auth::user();

			FilamentView::registerRenderHook('panels::body.end', function () {
				$components = [];
				$panel = Filament::getCurrentPanel();

				// Modal selalu tersedia
				$components[] = Blade::render('<x-simple-calculator-modal />');
				$components[] = Blade::render('<x-global-ocr-modal />');

				if (in_array($panel?->getId(), ['admin'])) {
					$components[] = Blade::render('<x-global-agent-modal />');
					$components[] = Blade::render('<x-global-company-modal />');
					$components[] = Blade::render('<x-global-deposit-modal />');
					$components[] = Blade::render('<x-global-business-modal />');

					// Tambah jika user punya akses tertentu
					// if (Auth::user()->can('view_any', \App\Models\Order::class)) {
					// }

					if (Auth::user()->hasAnyRole(['admin', 'Super Admin'])) {
						$components[] = Blade::render('<x-global-report-modal />');
						$components[] = Blade::render('<x-global-order-modal />');
					}
					if (Auth::user()->hasAnyRole(['admin', 'Super Admin'])) {
						$components[] = Blade::render('<x-global-closing-modal />');
					}

				}

				if (in_array($panel?->getId(), ['mc'])) {
					/*
						mc agent
						mc bank
						mc customer
						mc deposit
						mc order
						mc payment
					*/
					$components[] = Blade::render('<x-global-mcorder-modal />');
					$components[] = Blade::render('<x-global-mcpayment-modal />');
				}

				return implode("\n", $components);
			});
		});

		Carbon::setLocale(Config::get('app.locale'));
		date_default_timezone_set(config('app.timezone'));
	}
}
