<!DOCTYPE html>
<html lang="en" class="root-text-sm">

<head>
	@include('invoice.metatitle')
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet"
		integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="{{ $payload['invoice']->company->font->source ?? 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap' }}" rel="stylesheet">

	@include('invoice.css')
	<style>
        .invoice-line {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .invoice-line::before,
        .invoice-line::after {
            content: "";
            flex-grow: 1;
            border-bottom: 20px solid #000;
            margin: 0 10px;
        }
        .text-start .invoice-line::before { flex-grow: 0; }
        .text-end .invoice-line::after { flex-grow: 0; }

		.bill-to {
            padding: 15px;
            /* border: 1px solid #000; */
            margin-top: 20px;
        }

        .inwords{
            padding: 3px;
            border: 1px solid #afafaf;
            display: block;
        }
	</style>
</head>

<body style="font-family: {{$payload['invoice']->company->font->name ?? ''}} !important">
	<div class="main">
		<div class="px-3">
           @include('template.kop.' . ($payload['invoice']->company->templateheading ?? 'left'))
		</div>
        <hr class="mt-0">
		<div class="px-3 mt-3">
            @include('template.billto.'.($payload['invoice']->company->templatebillto ?? 'justified'))
		</div>

        <div class="px-3">
            @include('template.table.'.($payload['invoice']->company->templatetable ?? '4-columns'))
        </div>

        <div class="px-3">
            @include('template.inwords.'.($payload['invoice']->company->templateinword ?? 'left'))
        </div>

        <div class="px-3">
            <div class="row-mb-5 col-12">
				<div class="mb-3" style="font-style: italic">
					<span class="text-start">
						Ensure payment is made within 14 calendar days from the invoice date.
					</span>
				</div>
            </div>
        </div>

        <div class="px-3">
            <div class="row mb-5 d-flex justify-content-between">
                <div class="col-5">
                    @include('template.bankinfo.'.($payload['invoice']->company->templatebankinfo ?? 'model-3-nobg-noborder'))
                </div>
                <div class="col-5 text-center">
                    @if($payload['invoice']->company->signature)
                        <img src="{{ url('storage/' . $payload['invoice']->company->signature) }}" class="mx-2 mt-1 profile-image ml-auto" style="max-height:5rem"><br>
                    @endif
                    @if ($payload['invoice']->company->signature_name)
                        <u>{{$payload['invoice']->company->signature_name}}</u>
                    @endif
                </div>
            </div>
        </div>
		{{-- <div class="pagebreak"></div> --}}
		<div class="px-3">
			<div class="row-mb-5 col-12">
				@if ($payload['invoice']->remarks)
			        <hr>
					<div>
						<span class="fw-bold d-block">Remarks</span>
						<p class="m-0 text-sm">{!!$payload['invoice']->remarks!!}</p>
					</div>
				@endif
			</div>
			<hr>
		</div>
		<div class="footer">
			<div class="container mt-5">
				<div style="margin: 0 auto; text-align: center;">
					<div class="col-12" style="font-family: \'Courier New\', monospace; font-size: 11px;">
						<span class="text-center" style="font-size: 10px;">
							This is a digitally generated invoice, no authorization signature is required
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous">
	</script>
<script>
</script>
</body>

</html>
