<div x-cloak x-data="simpleCalculator()" x-init="init()" @open-calculator.window="openModal()"
    class="simple-calculator-modal">
    {{-- Modal Backdrop --}}
    <div x-show="open" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
        class="fixed inset-0 z-50 flex items-center justify-center p-4" style="display: none;">
        {{-- Backdrop --}}
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" @click="close()"></div>

        {{-- Modal Content --}}
        <div x-show="open" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-95" @keydown.escape.window="close()"
            class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 w-full max-w-md mx-auto z-60">
            {{-- Header --}}
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                        </path>
                    </svg>
                    Simple Calculator
                </h2>
                <button @click="close()"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            {{-- Calculator Form --}}
            <div class="space-y-4">
                {{-- Rate Field --}}
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Rate
                    </label>
                    <div class="flex gap-2 items-center">
                        <input type="radio" name="targetField" value="rate" x-model="targetField" />
                        <input x-ref="rate" type="text" x-model="rateDisplay"
                            :disabled="targetField === 'rate'" @focus="activeField = 'rate'; $event.target.select()"
                            @input="updateRate($event.target.value)" @blur="formatRate()" placeholder="Enter rate..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-right disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:cursor-not-allowed" />

                    </div>
                </div>

                {{-- Amount Field --}}
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Amount
                    </label>
                    <div class="flex gap-2 items-center">
                        <input type="radio" name="targetField" value="amount" x-model="targetField" />
                        <input x-ref="amount" type="text" x-model="amountDisplay"
                            :disabled="targetField === 'amount'"
                            @focus="activeField = 'amount'; $event.target.select()"
                            @input="updateAmount($event.target.value)" @blur="formatAmount()"
                            placeholder="Enter amount..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-right disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:cursor-not-allowed" />
                    </div>
                </div>

                {{-- Fee Field --}}
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Fee
                    </label>
                    <div class="flex gap-2 items-center">
                        <input type="radio" name="targetField" value="fee" x-model="targetField" />
                        <input x-ref="fee" type="text" x-model="feeDisplay" :disabled="targetField === 'fee'"
                            @focus="activeField = 'fee'; $event.target.select()" @input="updateFee($event.target.value)"
                            @blur="formatFee()" placeholder="Enter fee..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-right disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:cursor-not-allowed" />
                    </div>
                </div>

                {{-- Total Field --}}
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Total
                    </label>
                    <div class="flex gap-2 items-center">
                        <input type="radio" name="targetField" value="total" x-model="targetField" />
                        <input x-ref="total" type="text" x-model="totalDisplay"
                            :disabled="targetField === 'total'"
                            @focus="activeField = 'total'; $event.target.select()"
                            @input="updateTotal($event.target.value)" @blur="formatTotal()"
                            placeholder="Enter total..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-right disabled:bg-gray-100 dark:disabled:bg-gray-800 disabled:cursor-not-allowed" />
                    </div>
                </div>

                {{-- Calculation Info --}}
                <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <p class="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                        <strong>Tip:</strong> Select one field to calculate automatically, then fill in the remaining
                        fields.
                        <br>
                        <strong>Example:</strong> Select <em>Total</em>, then enter values for <em>Rate</em>,
                        <em>Amount</em>, and <em>Fee</em>.
                        <br>
                        <strong>Shortcut:</strong> <kbd
                            class="px-1 py-0.5 bg-white dark:bg-gray-700 rounded text-xs">Ctrl+Shift+K</kbd> to open
                        the calculator from anywhere.
                    </p>
                </div>

            </div>

            {{-- Footer --}}
            <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button @click="clearAll()"
                    class="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors">
                    Clear All
                </button>
                <button @click="close()"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Close
                </button>
            </div>
        </div>
    </div>

    {{-- Alpine.js Component Logic --}}
    <script>
        function simpleCalculator() {
            return {
                open: false,
                rate: null,
                amount: null,
                fee: null,
                total: null,
                rateDisplay: '',
                amountDisplay: '',
                feeDisplay: '',
                totalDisplay: '',
                activeField: null,
                targetField: 'total',

                init() {
                    // Global keyboard shortcut: Ctrl/Cmd + Shift + K
                    document.addEventListener('keydown', (e) => {
                        const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
                        const metaKey = isMac ? e.metaKey : e.ctrlKey;

                        if (metaKey && e.altKey && e.key.toLowerCase() === 'k') {
                            e.preventDefault();
                            this.openModal();
                        }
                    });

                    // Listen for navigation clicks
                    document.addEventListener('click', (e) => {
                        if (e.target.closest('a[href="#simple-calculator"]')) {
                            e.preventDefault();
                            this.openModal();
                        }
                    });
                },

                // Format number with thousand separators
                formatNumber(num) {
                    if (num === null || num === '' || isNaN(num)) return '';
                    return parseFloat(num).toLocaleString('id-ID', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                },

                // Parse formatted number back to float
                parseNumber(str) {
                    if (!str) return null;
                    // Remove thousand separators and convert comma to dot for decimal
                    const cleaned = str.replace(/\./g, '').replace(',', '.');
                    const num = parseFloat(cleaned);
                    return isNaN(num) ? null : num;
                },

                updateRate(value) {
                    this.rate = this.parseNumber(value);
                    this.compute();
                },

                updateAmount(value) {
                    this.amount = this.parseNumber(value);
                    this.compute();
                },

                updateFee(value) {
                    this.fee = this.parseNumber(value);
                    this.compute();
                },

                updateTotal(value) {
                    this.total = this.parseNumber(value);
                    this.compute();
                },

                formatRate() {
                    this.rateDisplay = this.formatNumber(this.rate);
                },

                formatAmount() {
                    this.amountDisplay = this.formatNumber(this.amount);
                },

                formatFee() {
                    this.feeDisplay = this.formatNumber(this.fee);
                },

                formatTotal() {
                    this.totalDisplay = this.formatNumber(this.total);
                },

                compute() {
                    const rate = this.rate ?? 0;
                    const amount = this.amount ?? 0;
                    const fee = this.fee ?? 0;
                    const total = this.total ?? 0;

                    switch (this.targetField) {
                        case 'total':
                            if (!isNaN(rate) && !isNaN(amount)) {
                                const b = amount - fee;
                                this.total = parseFloat((rate * b).toFixed(2));
                                this.totalDisplay = this.formatNumber(this.total);
                            }
                            break;

                        case 'rate':
                            if (!isNaN(amount) && !isNaN(fee) && (amount - fee) !== 0) {
                                const b = amount - fee;
                                this.rate = parseFloat((total / b).toFixed(2));
                                this.rateDisplay = this.formatNumber(this.rate);
                            }
                            break;

                        case 'amount':
                            if (!isNaN(rate) && rate !== 0) {
                                const b = total / rate;
                                this.amount = parseFloat((b + fee).toFixed(2));
                                this.amountDisplay = this.formatNumber(this.amount);
                            }
                            break;

                        case 'fee':
                            if (!isNaN(rate) && rate !== 0) {
                                const b = total / rate;
                                this.fee = parseFloat((amount - b).toFixed(2));
                                this.feeDisplay = this.formatNumber(this.fee);
                            }
                            break;
                    }
                },


                openModal() {
                    this.open = true;
                    this.clearAll();
                    this.targetField = 'total';
                    this.activeField = 'rate';

                    // Focus on rate field after modal opens
                    this.$nextTick(() => {
                        if (this.$refs.rate) {
                            this.$refs.rate.focus();
                        }
                    });
                },

                close() {
                    this.open = false;
                    this.activeField = null;
                },

                clearAll() {
                    this.rate = null;
                    this.amount = null;
                    this.fee = null;
                    this.total = null;
                    this.rateDisplay = '';
                    this.amountDisplay = '';
                    this.feeDisplay = '';
                    this.totalDisplay = '';
                    this.activeField = null;
                }
            }
        }
    </script>
</div>

{{-- Custom Styles --}}
<style>
    [x-cloak] {
        display: none !important;
    }

    .simple-calculator-modal kbd {
        font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
        font-size: 0.75rem;
        font-weight: 600;
        border: 1px solid;
        border-radius: 0.25rem;
        padding: 0.125rem 0.25rem;
    }
</style>
