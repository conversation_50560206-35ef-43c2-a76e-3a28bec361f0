<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HealthCheckSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'check_name',
        'is_enabled',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_enabled' => 'boolean',
    ];

    /**
     * Get all enabled check names.
     *
     * @return array
     */
    public static function getEnabledChecks(): array
    {
        return self::where('is_enabled', true)
            ->pluck('check_name')
            ->toArray();
    }

    /**
     * Check if a specific check is enabled.
     *
     * @param string $checkName
     * @return bool
     */
    public static function isCheckEnabled(string $checkName): bool
    {
        return self::where('check_name', $checkName)
            ->where('is_enabled', true)
            ->exists();
    }

    /**
     * Toggle the status of a check.
     *
     * @param string $checkName
     * @return bool
     */
    public static function toggleCheck(string $checkName): bool
    {
        $setting = self::where('check_name', $checkName)->first();
        
        if (!$setting) {
            return false;
        }
        
        $setting->is_enabled = !$setting->is_enabled;
        $setting->save();
        
        return $setting->is_enabled;
    }
}
