<div x-cloak x-data="globalDepositModal()" x-init="init()" @open-deposit-modal.window="openModal()"
	@close-modal.window="close()"
    class="global-deposit-modal">
    <div x-show="open" x-transition class="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto" style="display: none;">
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" @click="close()"></div>

        <div x-show="open"
            x-transition
            @keydown.escape.window="close()"
            class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 w-full max-w-4xl mx-auto z-60 overflow-y-auto max-h-[90vh]"
        >
            {{-- Livewire Deposit Create Form --}}
            @livewire(\App\Filament\Admin\Resources\CompanyDepositoResource\Pages\CreateCompanyDeposito::class)
			<span class="text-xs italic text-indigo-500">Warning! Redirecting to the dashboard upon successful submission</span>
        </div>
    </div>
</div>
<script>
    function globalDepositModal() {
        return {
            open: false,
            init() {
                // Shortcut: Ctrl+Shift+O
                document.addEventListener('keydown', (e) => {
                    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
                    const metaKey = isMac ? e.metaKey : e.ctrlKey;

					//alfabet
                    if (metaKey && e.altKey && e.key.toLowerCase() === 'd') {
                        e.preventDefault();
                        this.openModal();
                    }
                });

                // From menu click
                document.addEventListener('click', (e) => {
                    if (e.target.closest('a[href="#deposit-modal"]')) {
                        e.preventDefault();
                        this.openModal();
                    }
                });
            },
            openModal() {
                this.open = true;
            },
            close() {
                this.open = false;
            }
        }
    }
</script>
