<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use TomatoPHP\FilamentApi\Traits\InteractWithAPI;

class ListUsers extends ListRecords
{
    // use InteractWithAPI;
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public static function getFilamentAPISlug(): ?string
    {
        return 'users';
    }

    public static function getFilamentAPIMiddleware(): array
    {
        return [];
    }
}
