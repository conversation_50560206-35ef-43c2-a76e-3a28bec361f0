<?php

namespace App\Filament\Mc\Resources\McDepositResource\Pages;

use App\Filament\Mc\Resources\McDepositResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateMcDeposit extends CreateRecord
{
    protected static string $resource = McDepositResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	public function getHeading(): string
	{
		return 'New Deposit';
	}
}
