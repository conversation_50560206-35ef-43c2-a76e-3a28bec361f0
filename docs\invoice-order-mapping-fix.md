# Perbaikan Mapping Order ke Invoice

## Masalah yang Ditemukan

Terdapat kesalahan pemahaman dalam struktur database yang menyebabkan mapping yang salah antara Order dan Invoice:

### Masalah Awal:
- `company_id` di tabel `orders` seharusnya adalah `client_id` di tabel `invoices`
- `client_id` yang dipilih saat membuat invoice seharusnya menjadi `company_id` di tabel `invoices`

### Dampak Masalah:
1. Invoice dibuat dengan company dan client yang terbalik
2. Bank information diambil dari company yang salah
3. Business type untuk template invoice details diambil dari company yang salah
4. Invoice details dibuat dengan company_id dan client_id yang salah

## Perbaikan yang Dilakukan

### 1. File: `app\Filament\Admin\Resources\OrderResource\Pages\ListOrders.php`

#### Perubahan di Action "Create Invoice":

**Sebelum:**
```php
$client = Company::find($record->company_id);
$company = Company::find($data['client_id']);

$bank = CompanyBank::where('company_id', $record->company_id)->where('is_default', true)->first();

$invoice = \App\Models\Invoice::create([
    'company_id' => $data['company_id'], // SALAH
    'client_id' => $record->company_id,  // BENAR
]);

$invoice->invoiceDetails()->create([
    'company_id' => $record->company_id, // SALAH
    'client_id' => $data['client_id'],   // SALAH
]);
```

**Sesudah:**
```php
$client = Company::find($record->company_id); // Client dari order
$company = Company::find($data['client_id']); // Company yang dipilih

$bank = CompanyBank::where('company_id', $data['client_id'])->where('is_default', true)->first();

$invoice = \App\Models\Invoice::create([
    'company_id' => $data['client_id'],  // Company yang dipilih
    'client_id' => $record->company_id,  // Client dari order
]);

$invoice->invoiceDetails()->create([
    'company_id' => $data['client_id'],  // Company yang dipilih
    'client_id' => $record->company_id,  // Client dari order
]);
```

### 2. File: `app\Filament\Admin\Resources\OrderResource.php`

#### Perubahan Label untuk Clarity:

**Sebelum:**
```php
Select::make('company_id')
    ->label('Select Company')
```

**Sesudah:**
```php
Select::make('company_id')
    ->label('Select Client')
    ->helperText('Note: This will become the client in the invoice')
```

### 3. Perubahan Label di Form Invoice Creation:

**Sebelum:**
```php
Select::make('client_id')
    ->label('Select Client')
```

**Sesudah:**
```php
Select::make('client_id')
    ->label('Select Company')
    ->helperText('Note: This will become the company issuing the invoice')
```

## Penjelasan Logika yang Benar

### Order:
- `company_id` di order = Client yang akan menerima invoice
- Tipe company: `type = 2` (Client)

### Invoice Creation:
- User memilih Company yang akan mengeluarkan invoice (`type = 1`)
- `company_id` di invoice = Company yang dipilih (pengeluaran invoice)
- `client_id` di invoice = `company_id` dari order (penerima invoice)

### Business Logic:
1. **Template Invoice Details**: Diambil berdasarkan `business_type` dari company yang mengeluarkan invoice (sama seperti bank information)
2. **Bank Information**: Diambil dari company yang mengeluarkan invoice
3. **Client Address**: Diambil dari client (company_id di order)

### Query Logic:
```php
// Untuk Bank Information (ada default)
$bank = CompanyBank::where('company_id', $data['client_id'])->where('is_default', true)->first();

// Untuk Invoice Details (tidak ada default, ambil semua)
$detail = MasterInvoiceDetail::where('business_type', $company->business_type)->get();
```

## Validasi Perbaikan

Setelah perbaikan ini:
1. ✅ Invoice dibuat dengan company dan client yang benar
2. ✅ Bank information diambil dari company yang benar (yang mengeluarkan invoice)
3. ✅ Business type untuk template diambil dari company yang benar (yang mengeluarkan invoice)
4. ✅ Invoice details dibuat dengan mapping yang benar
5. ✅ Client address diambil dari client yang benar

## Catatan Penting

- Database structure tidak perlu diubah
- Hanya logic mapping yang diperbaiki
- Relasi model tetap sama
- Perubahan hanya pada level aplikasi
