<?php

namespace App\Filament\Admin\Pages;

use App\Models\Company;
use App\Models\McAgent;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Action;
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class Report extends Page
{
    protected static ?string $navigationIcon = 'icon-file-medical';
    protected static ?string $navigationLabel = 'Reports';
    protected static ?string $title = 'Reports';
    protected static ?int $navigationSort = 4;
    protected static string $view = 'filament.admin.pages.report';
	protected static bool $shouldRegisterNavigation = false;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-sm italic">Generate Reports to PDF or XLSX</span>');
    }

    public ?array $formData = [];

    public function form(Form $form): Form
    {
        return $form
            ->schema([
				Section::make()
					->columnSpanFull()
					->schema([
						Select::make('report_type')
							->label('Select Report')
							->reactive()
							->options($this->getReportOptions())
							->required(),

						DatePicker::make('start_date')
							->reactive()
							->label(fn ($get) => Str::contains($get('report_type'), 'dailyBankStatement') ? 'Transaction Date' : 'Start Date')
							// ->hidden(fn ($get) => Str::contains($get('report_type'), 'grossProfitInvoice'))
							->required()
							->default(today())
							->helperText('Leave blank to display unfiltered data'),

						DatePicker::make('end_date')
							->label('To date')
							->hidden(fn ($get) => Str::contains($get('report_type'), 'dailyBankStatement'))
							->reactive()
							->required()
							->default(today())
							->helperText('Leave blank to display unfiltered data'),

						Select::make('agent_id')
							->visible(fn ($get) => Str::contains($get('report_type'), 'dailyOrderProfit'))
							->label('Agent')
							->reactive()
							->multiple()
							->options(fn () => McAgent::pluck('name', 'id'))
							->helperText('Leave blank to display unfiltered data'),

						Select::make('company_id')
							->label('Company')
							->visible(fn ($get) =>
								Str::contains($get('report_type'), 'grossProfitInvoice')
								|| Str::contains($get('report_type'), 'viewOrderPerformance')
							)
							->reactive()
							->options(fn () => Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id')),

						Actions::make([
							ActionsAction::make('generate')
								->label('View Report')
								->color('primary')
								->icon('heroicon-o-printer')
								->requiresConfirmation(false)
								->url(function ($get) {
									return route($get('report_type'), [
										'start_date' => $get('start_date'),
										'end_date' => $get('end_date'),
										'agent_id' => $get('agent_id'),
										'company_id' => $get('company_id'),
									]);
								})
								->openUrlInNewTab()
								->visible(fn ($get) => Route::has($get('report_type') ?? null)),
						])->extraAttributes(['class' => 'items-end'])
					])
            ])
            ->statePath('formData');
    }

	public function redirectToReport()
	{
		$data = $this->formData;

		$routeName = $data['report_type'] ?? [];

		if (!Route::has($routeName)) {
			abort(404, 'Report route not found.');
		}
		$url = route($routeName, [
			'start_date' => $data['start_date'] ?? null,
			'end_date' => $data['end_date'] ?? null,
			'agent_id' => $data['agent_id'] ?? null,
			'company_id' => $data['company_id'] ?? null,
		]);
		return redirect()->away($url);
	}


	protected function getReportOptions(): array
	{
		return collect(Route::getRoutes())
			->filter(fn ($route) => str_starts_with($route->getName(), 'report.'))
			->mapWithKeys(function ($route) {
				$name = $route->getName();
				$baseName = str_replace('report.', '', $name);
				$label = Str::headline($baseName);
				return [$name => $label];
			})
			->toArray();
	}

}
