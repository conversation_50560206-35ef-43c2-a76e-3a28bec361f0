# CompanyDeposito Deletion Handling - Implementation Example

## 🛡️ **Improved Protection Strategy**

Setelah feedback, implementasi telah diperbaiki dari **throw exception** menjadi **graceful handling** dengan user experience yang lebih baik.

## 🔧 **Model Implementation**

### **CompanyDeposito Model** (`app/Models/CompanyDeposito.php`)

```php
protected static function boot()
{
    parent::boot();

    // Prevent manual deletion of order-related deposito
    static::deleting(function ($deposito) {
        // Check if this deposito is related to an order
        if ($deposito->order_id) {
            // Prevent deletion by returning false
            return false;
        }
    });
}

/**
 * Check if this deposito can be deleted manually
 */
public function canBeDeleted(): bool
{
    return is_null($this->order_id);
}

/**
 * Get deletion prevention message
 */
public function getDeletionPreventionMessage(): string
{
    if ($this->canBeDeleted()) {
        return '';
    }

    $orderNo = $this->order ? $this->order->order_no : $this->order_id;
    return "Deposito ini terkait dengan Order #{$orderNo}. Untuk menghapus deposito, hapus atau edit Order terkait.";
}
```

## 🎯 **Usage Examples**

### **1. In Filament Resource (Recommended)**

```php
// In CompanyDepositoResource.php

public static function table(Table $table): Table
{
    return $table
        ->columns([
            // ... your columns
        ])
        ->actions([
            Tables\Actions\DeleteAction::make()
                ->before(function (CompanyDeposito $record) {
                    // Check if deletion is allowed
                    if (!$record->canBeDeleted()) {
                        // Show notification and cancel action
                        Notification::make()
                            ->title('Tidak dapat menghapus deposito')
                            ->body($record->getDeletionPreventionMessage())
                            ->warning()
                            ->send();
                        
                        // Cancel the deletion
                        return false;
                    }
                }),
        ]);
}
```

### **2. In Controller**

```php
// In CompanyDepositoController.php

public function destroy(CompanyDeposito $deposito)
{
    // Check if deletion is allowed
    if (!$deposito->canBeDeleted()) {
        return response()->json([
            'success' => false,
            'message' => $deposito->getDeletionPreventionMessage()
        ], 422);
    }

    // Proceed with deletion
    $deposito->delete();

    return response()->json([
        'success' => true,
        'message' => 'Deposito berhasil dihapus'
    ]);
}
```

### **3. In Blade Template**

```blade
{{-- In deposito list view --}}
@foreach($depositos as $deposito)
    <tr>
        <td>{{ $deposito->description }}</td>
        <td>{{ $deposito->amount }}</td>
        <td>
            @if($deposito->canBeDeleted())
                <button class="btn btn-danger" onclick="deleteDeposito({{ $deposito->id }})">
                    Hapus
                </button>
            @else
                <button class="btn btn-secondary" disabled 
                        title="{{ $deposito->getDeletionPreventionMessage() }}">
                    Tidak dapat dihapus
                </button>
            @endif
        </td>
    </tr>
@endforeach
```

### **4. In API Response**

```php
// In API Resource or Controller

public function show(CompanyDeposito $deposito)
{
    return [
        'id' => $deposito->id,
        'description' => $deposito->description,
        'amount' => $deposito->amount,
        'can_be_deleted' => $deposito->canBeDeleted(),
        'deletion_message' => $deposito->getDeletionPreventionMessage(),
        // ... other fields
    ];
}
```

## 🎨 **Frontend Implementation Examples**

### **JavaScript Handling**

```javascript
function deleteDeposito(depositoId) {
    // First check if deletion is allowed
    fetch(`/api/depositos/${depositoId}`)
        .then(response => response.json())
        .then(data => {
            if (!data.can_be_deleted) {
                // Show warning message
                alert(data.deletion_message);
                return;
            }
            
            // Proceed with deletion confirmation
            if (confirm('Apakah Anda yakin ingin menghapus deposito ini?')) {
                // Perform deletion
                fetch(`/api/depositos/${depositoId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        location.reload();
                    } else {
                        alert(result.message);
                    }
                });
            }
        });
}
```

### **Vue.js Component Example**

```vue
<template>
    <div>
        <button 
            v-if="deposito.can_be_deleted"
            @click="deleteDeposito"
            class="btn btn-danger"
        >
            Hapus
        </button>
        <button 
            v-else
            :title="deposito.deletion_message"
            class="btn btn-secondary"
            disabled
        >
            Tidak dapat dihapus
        </button>
    </div>
</template>

<script>
export default {
    props: ['deposito'],
    methods: {
        deleteDeposito() {
            if (confirm('Apakah Anda yakin?')) {
                // Perform deletion
                this.$emit('delete', this.deposito.id);
            }
        }
    }
}
</script>
```

## ✅ **Benefits of This Approach**

### **1. Better User Experience**
- ❌ No unexpected errors or exceptions
- ✅ Clear feedback about why deletion is prevented
- ✅ Graceful handling with proper notifications

### **2. Flexible Implementation**
- ✅ Can be used in any UI framework (Filament, Blade, Vue, React)
- ✅ Consistent API across different interfaces
- ✅ Easy to customize messages and behavior

### **3. Maintainable Code**
- ✅ Business logic centralized in model
- ✅ Easy to test and validate
- ✅ Clear separation of concerns

### **4. Production Ready**
- ✅ No breaking changes to existing code
- ✅ Backward compatible
- ✅ Proper error handling

## 🧪 **Testing**

The implementation includes comprehensive tests that verify:
- ✅ Order-related deposito cannot be deleted (returns false)
- ✅ Manual deposito can be deleted normally
- ✅ Proper validation messages are returned
- ✅ Model events work correctly

## 🎯 **Summary**

This improved implementation provides:
1. **Graceful prevention** instead of throwing exceptions
2. **User-friendly validation** methods
3. **Flexible usage** across different UI frameworks
4. **Consistent behavior** throughout the application
5. **Better user experience** with clear feedback

The approach is much more suitable for production use and provides better developer and user experience.
