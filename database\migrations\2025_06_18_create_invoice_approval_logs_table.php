<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_approval_logs', function (Blueprint $table) {
            $table->id();

            // Core relationships
            $table->unsignedBigInteger('invoice_id')->comment('Invoice yang di-approve/reject');
            $table->unsignedBigInteger('user_id')->comment('User yang melakukan approval action');
            $table->unsignedBigInteger('creator_id')->comment('User yang membuat invoice (invoice_create_by)');

            // Approval workflow tracking
            $table->enum('approval_type', ['verification', 'supervision', 'approval'])
                  ->comment('Level approval: verification/supervision/approval');
            $table->enum('old_status', ['0', '1', '2'])->nullable()
                  ->comment('Status sebelumnya: 0=pending, 1=approved, 2=rejected');
            $table->enum('new_status', ['0', '1', '2'])
                  ->comment('Status baru: 0=pending, 1=approved, 2=rejected');

            // Additional information
            $table->text('reason')->nullable()
                  ->comment('Alasan rejection atau catatan approval');

            // Metadata
            $table->json('metadata')->nullable()
                  ->comment('Additional data: invoice_no, company_name, etc for quick access');

            $table->timestamps();

            // Indexes for performance
            $table->index('invoice_id', 'idx_invoice_approval_logs_invoice_id');
            $table->index('creator_id', 'idx_invoice_approval_logs_creator_id');
            $table->index('user_id', 'idx_invoice_approval_logs_user_id');
            $table->index('approval_type', 'idx_invoice_approval_logs_approval_type');
            $table->index('new_status', 'idx_invoice_approval_logs_new_status');
            $table->index('created_at', 'idx_invoice_approval_logs_created_at');

            // Composite indexes for common queries
            $table->index(['creator_id', 'new_status'], 'idx_creator_status');
            $table->index(['approval_type', 'new_status'], 'idx_type_status');
            $table->index(['invoice_id', 'approval_type'], 'idx_invoice_type');
            $table->index(['created_at', 'new_status'], 'idx_date_status');

            // Foreign key constraints
            $table->foreign('invoice_id')
                  ->references('id')
                  ->on('invoices')
                  ->onDelete('cascade')
                  ->name('fk_invoice_approval_logs_invoice_id');

            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade')
                  ->name('fk_invoice_approval_logs_user_id');

            $table->foreign('creator_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade')
                  ->name('fk_invoice_approval_logs_creator_id');
        });

        // Add comment to table
        DB::statement("ALTER TABLE invoice_approval_logs COMMENT = 'Audit trail untuk semua approval actions pada invoice - tracking rejections, approvals, dan workflow history'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_approval_logs');
    }
};
