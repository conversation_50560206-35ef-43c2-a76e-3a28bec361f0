<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Invoice extends Model
{
	use HasFactory, SoftDeletes;

	protected $table = 'invoices';

	protected $fillable = [
		'order_id',
		'company_id',
		'client_id',
		'parent_invoice_id',
		'prev_invoice',
		'invoice_create_by',
		'invoice_no',
		'invoice_date',
		'due_date',
		'currency_id',
		'order_amount',
		'booking_fee',
		'invoice_amount',
		'status',
		'inv_sub_total',
		'rates',
		'amount_inword',
		'client_address',

		//bank info
		'bank_acc_name',
		'bank_code',
		'bank_acc_no',
		'bank_acc_address',
		'bank_name',
		'bank_address',
		'bank_correspondent',
		'swift',
		'swift_correspondent',
		'remarks',
		'routing_no',
		'transit',
		'tt_charge',
		'iban',
		'institution',
		'bsb',
		'branch_code',
		'sort_code',
		'branch_bank',
		'back2back',
		'ABA',
		'IFSC',
		'bank_custom_columns',

		//verification
		'verification_status',
		'verified_by',
		'supervision_status',
		'supervised_by',
		'approval_status',
		'approved_by',
	];

	protected $casts = [
		'bank_custom_columns' => 'array',
	];

	public static bool $skipBootedEvent = false;
	public function assignOrderToDescendants(int $orderId): void
	{
		$this->order_id = $orderId;
		$this->save();

		if ($this->childInvoice) {
			$this->childInvoice->assignOrderToDescendants($orderId);
		}
	}

	protected static function boot()
	{
		parent::boot();

		// Global scope for ordering
		static::addGlobalScope('order', function ($builder) {
			$builder->orderBy('created_at', 'desc');
		});

		// Initialize approval workflow status for new invoices
		static::creating(function ($invoice) {
			if (self::$skipBootedEvent) return;
			// Set default approval statuses to pending
			$invoice->verification_status = '0';	// Pending
			$invoice->supervision_status = '0';		// Pending
			$invoice->approval_status = '0';		// Pending
			$invoice->status = 'Draft';				// Pending
			$invoice->invoice_create_by = Auth::user()->id;
		});

		static::deleting(function ($invoice) {
			// 1. Hapus semua invoice details secara paksa (hard delete)
			$invoice->invoiceDetails()->forceDelete();

			// 2. Hapus semua child invoice (rekursif)
			foreach ($invoice->childInvoice as $child) {
				$child->delete(); // trigger rekursif
			}

			// 3. Jika invoice ini adalah ROOT (tidak punya parent)
			if (is_null($invoice->parent_id) && $invoice->order) {
				// Hitung invoice lain dalam order ini, selain yang sedang dihapus
				$remaining = $invoice->order->invoices()
					->where('id', '!=', $invoice->id)
					->whereNull('deleted_at')
					->count();

				// Jika tidak ada lagi invoice lain, update status Order jadi 'Forwarded'
				if ($remaining === 0) {
					$invoice->order->update(['status' => 'Forwarded']);
				}
			}

			// 4. Jika invoice ini adalah CHILD (punya parent)
			if (!is_null($invoice->parent_id)) {
				// Ubah parent-nya jadi Draft, karena child-nya dihapus
				$invoice->parentInvoice()->update(['status' => 'Draft']);
			}
		});
	}

	// Relationship --

	public function company()
	{
		return $this->belongsTo(Company::class, 'company_id', 'id');
	}

	public function parentInvoice()
	{
		return $this->belongsTo(Invoice::class, 'parent_invoice_id', 'id');
	}

	public function childInvoice()
	{
		return $this->hasOne(Invoice::class, 'parent_invoice_id', 'id');
	}

	public function creator()
	{
		return $this->belongsTo(User::class, 'invoice_create_by', 'id');
	}

	public function order()
	{
		return $this->belongsTo(Order::class, 'order_id', 'id');
	}

	public function invoiceDetails()
	{
		return $this->hasMany(InvoiceDetail::class, 'invoice_id', 'id');
	}

	public function companyBanks()
	{
		return $this->hasMany(CompanyBank::class, 'company_id', 'company_id')
			->where('include_in_invoice', true);
	}

	public function allCompanyBanks()
	{
		return $this->hasMany(CompanyBank::class, 'company_id', 'company_id');
	}

	public function invoicetemplate()
	{
		return $this->hasOne(InvoiceTemplate::class, 'company_id', 'company_id');
	}

	public function client()
	{
		return $this->belongsTo(Company::class, 'client_id', 'id');
	}

	public function currency()
	{
		return $this->belongsTo(Currency::class, 'currency_id', 'id');
	}

	public function verifier()
	{
		return $this->belongsTo(User::class, 'verified_by', 'id');
	}

	public function supervisor()
	{
		return $this->belongsTo(User::class, 'supervised_by', 'id');
	}

	public function approver()
	{
		return $this->belongsTo(User::class, 'approved_by', 'id');
	}
	// --
}
