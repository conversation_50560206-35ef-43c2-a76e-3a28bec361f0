
<div class="flex flex-row items-start gap-2">
	<x-filament::avatar
		src="{{ asset('storage/'. $companyLogo) }}"
		alt="{{ $company }}"
	/>
	<div class="flex flex-col">
		<span class="font-bold">
			{{ $bank }}
		</span>
		<span>{{ $accountName }}</span>
		<span>{{ $accountNo }}</span>
	</div>
</div>

<div class="">
	<table class="w-full mt-2">
		<tbody>
			<tr>
				<td class="ps-2">Total Debit (in)</td>
				<td class="text-end pe-2">Rp {{$totalIn}}</td>
			</tr>
			<tr>
				<td class="ps-2">Total Credit (out)</td>
				<td class="text-end pe-2">Rp {{$totalOut}}</td>
			</tr>
			<tr>
				<td class="ps-2">Total Order (out)</td>
				<td class="text-end pe-2">Rp {{$totalOrder}}</td>
			</tr>
		</tbody>
		<tfoot>
			<tr class="border-t font-bold {{ $balanceColor }} dark:text-gray-500">
				<td class="p-2">Balance</td>
				<td class="text-end p-2">Rp {{$balance}}</td>
			</tr>
			<tr class="text-center italic">
				<td colspan="2">{{$numberSpell}}</td>
			</tr>
		</tfoot>
	</table>
</div>



