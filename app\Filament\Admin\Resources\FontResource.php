<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\FontResource\Pages;
use App\Filament\Admin\Resources\FontResource\RelationManagers;
use App\Models\Font;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FontResource extends Resource
{
    protected static ?string $model = Font::class;
	protected static ?string $navigationGroup = 'Masters Data';
	protected static ?string $navigationLabel = 'Master Fonts';
    protected static ?string $navigationIcon = 'icon-file-font';
	protected static ?string $activeNavigationIcon = 'icon-file-font-fill';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')->inlineLabel()->columnSpanFull()->required(),
                Textarea::make('source')
					->label('Source URL')
					->inlineLabel()
					->columnSpanFull()
					->rules(['nullable', 'url'])
					->required()
					->rows(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFonts::route('/'),
            'create' => Pages\CreateFont::route('/create'),
            'edit' => Pages\EditFont::route('/{record}/edit'),
        ];
    }
}
