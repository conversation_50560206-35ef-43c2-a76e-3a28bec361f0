<?php

namespace App\Filament\Mc\Resources;

use App\Filament\Mc\Resources\McOrderResource\Pages;
use App\Filament\Mc\Resources\McOrderResource\RelationManagers;
use App\Models\Currency;
use App\Models\McBank;
use App\Models\McBankGroup;
use App\Models\McCustomer;
use App\Models\McOrder;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Actions\Action as ActionsAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class McOrderResource extends Resource
{
    protected static ?string $model = McOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	protected static ?string $recordTitleAttribute = 'order_code';
	protected static ?string $title = 'Orders';
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	protected static bool $shouldRegisterNavigation = false;

	public static function getNavigationLabel(): string
	{
		return 'Orders';
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
				Group::make()
					->columnSpan(1)
					->schema([
						Select::make('mc_customer_id')
							->label('Customer')
							->inlineLabel()
							->required()
							->searchable()
							->options(McCustomer::query()->get()->pluck('name', 'id')),
						Select::make('currency_id')
							->required()
							->inlineLabel()
							->reactive()
							->searchable()
							->options(Currency::query()->get()->pluck('symbol', 'id')),
						Hidden::make('bank_group_id'),
						Select::make('bank_id')
							->label('Banked To')
							->inlineLabel()
							->options(McBank::query()->get()->pluck('name', 'id'))
							->reactive()
							->afterStateUpdated(function ($set, $state){
								$parentId = McBank::find($state);
								$set('bank_group_id', $parentId->mc_bank_group_id);
							}),
						DatePicker::make('order_date')
							->inlineLabel()
							->default(today()),
					]),
				Fieldset::make('Order')
					->columnSpan(1)
					->columns(1)
					->schema([
						TextInput::make('amount')
							->reactive()
							->required()
							->inlineLabel()
							->prefix(function ($get) {
								$currencyId = $get('currency_id');
								if (! $currencyId) return null;

								$currency = Currency::find($currencyId);
								return $currency?->symbol ?? null;
							})
							->numeric()
							->minValue(1)
							->live(onBlur:true)
							->afterStateUpdated(function ($get, $set, $state) {
								$set('charges', $state <= 20000 ? 85000 : 0);
								self::calculate($get, $set);
							}),
						TextInput::make('buy_rates')
							->required()
							->inlineLabel()
							->prefix('IDR')
							->minValue(1)
							->numeric()
							->live(onBlur:true)
							->afterStateUpdated(function ($get, $set, $state) {
								if ($state || $state > 0) {
									$set('sell_rates', $state + 10);
								}
								self::calculate($get, $set);
							}),
						TextInput::make('sell_rates')
							->required()
							->inlineLabel()
							->minValue(1)
							->prefix('IDR')
							->numeric()
							->live(onBlur:true)
							->afterStateUpdated(function ($get, $set, $state) {
								self::calculate($get, $set);
							}),
						TextInput::make('charges')
							->required()
							->inlineLabel()
							->prefix('IDR')
							->numeric()
							->live(onBlur:true)
							->afterStateUpdated(function ($get, $set, $state) {
								self::calculate($get, $set);
							}),
						Placeholder::make('Total Order')
							->extraAttributes(['class'=>'text-2xl font-bold text-end'])
							->content(fn ($get) => new HtmlString('<span class="text-2xl font-bold text-primary-500"> IDR ' . number_format($get('total_order'),2,',','.') . '</span>'))
							->helperText(function ($get) {
								$inword = $get('amount_inwords');
								if ($inword || $get('total_order') > 0) {
									return new HtmlString('<div class="flex justify-between"><span></span><span class="text-end capitalize">' . $get('amount_inwords') . ' rupiah</span></div>');
								}
								return null;
							}),
						Hidden::make('total_order'),
					])
					->afterStateHydrated(function ($get, $set, $state) {
						// self::calculate($get, $set);
					}),

				// Placeholder::make('note')
				// 	->hiddenLabel()
				// 	->content(fn () => new HtmlString('<span class="text-xs">Total Order</span>'))
				// 	->visibleOn('create'),
            ])->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('order_code')
                    ->searchable(),
                TextColumn::make('customer.name')
                    ->sortable()
					->searchable(),
                TextColumn::make('currency.symbol')
                    ->sortable()
					->alignCenter()
					->searchable(),
                TextColumn::make('amount')
                    // ->money(fn ($record) => $record->currency->symbol)
					->formatStateUsing(fn ($state) => number_format($state,2,',','.'))
					->alignEnd()
                    ->sortable(),
                TextColumn::make('buy_rates')
                    ->formatStateUsing(fn ($state) => number_format($state,2,',','.'))
					->alignEnd()
                    ->sortable(),
                TextColumn::make('sell_rates')
                    ->formatStateUsing(fn ($state) => number_format($state,2,',','.'))
					->alignEnd()
                    ->sortable(),
                TextColumn::make('charges')
                    ->formatStateUsing(fn ($state) => number_format($state,2,',','.'))
					->alignEnd()
                    ->sortable(),
                TextColumn::make('total_order')
                    ->formatStateUsing(fn ($state) => number_format($state,2,',','.'))
					->alignEnd()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make()
					->iconButton()
					->modalFooterActionsAlignment(Alignment::Right)
					->tooltip('View Order')
					->modalHeading(fn ($record) => new HtmlString('<span class="font-bold text-info-500 underline">'.$record->order_code.'</span>'))
					->modalDescription(function ($record) {
						//jika mengandung cls ''
						if (str_contains($record->order_code, 'CLS')) {
							return new HtmlString('<span class="text-sm text-danger-500">This is a Closing Transaction (summary)</span>');
						}
					})
					->extraModalFooterActions([
						Action::make('print')
							->label('Print Invoice')
							->color('info')
							->icon('heroicon-o-printer')
							->url(fn ($record) => route('mcOrderPreview', ['record' => $record->id])),
							// ->action(fn ($livewire, $record) => $livewire->redirectRoute('invoice.print', ['id' => $record->id])),
					]),
                EditAction::make()
					->iconButton()
					->tooltip('Edit Order')
					->modalFooterActionsAlignment(Alignment::Right)
					->modalDescription(function ($record) {
						//jika mengandung cls ''
						if (str_contains($record->order_code, 'CLS')) {
							return new HtmlString('<span class="text-sm text-danger-500">This is a Closing Transaction (summary)</span>');
						}
					})
					->modalHeading(fn ($record) =>
						new HtmlString('<span class="font-bold text-info-500 underline">Edit '
						.$record->order_code.'</span>')),
                ActionsAction::make('print')
                    ->tooltip('Print Invoice')
                    ->icon('heroicon-o-printer')
					->iconButton()
                    ->color('info')
					->url(fn ($record) => route('mcOrderPreview', ['record' => $record->id])),
				DeleteAction::make()
					->iconButton()
					->tooltip('Delete')
					->requiresConfirmation()
					->modalHeading('Delete Transaction')
					->modalDescription('Are you sure you want to delete this transaction?')
					->modalSubmitActionLabel('Yes, delete it'),

            ])
            ->bulkActions([
                BulkActionGroup::make([
                    // DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMcOrders::route('/'),
            // 'create' => Pages\CreateMcOrder::route('/create'),
            // 'edit' => Pages\EditMcOrder::route('/{record}/edit'),
        ];
    }

	public static function calculate($get, $set):void
	{
		$amount = $get('amount');
		$rates = $get('sell_rates');
		$charges = $get('charges');

		$total = ($amount * $rates) + $charges;
		$set('total_order', $total);
		$spellTotal = Number::spell($total, locale: 'id');
		$set('amount_inwords', $spellTotal);
	}
}
