<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class McBankGroup extends Model
{
	use HasFactory, SoftDeletes;

    protected $table = 'mc_bank_groups';

	protected $fillable = [
		'company_id',
		'name',
	];

	public function banks(): HasMany
	{
		return $this->hasMany(McBank::class, 'mc_bank_group_id', 'id');
	}

	public function company(): HasOne
	{
		return $this->hasOne(Company::class);
	}
}
