<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Bank Statement Report</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="{{ $fontSource }}" rel="stylesheet">
    @vite('resources/css/app.css')

    <style>
        @page {
            size: {{ $paperSize }};
            margin: {{ $marginTop }}mm {{ $marginRight }}mm {{ $marginBottom }}mm {{ $marginLeft }}mm;
        }

		@media print {
			.page-break {
				page-break-before: always;
			}
		}


        body {
            font-family: '{{ $fontFamily }}', sans-serif;
            font-size: 12px;
            color: #111827;
            line-height: 1.4;
        }

        th, td {
            border: 1px solid #dddddd;
            padding: 6px 8px;
            text-align: left;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            background-color: #f0f0f0;
            font-weight: 600;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .uppercase {
            text-transform: uppercase;
        }

        .font-bold {
            font-weight: bold;
        }
		@page {
			size: 355.6mm 215.9mm;
		}
    </style>
</head>
<body class=" text-xs">
	<div class="grid grid-cols-2 justify-between mb-4 items-center">
		<div class="col-span-1">
			<h2 class="text-xl font-bold uppercase">Bank Statement Report</h2>
			<div>
				<span class="me-1">Transaction Date: </span>
				<span class="me-3 font-semibold">
					{{ $dateStart ? \Carbon\Carbon::parse($dateStart)->translatedFormat('d F Y') : 'Unfiltered' }}
				</span>
			</div>
		</div>
		<div class="col-span-1 text-end">
			{{-- <span>Report Date: {{ \Carbon\Carbon::today()->format('d F Y') }}</span> --}}
		</div>
	</div>

	{{--
		<div class="mb-6 gap-4 grid grid-cols-2">
			<div class="col-span-1">
				<h4 class="font-semibold text-sm mb-2 w-full text-center underline-offset-8" style="text-underline-offset: 8pts">
					STATISTICS
				</h4>
			</div>
			<div class="col-span-1">
				<h4 class="font-semibold text-sm mb-2 w-full text-center underline-offset-8" style="text-underline-offset: 8pts">
					COMPANY BALANCES
				</h4>
				<table class="w-full text-sm">
					<thead class="bg-blue-950 text-left text-white">
						<tr>
							<th class="p-2">Company</th>
							<th class="p-2 text-right">Balance</th>
						</tr>
					</thead>
					<tbody>
						@foreach ($unfilteredReport as $companyId => $banks)
							@php
								$firstTrx = collect($banks)->first()['transactions'][0] ?? null;
								$companyName = $firstTrx['company'] ?? 'Unknown Company';
								$companyBalance = collect($banks)->sum('balance');
							@endphp
							<tr class="p-2 text-xs">
								<td>{{ $companyName }}</td>
								<td class="text-right">
									{{ number_format($companyBalance, 2, ',', '.') }}
								</td>
							</tr>
						@endforeach
					</tbody>
					<tfoot>
						<tr>
							<td class="font-bold">Total</td>
							<td class="text-right font-bold">
								{{ number_format(collect($unfilteredReport)->sum(fn($banks) => collect($banks)->sum('balance')), 2, ',', '.') }}
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>
		<div class="page-break"></div>
		<h3 class="font-semibold text-sm mb-2 w-full text-center underline-offset-8" style="text-underline-offset: 8pts">
			TRANSACTION DETAILS
		</h3>
	--}}
	<table class="w-full text-sm">
		<thead class="bg-blue-950 text-left text-white">
			<tr>
				<th class="p-2" width="10%">Company</th>
				<th class="p-2" width="10%">Bank</th>
				<th class="p-2 text-right" width="15%">Incoming</th>
				<th class="p-2 text-right" width="15%">Outgoing</th>
				<th class="p-2" width="35%">Description</th>
			</tr>
		</thead>
		<tbody>
			@foreach ($report as $companyId => $banks)
				@php
					$firstTrx = collect($banks)->first()['transactions'][0] ?? null;
        			$companyName = $firstTrx['company'] ?? 'Unknown Company';
				@endphp
				<tr class="bg-blue-50 font-bold p-2">
					<td colspan="5">
						{{ $companyName }}
					</td>
				</tr>

				@foreach ($banks as $bankId => $bankData)
					@php
						$bankName = $bankData['transactions'][0]['bank'] ?? 'Unknown Bank';
            			$balance = $bankData['balance'];
					@endphp
					<tr class="p-2 text-xs font-semibold">
						<td class="ps-4" colspan="4">
							<span class="ps-4"><span class="ps-4">{{ $bankName }}</span></span>
						</td>
						<td class="" colspan="2">
							<div class="flex justify-between">
								<span>Acc. Balance: </span>
								<span class="">IDR {{ number_format($balance ?? 0, 2, ',', '.') }}</span>
							</div>
						</td>
					</tr>

					@foreach ($bankData['transactions'] as $index => $trx)
						@php
							$isFirst = $index == 0;
							$isLast = $index == count($bankData['transactions']) - 1;

							$borderClass = 'border-b-0 border-t-0';
							if ($isFirst && $isLast) {
								$borderClass = ''; // jangan hilangkan border sama sekali
							} elseif ($isFirst) {
								$borderClass = 'border-b-0'; // hilangkan border bawah
							} elseif ($isLast) {
								$borderClass = 'border-t-0'; // hilangkan border atas
							}
						@endphp
						<tr class="p-2 text-xs">
							<td colspan="2" class="{{ $borderClass }}"></td>
							<td class="text-right">
								{{ $trx['in_amount'] !== null ? number_format($trx['in_amount'], 2, ',', '.') : '' }}
							</td>
							<td class="text-right text-red-500">
								{{ $trx['out_amount'] !== null ? number_format($trx['out_amount'], 2, ',', '.') : '' }}
							</td>
							<td>{{ $trx['description'] }}</td>
						</tr>
					@endforeach
				@endforeach
			@endforeach
		</tbody>
	</table>
	<div class="gap-4 grid grid-cols-2 mt-4">
		<div class="col-span-1">
		</div>
		<div class="col-span-1">
			<table class="w-full text-sm">
				<tbody class="text-xs">
					<tr>
						<td colspan="2" class="text-center font-bold bg-blue-100 uppercase">Summary</td>
					</tr>
					<tr>
						<td class="font-bold text-end border-e-0">Balance until {{ \Carbon\Carbon::parse($dateStart)->subDay()->format('d M Y') }}</td>
						<td class="text-end font-bold">IDR {{ number_format($previousBalance, 2, ',', '.') }}</td>
					</tr>
					<tr>
						<td class="font-bold text-end border-e-0">Transaction Date: {{ $dateStart ? \Carbon\Carbon::parse($dateStart)->translatedFormat('d F Y') : 'no data' }}</td>
						<td class="text-end font-bold">
							<div class="flex justify-between">
								<span>Incoming</span>
								<span>IDR {{ number_format($inTotal, 2, ',', '.') }}</span>
							</div>
						</td>
					</tr>
					<tr>
						<td class="font-bold text-end border-e-0"></td>
						<td class="text-end font-bold">
							<div class="flex justify-between text-red-500">
								<span>Outgoing</span>
								<span>IDR {{ number_format($outTotal, 2, ',', '.') }}</span>
							</div>
						</td>
					</tr>
					<tr>
						<td class="font-bold text-end border-e-0"></td>
						<td class="text-end font-bold">
							<div class="flex justify-between">
								<span>Balance</span>
								<span>IDR {{ number_format($balanceTotal + $previousBalance, 2, ',', '.') }}</span>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</body>
</html>
