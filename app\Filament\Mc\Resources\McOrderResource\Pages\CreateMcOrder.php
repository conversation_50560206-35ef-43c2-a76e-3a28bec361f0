<?php

namespace App\Filament\Mc\Resources\McOrderResource\Pages;

use App\Filament\Mc\Resources\McOrderResource;
use App\Models\McCustomer;
use App\Models\McDeposit;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class CreateMcOrder extends CreateRecord
{
	protected static string $resource = McOrderResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	public function getHeading(): string
	{
		return 'New Orders';
	}
	public function getSubheading(): string|Htmlable
	{
		return new HtmlString('<span class="text-md">Deposit and withdrawal transactions for internal company use.</span>');
	}
	public function getRedirectUrl(): string
	{
		// Kalau dipanggil dari modal, jangan redirect
		return request()->has('redirectUrl') ? request()->input('redirectUrl') : parent::getRedirectUrl();
	}
}
