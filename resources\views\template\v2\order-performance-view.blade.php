<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Daily Order Performance Report</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="{{ $fontSource }}" rel="stylesheet">
    @vite('resources/css/app.css')

    <style>
        @page {
            size: {{ $paperSize }};
            margin: {{ $marginTop }}mm {{ $marginRight }}mm {{ $marginBottom }}mm {{ $marginLeft }}mm;
        }

        @media print {
            .page-break {
                page-break-before: always;
            }
        }


        body {
            font-family: '{{ $fontFamily }}', sans-serif;
            font-size: 12px;
            color: #111827;
            line-height: 1.4;
        }

        th,
        td {
            border: 1px solid #d1d5db;
            padding: 6px 8px;
            text-align: left;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            background-color: #f3f4f6;
            font-weight: 600;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .uppercase {
            text-transform: uppercase;
        }

        .font-bold {
            font-weight: bold;
        }

        @page {
            size: 355.6mm 215.9mm;
        }
    </style>
</head>

<body class=" text-xs p-6">
    <div class="grid grid-cols-2 justify-between mb-4 items-center mb-6">
        <div class="col-span-1">
            <h2 class="text-xl font-bold uppercase">Daily Order Performance Report</h2>
            <div>
                <span class="me-1">Start Date:</span>
                <span class="me-3 font-semibold">
                    {{ $dateStart ? \Carbon\Carbon::parse($dateStart)->translatedFormat('d F Y') : 'Unfiltered' }}
                </span>

                <span class="me-1">to:</span>
                <span class="font-semibold">
                    {{ $dateEnd ? \Carbon\Carbon::parse($dateEnd)->translatedFormat('d F Y') : 'Unfiltered' }}
                </span>
            </div>
        </div>
        <div class="col-span-1 text-end">
            {{-- <span>Report Date: {{ \Carbon\Carbon::today()->format('d F Y') }}</span> --}}
        </div>
    </div>

    {{-- <div class="grid grid-cols-4 justify-between items-center space-x-3 mb-6">
        <div class="col-span-1 rounded shadow p-4" style="background-color: #fffcf4">
            <span>Order Count</span>
            <h3 class="text-2xl font-semibold">{{ number_format($orderSummary['orderCount'], 0, ',', '.') }}</h3>
        </div>
        <div class="col-span-1 rounded shadow p-4" style="background-color: #fff4f4">
            <span>Total Purchased</span>
            <h3 class="text-2xl font-semibold">IDR {{ number_format($orderSummary['totalPurchase'], 2, ',', '.') }}</h3>
        </div>
        <div class="col-span-1 rounded shadow p-4" style="background-color: #f4f9ff">
            <span>Total Sales</span>
            <h3 class="text-2xl font-semibold">IDR {{ number_format($orderSummary['totalSell'], 2, ',', '.') }}</h3>
        </div>
        <div class="col-span-1 rounded shadow p-4" style="background-color: #f4fff5">
            <span>Total Profit</span>
            <h3 class="text-2xl font-semibold">IDR {{ number_format($orderSummary['totalProfit'], 2, ',', '.') }}</h3>
        </div>
    </div> --}}
    @if ($report)
		<div class="w-full space-y-4 mb-6">
			<h3 class="font-semibold text-sm mb-2 w-full text-center underline underline-offset-8 mt-6">
				ORDER BY CURRENCY REPORT
			</h3>
			<table class="order-table text-xs w-full border border-gray-300">
				<thead class="bg-gray-100">
					<tr>
						<th class="border px-2 py-1 text-left">Currency</th>
						<th class="border px-2 py-1 text-left">Order Count</th>
						<th class="border px-2 py-1 text-right">Total Amount</th>
						<th class="border px-2 py-1 text-right">Total Purchases</th>
						<th class="border px-2 py-1 text-right">Total Booking Fees</th>
						<th class="border px-2 py-1 text-right">Total Order</th>
					</tr>
				</thead>
				<tbody>
					@foreach ($orderByCurrency as $currency => $data)
						<tr>
							<td>
								<div class="flex justify-between">
									<span>{{ $currency }}</span>
								</div>
							</td>
							<td class="text-right gap-1">
								{{ number_format($data['orderCount'], 0, ',', '.') }}
							</td>
							<td class="text-right gap-1">
								{{ $data['symbol'] }} {{ number_format($data['orderTotal'], 2, ',', '.') }}
							</td>
							<td class="text-right">
								IDR {{ number_format($data['totalPurchase'], 2, ',', '.') }}
							</td>
							<td class="text-right">
								IDR {{ number_format($data['totalFee'], 2, ',', '.') }}
							</td>
							<td class="text-right">
								IDR {{ number_format($data['totalPb'], 2, ',', '.') }}
							</td>
						</tr>
					@endforeach
				</tbody>
			</table>
		</div>
    @endif

    <h3 class="font-semibold text-sm mb-2 w-full text-center underline-offset-8" style="text-underline-offset: 8pts">
        DETAIL ORDER BY COMPANY REPORT
    </h3>
    <table class="order-table text-xs">
        <thead>
            <tr class="bg-blue-950 text-white">
                <th>#</th>
                <th>Company</th>
                <th>Curr.</th>
                <th class="text-right">Order Amount</th>
                <th class="text-right">Booking Fee</th>
                <th class="text-right">Bank Rate</th>
                <th class="text-right">Sub Total</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($report as $i => $row)
                <tr>
                    <td>{{ $i + 1 }}</td>
                    <td>
						<div class="flex flex-col">
							<span class="font-medium">{{ $row['company_id'] }}</span>
							<span class="text-gray-300">
								{{ $row['order_no'] }}
							</span>
						</div>
					</td>
                    <td class="text-center">{{ $row['currency'] }}</td>
                    <td class="text-right">{{ number_format($row['order_amount'], 2, ',', '.') }}</td>
                    <td class="text-right">{{ number_format($row['booking_fee'], 2, ',', '.') }}</td>
                    <td class="text-right">{{ number_format($row['bank_rates'], 2, ',', '.') }}</td>
                    <td class="text-right">{{ number_format($row['totalPb'], 2, ',', '.') }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="12" class="text-center py-4" style="color: #9c9c9c">
                        No data available to display.
                    </td>
                </tr>
            @endforelse
        </tbody>
        <tfoot>
            <tr class="border-t-2 border-t-slate-300">
                <td colspan="6" class="text-right font-medium">Order Count</td>
                <td class="text-right font-bold">{{ number_format($orderSummary['orderCount'], 2, ',', '.') }}</td>
            </tr>
            <tr>
                <td colspan="6" class="text-right font-medium">Total Purchased Amount (IDR)</td>
                <td class="text-right font-bold">{{ number_format($orderSummary['totalPurchase'], 2, ',', '.') }}</td>
            </tr>
            <tr>
                <td colspan="6" class="text-right font-medium">Total Booking Fee (IDR)</td>
                <td class="text-right font-bold">{{ number_format($orderSummary['totalFee'], 2, ',', '.') }}</td>
            </tr>
            <tr>
                <td colspan="6" class="text-right font-medium">Total (IDR)</td>
                <td class="text-right font-bold">{{ number_format($orderSummary['totalPb'], 2, ',', '.') }}</td>
            </tr>
		</tfoot>
    </table>
</body>

</html>
