<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\CompanyBank;
use App\Models\User;

class CompanyBankPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any CompanyBank');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CompanyBank $companybank): bool
    {
        return $user->checkPermissionTo('view CompanyBank');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create CompanyBank');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CompanyBank $companybank): bool
    {
        return $user->checkPermissionTo('update CompanyBank');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CompanyBank $companybank): bool
    {
        return $user->checkPermissionTo('delete CompanyBank');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any CompanyBank');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CompanyBank $companybank): bool
    {
        return $user->checkPermissionTo('restore CompanyBank');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any CompanyBank');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, CompanyBank $companybank): bool
    {
        return $user->checkPermissionTo('replicate CompanyBank');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder CompanyBank');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CompanyBank $companybank): bool
    {
        return $user->checkPermissionTo('force-delete CompanyBank');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any CompanyBank');
    }
}
