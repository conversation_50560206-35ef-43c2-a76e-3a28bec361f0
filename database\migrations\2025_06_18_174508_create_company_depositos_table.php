<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
		Schema::create('company_depositos', function (Blueprint $table) {
			$table->id();
			$table->unsignedBigInteger('company_id')->nullable();
			$table->unsignedBigInteger('order_id')->nullable()->comment('if trx_type is order');
			$table->unsignedBigInteger('bank_id')->nullable()->comment('for bank transaction tracking');
			$table->string('description')->nullable();
			$table->enum('trx_type', ['in', 'out', 'order'])->nullable();
			$table->date('trx_date')->nullable();
			$table->decimal('amount', 18, 2)->nullable();
			$table->timestamps();
			$table->softDeletes();
		});

		DB::statement("
			CREATE OR REPLACE VIEW view_company_deposit_summary AS
			SELECT
				bank_id,
				SUM(CASE WHEN trx_type = 'in' THEN amount ELSE 0 END) AS total_in,
				SUM(CASE WHEN trx_type = 'out' THEN amount ELSE 0 END) AS total_out,
				SUM(CASE WHEN trx_type = 'order' THEN amount ELSE 0 END) AS total_order,
				SUM(CASE
					WHEN trx_type = 'in' THEN amount
					WHEN trx_type IN ('out', 'order') THEN -amount
					ELSE 0
				END) AS balance
			FROM
				company_depositos
			WHERE
				deleted_at IS NULL
			GROUP BY
				bank_id;
		");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_depositos');
		DB::statement("DROP VIEW IF EXISTS view_company_deposit_summary");
    }
};
