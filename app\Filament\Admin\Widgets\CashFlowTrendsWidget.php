<?php

namespace App\Filament\Admin\Widgets;

use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class CashFlowTrendsWidget extends ChartWidget
{
	protected static ?string $heading = 'Trends (30 Days)';
	protected static ?string $pollingInterval = '90s';
	protected int | string | array $columnSpan = 2;

	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	protected function getData(): array
	{
		$days = [];
		$totalIn = [];
		$totalOutCombined = [];
		$totalOutOnly = [];
		$totalOrderOnly = [];
		$netBalance = [];

		for ($i = 29; $i >= 0; $i--) {
			$date = Carbon::now()->subDays($i)->format('Y-m-d');
			$days[] = Carbon::now()->subDays($i)->format('M d');

			$dailyData = DB::table('company_depositos')
				->whereNull('deleted_at')
				->whereDate('trx_date', $date)
				->selectRaw('
                SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as daily_in,
                SUM(CASE WHEN trx_type = "out" THEN amount ELSE 0 END) as daily_out,
                SUM(CASE WHEN trx_type = "order" THEN amount ELSE 0 END) as daily_order
            ')
				->first();

			$in = ($dailyData->daily_in ?? 0) / 1_000_000;
			$out = ($dailyData->daily_out ?? 0) / 1_000_000;
			$order = ($dailyData->daily_order ?? 0) / 1_000_000;
			$combinedOut = $out + $order;
			$net = $in - $combinedOut;

			$totalIn[] = round($in, 2);
			$totalOutOnly[] = round($out, 2);
			$totalOrderOnly[] = round($order, 2);
			$totalOutCombined[] = round($combinedOut, 2);
			$netBalance[] = round($net, 2);
		}

		return [
			'datasets' => [
				[
					'label' => 'Total Debit (M IDR)',
					'data' => $totalIn,
					'borderColor' => 'rgb(16, 185, 129)', // Green
					'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
					'tension' => 0.3,
				],
				[
					'label' => 'Total Credited (M IDR)',
					'data' => $totalOutCombined,
					'borderColor' => 'rgb(239, 68, 68)', // Red
					'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
					'tension' => 0.3,
				],
				[
					'label' => 'Total Out (M IDR)',
					'data' => $totalOutOnly,
					'borderColor' => 'rgb(249, 115, 22)', // Orange
					'backgroundColor' => 'rgba(249, 115, 22, 0.1)',
					'tension' => 0.3,
				],
				[
					'label' => 'Total Order (M IDR)',
					'data' => $totalOrderOnly,
					'borderColor' => 'rgb(234, 179, 8)', // Yellow
					'backgroundColor' => 'rgba(234, 179, 8, 0.1)',
					'tension' => 0.3,
				],
				[
					'label' => 'Net Balance (M IDR)',
					'data' => $netBalance,
					'borderColor' => 'rgb(59, 130, 246)', // Blue
					'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
					'tension' => 0.3,
					'borderWidth' => 3,
				],
			],
			'labels' => $days,
		];
	}



	protected function getType(): string
	{
		return 'line';
	}

	protected function getOptions(): array
	{
		return [
			'responsive' => true,
			'interaction' => [
				'mode' => 'index',
				'intersect' => false,
			],
			'scales' => [
				'y' => [
					'title' => [
						'display' => true,
						'text' => 'Amount (Million IDR)',
					],
					'grid' => [
						'color' => 'rgba(0, 0, 0, 0.1)',
					],
				],
				'x' => [
					'grid' => [
						'display' => false,
					],
				],
			],
			'plugins' => [
				'legend' => [
					'display' => true,
					'position' => 'top',
				],
				'tooltip' => [
					'mode' => 'index',
					'intersect' => false,
				],
			],
		];
	}
}
