<?php

namespace App\Filament\Mc\Resources;

use App\Filament\Mc\Resources\McBankGroupResource\Pages;
use App\Filament\Mc\Resources\McBankGroupResource\RelationManagers;
use App\Filament\Mc\Resources\McBankGroupResource\RelationManagers\BanksRelationManager;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\McBankGroup;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms;
use Filament\Forms\Components\{Section, Select, TextInput};
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Log;

class McBankGroupResource extends Resource
{
    protected static ?string $model = McBankGroup::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';
	protected static ?string $recordTitleAttribute = 'company.name';
	protected static bool $shouldRegisterNavigation = false;
	public static function getNavigationLabel(): string
	{
		return 'Bank Group';
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Bank Group')
					->schema([
						Select::make('company_id')
							->label('Company')
							->options(Company::query()->where('name','like','PT%')->get()->pluck('name', 'id'))
							->reactive()
							->hiddenOn('view')
							->searchable()
							->afterStateUpdated(function ($set, $state) {
								$company = Company::find($state);
								$set('name', $company->name);
								static::fillBanks($set, $state);
							}),
						TextInput::make('name')->label('Group Name')->required(),
						TableRepeater::make('banksGroup')
							->relationship('banks')
							->headers([
								Header::make('Bank'),
								Header::make('Account Number'),
								Header::make('Account Name'),
							])
							->schema([
								TextInput::make('name'),
								TextInput::make('account_no'),
								TextInput::make('account_name'),
							])
					])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable(),
				TextColumn::make('banks.name')
					->listWithLineBreaks()
					->bulleted()
            ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make()->iconButton()->modalHeading('Bank Group'),
                EditAction::make()->iconButton()->modalHeading('Edit Bank Group'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // BanksRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMcBankGroups::route('/'),
            // 'create' => Pages\CreateMcBankGroup::route('/create'),
            // 'view' => Pages\ViewMcBankGroup::route('/{record}'),
            // 'edit' => Pages\EditMcBankGroup::route('/{record}/edit'),
        ];
    }

	public static function fillBanks(callable $set, $state): void
	{
		$set('banksGroup', []);
		if (!is_null($state)) {
			$banks = CompanyBank::where('company_id', $state)
				->get();

			if ($banks->isNotEmpty()) {
				$banksGroup = $banks->map(function ($template) {
					return [
						'name'=> $template->bank_name,
						'account_no'=> $template->bank_acc_no,
						'account_name'=> $template->bank_acc_name,
					];
				})->toArray();

				$set('banksGroup', $banksGroup);
			}
		}
	}
}
