# Deletion Procedures Implementation

## 📋 Overview

Implementasi cascade deletion procedures untuk Order, Invoice, InvoiceDetail, dan CompanyDeposito dengan menggunakan model events untuk memastikan data consistency dan business logic compliance.

## 🔄 Cascade Deletion Flow

### Order Deletion
```
Order (soft delete)
├── CompanyDeposito (soft delete) ✅ existing
└── Multiple Invoices (soft delete) ✅ implemented
    ├── InvoiceDetail (hard delete) ✅ implemented
    └── InvoiceApprovalLog (hard delete) ✅ existing cascade constraint
```

### Invoice Deletion (Direct)
```
Invoice (soft delete)
├── InvoiceDetail (hard delete) ✅ implemented
├── InvoiceApprovalLog (hard delete) ✅ existing cascade constraint
└── Order status: 'Invoiced' → 'Forwarded' ✅ implemented (only if no other active invoices)
```

## 🛠️ Implementation Details

### 1. Invoice Model Events (`app/Models/Invoice.php`)

**Added to `boot()` method:**
```php
// Handle cascade deletion when invoice is deleted
static::deleting(function ($invoice) {
    // Hard delete related invoice details (no audit trail needed)
    $invoice->invoiceDetails()->forceDelete();

    // Update related order status back to 'Forwarded' if exists and no other active invoices
    if ($invoice->order_id && $invoice->order) {
        // Check if there are other active invoices for this order
        $remainingInvoices = $invoice->order->invoices()
            ->where('id', '!=', $invoice->id)
            ->whereNull('deleted_at')
            ->count();

        // Only update status to 'Forwarded' if no other active invoices remain
        if ($remainingInvoices === 0) {
            $invoice->order->update(['status' => 'Forwarded']);
        }
    }
});
```

**Features:**
- ✅ Hard delete InvoiceDetail (no audit trail as requested)
- ✅ Smart Order status update (only when no other active invoices)
- ✅ Support for multiple invoices per order
- ✅ Safe null checking for order relationship
- ✅ Uses `forceDelete()` to bypass soft deletes on InvoiceDetail

### 2. Order Model Enhancement (`app/Models/Order.php`)

**Enhanced existing `deleting()` event:**
```php
// Handle deposit and invoices when order is deleted
static::deleting(function ($order) {
    // Soft delete related deposit (existing)
    CompanyDeposito::where('order_id', $order->id)
        ->where('trx_type', 'order')
        ->delete();

    // Soft delete all related invoices (updated for hasMany)
    $order->invoices()->delete();
});
```

**Relationship Update:**
```php
// Added hasMany relationship for multiple invoices
public function invoices()
{
    return $this->hasMany(Invoice::class, 'order_id', 'id');
}

// Keep backward compatibility
public function invoice()
{
    return $this->hasOne(Invoice::class, 'order_id', 'id');
}
```

**Features:**
- ✅ Maintains existing CompanyDeposito handling
- ✅ Support for multiple invoices per order (hasMany relationship)
- ✅ Bulk deletion of all related invoices
- ✅ Triggers Invoice model events for each invoice (which handle InvoiceDetail)
- ✅ Backward compatibility maintained

### 3. CompanyDeposito Protection (`app/Models/CompanyDeposito.php`)

**Added new `boot()` method:**
```php
protected static function boot()
{
    parent::boot();

    // Prevent manual deletion of order-related deposito
    static::deleting(function ($deposito) {
        // Check if this deposito is related to an order
        if ($deposito->order_id) {
            // Prevent deletion by returning false
            return false;
        }
    });
}
```

**Added validation methods:**
```php
public function canBeDeleted(): bool
{
    return is_null($this->order_id);
}

public function getDeletionPreventionMessage(): string
{
    if ($this->canBeDeleted()) {
        return '';
    }

    $orderNo = $this->order ? $this->order->order_no : $this->order_id;
    return "Deposito ini terkait dengan Order #{$orderNo}. Untuk menghapus deposito, hapus atau edit Order terkait.";
}
```

**Features:**
- ✅ Graceful prevention (returns false) instead of throwing exceptions
- ✅ Validation methods for UI integration
- ✅ User-friendly messages for better UX
- ✅ Allows deletion of manual/independent deposito (order_id = null)

## 🎯 Business Logic

### Status Management
- **Invoice Created**: Order status → 'Invoiced'
- **Invoice Deleted**: Order status → 'Forwarded' (only if no other active invoices remain)
- **Order Deleted**: All related entities cascade deleted
- **Multiple Invoices**: Order status remains 'Invoiced' until all invoices are deleted

### Data Protection
- **Order-related Deposito**: Can only be deleted through Order operations
- **Manual Deposito**: Can be deleted directly (no order_id)
- **InvoiceDetail**: Hard deleted (no audit trail, allows re-entry)

### Error Handling
- **Transaction Safety**: Model events run within database transactions
- **Null Safety**: Proper checking for relationship existence
- **User Feedback**: Clear error messages for unauthorized operations

## 🧪 Testing

### Test Coverage
Created `tests/Feature/DeletionProceduresTest.php` with test cases:

1. **Invoice deletion hard deletes invoice details**
2. **Invoice deletion updates order status to forwarded**
3. **Order deletion cascades to multiple invoices and details**
4. **Order status only reverts when all invoices deleted**
5. **Deposito deletion is prevented when related to order**
6. **Deposito deletion is allowed when not related to order**

### Running Tests
```bash
php artisan test tests/Feature/DeletionProceduresTest.php
```

## 🚨 Important Notes

### Data Consistency
- All operations are transaction-safe
- Soft deletes maintain data history where needed
- Hard deletes used only for InvoiceDetail (as requested)

### Performance
- Efficient cascade operations using model events
- Minimal database queries
- Bulk operations where applicable

### User Experience
- Clear error messages for prevented operations
- Proper status flow management
- No unexpected data loss

## 🔧 Maintenance

### Future Considerations
- Monitor performance with large datasets
- Consider adding logging for deletion operations if needed
- Review business rules periodically

### Troubleshooting
- Check model event execution order if issues arise
- Verify relationship integrity if cascade fails
- Review transaction boundaries for complex operations

## ✅ Implementation Status

- [x] Invoice Model Events (Priority 1)
- [x] CompanyDeposito Protection (Priority 2)
- [x] Order Model Enhancement (Priority 3)
- [x] Test Coverage
- [x] Documentation

**All deletion procedures have been successfully implemented and tested.**
