<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Order;
use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\CompanyDeposito;
use App\Models\Company;
use App\Models\Currency;
use App\Models\CompanyBank;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class DeletionProceduresTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $company;
    protected $currency;
    protected $bank;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data manually for now
        $this->company = Company::create([
            'name' => 'Test Company',
            'address' => 'Test Address',
            'phone' => '*********',
            'email' => '<EMAIL>',
            'type' => 2,
            'business_type' => 1,
        ]);

        $this->currency = Currency::create([
            'name' => 'USD',
            'symbol' => '$',
            'country' => 'United States',
            'suffix' => '',
        ]);

        $this->bank = CompanyBank::create([
            'company_id' => $this->company->id,
            'bank_acc_name' => 'Test Bank Account',
            'bank_code' => '123',
            'bank_acc_no' => '*********0',
            'bank_name' => 'Test Bank',
            'include_in_invoice' => true,
        ]);
    }

    /** @test */
    public function invoice_deletion_hard_deletes_invoice_details()
    {
        // Create order
        $order = Order::create([
            'order_no' => 'ORD-001',
            'company_id' => $this->company->id,
            'currency_id' => $this->currency->id,
            'bank_id' => $this->bank->id,
            'status' => 'Invoiced',
            'order_amount' => 1000,
            'total' => 1000,
            'order_date' => now(),
        ]);

        // Create invoice
        $invoice = Invoice::create([
            'order_id' => $order->id,
            'company_id' => $this->company->id,
            'invoice_no' => 'INV-001',
            'verification_status' => '0',
            'supervision_status' => '0',
            'approval_status' => '0',
        ]);

        // Create invoice details
        $detail1 = InvoiceDetail::create([
            'invoice_id' => $invoice->id,
            'company_id' => $this->company->id,
            'description' => 'Test Item 1',
            'quantity' => 1,
            'price' => 100,
            'sub_total' => 100,
        ]);

        $detail2 = InvoiceDetail::create([
            'invoice_id' => $invoice->id,
            'company_id' => $this->company->id,
            'description' => 'Test Item 2',
            'quantity' => 2,
            'price' => 200,
            'sub_total' => 400,
        ]);

        // Delete invoice
        $invoice->delete();

        // Assert invoice is soft deleted
        $this->assertSoftDeleted('invoices', ['id' => $invoice->id]);

        // Assert invoice details are hard deleted
        $this->assertDatabaseMissing('invoice_details', ['id' => $detail1->id]);
        $this->assertDatabaseMissing('invoice_details', ['id' => $detail2->id]);
    }

    /** @test */
    public function invoice_deletion_updates_order_status_to_forwarded()
    {
        // Create order with Invoiced status
        $order = Order::create([
            'order_no' => 'ORD-002',
            'company_id' => $this->company->id,
            'currency_id' => $this->currency->id,
            'bank_id' => $this->bank->id,
            'status' => 'Invoiced',
            'order_amount' => 1000,
            'total' => 1000,
            'order_date' => now(),
        ]);

        // Create invoice
        $invoice = Invoice::create([
            'order_id' => $order->id,
            'company_id' => $this->company->id,
            'invoice_no' => 'INV-002',
            'verification_status' => '0',
            'supervision_status' => '0',
            'approval_status' => '0',
        ]);

        // Delete invoice
        $invoice->delete();

        // Assert order status is updated to Forwarded
        $order->refresh();
        $this->assertEquals('Forwarded', $order->status);
    }

    /** @test */
    public function order_deletion_cascades_to_multiple_invoices_and_details()
    {
        // Create order
        $order = Order::create([
            'order_no' => 'ORD-003',
            'company_id' => $this->company->id,
            'currency_id' => $this->currency->id,
            'bank_id' => $this->bank->id,
            'status' => 'Invoiced',
            'order_amount' => 1000,
            'total' => 1000,
            'order_date' => now(),
        ]);

        // Create multiple invoices
        $invoice1 = Invoice::create([
            'order_id' => $order->id,
            'company_id' => $this->company->id,
            'invoice_no' => 'INV-003-1',
            'verification_status' => '0',
            'supervision_status' => '0',
            'approval_status' => '0',
        ]);

        $invoice2 = Invoice::create([
            'order_id' => $order->id,
            'company_id' => $this->company->id,
            'invoice_no' => 'INV-003-2',
            'verification_status' => '0',
            'supervision_status' => '0',
            'approval_status' => '0',
        ]);

        // Create invoice details for both invoices
        $detail1 = InvoiceDetail::create([
            'invoice_id' => $invoice1->id,
            'company_id' => $this->company->id,
            'description' => 'Test Item 1',
            'quantity' => 1,
            'price' => 100,
            'sub_total' => 100,
        ]);

        $detail2 = InvoiceDetail::create([
            'invoice_id' => $invoice2->id,
            'company_id' => $this->company->id,
            'description' => 'Test Item 2',
            'quantity' => 2,
            'price' => 200,
            'sub_total' => 400,
        ]);

        // Delete order
        $order->delete();

        // Assert order is soft deleted
        $this->assertSoftDeleted('orders', ['id' => $order->id]);

        // Assert both invoices are soft deleted
        $this->assertSoftDeleted('invoices', ['id' => $invoice1->id]);
        $this->assertSoftDeleted('invoices', ['id' => $invoice2->id]);

        // Assert all invoice details are hard deleted
        $this->assertDatabaseMissing('invoice_details', ['id' => $detail1->id]);
        $this->assertDatabaseMissing('invoice_details', ['id' => $detail2->id]);
    }

    /** @test */
    public function deposito_related_to_order_can_be_identified()
    {
        // Create order
        $order = Order::create([
            'order_no' => 'ORD-004',
            'company_id' => $this->company->id,
            'currency_id' => $this->currency->id,
            'bank_id' => $this->bank->id,
            'total' => 1000,
            'rates' => 15000,
            'order_date' => now(),
            'order_amount' => 1000,
        ]);

        // Get the auto-created deposito
        $deposito = CompanyDeposito::where('order_id', $order->id)->first();
        $this->assertNotNull($deposito);

        // Check that deposito is related to order
        $this->assertNotNull($deposito->order_id);
        $this->assertEquals($order->id, $deposito->order_id);

        // Check validation methods still work
        $this->assertFalse($deposito->canBeDeleted());
        $this->assertStringContainsString('Order #ORD-004', $deposito->getDeletionPreventionMessage());

        // Deposito can still be deleted at model level (UI will prevent it)
        $result = $deposito->delete();
        $this->assertTrue($result);

        // Assert deposito is soft deleted
        $this->assertSoftDeleted('company_depositos', ['id' => $deposito->id]);
    }

    /** @test */
    public function deposito_deletion_is_allowed_when_not_related_to_order()
    {
        // Create manual deposito (not related to order)
        $deposito = CompanyDeposito::create([
            'company_id' => $this->company->id,
            'bank_id' => $this->bank->id,
            'order_id' => null, // No order relation
            'trx_type' => 'in',
            'description' => 'Manual deposit',
            'trx_date' => now(),
            'amount' => 5000,
        ]);

        // Check that deposito can be deleted
        $this->assertTrue($deposito->canBeDeleted());

        // Check no deletion prevention message
        $this->assertEmpty($deposito->getDeletionPreventionMessage());

        // Delete deposito - should work
        $result = $deposito->delete();
        $this->assertTrue($result);

        // Assert deposito is soft deleted
        $this->assertSoftDeleted('company_depositos', ['id' => $deposito->id]);
    }

    /** @test */
    public function order_status_only_reverts_when_all_invoices_deleted()
    {
        // Create order with Invoiced status
        $order = Order::create([
            'order_no' => 'ORD-005',
            'company_id' => $this->company->id,
            'currency_id' => $this->currency->id,
            'bank_id' => $this->bank->id,
            'status' => 'Invoiced',
            'order_amount' => 1000,
            'total' => 1000,
            'order_date' => now(),
        ]);

        // Create multiple invoices
        $invoice1 = Invoice::create([
            'order_id' => $order->id,
            'company_id' => $this->company->id,
            'invoice_no' => 'INV-005-1',
            'verification_status' => '0',
            'supervision_status' => '0',
            'approval_status' => '0',
        ]);

        $invoice2 = Invoice::create([
            'order_id' => $order->id,
            'company_id' => $this->company->id,
            'invoice_no' => 'INV-005-2',
            'verification_status' => '0',
            'supervision_status' => '0',
            'approval_status' => '0',
        ]);

        // Delete first invoice
        $invoice1->delete();

        // Order status should still be 'Invoiced' because invoice2 still exists
        $order->refresh();
        $this->assertEquals('Invoiced', $order->status);

        // Delete second invoice
        $invoice2->delete();

        // Now order status should revert to 'Forwarded'
        $order->refresh();
        $this->assertEquals('Forwarded', $order->status);
    }
}
