<!DOCTYPE html>
<html lang="en" class="root-text-sm">

<head>
	@include('invoice.metatitle')
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet"
		integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="{{ $payload['invoice']->company->font->source ?? 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap' }}" rel="stylesheet">

	@include('invoice.css')
</head>

<body style="font-family: {{$payload['invoice']->company->font->name ?? ''}} !important">

	<?php
		$invoiceDate = date_create($payload['invoice']->invoice_date);
		$invoiceDue = date_create($payload['invoice']->due_date);
		$difference = date_diff($invoiceDate, $invoiceDue);
	?>
	<div class="main small">
		<div class="">
			<div class="row d-flex justify-content-between align-items-center px-3">
				<div class="col-8">
					<img src="{{ url('storage/' . $payload['invoice']->company->logo) }}" class="mx-2 mt-1 profile-image ml-auto" style="width: 200px; height:auto">
					<div>
						<ul class="list-unstyled">
							<li>
								@if ($payload['invoice']->company->phone)
									<span>{{$payload['invoice']->company->phone}},</span>
								@endif
								@if ($payload['invoice']->company->email)
									<span>{{$payload['invoice']->company->email}}</span>
								@endif
							</li>
							<li class="d-flex justify-content-start align-items-start">
								<span>{!!$payload['invoice']->company->address!!}</span>
							</li>
						</ul>
					</div>
				</div>
				<span class="text-end fw-bold h1 ml-auto col-4">INVOICE</span>
			</div>
		</div>

		{{-- <div class="pagebreak"></div> --}}
		<div class="">
			<div class="row d-flex justify-content-between align-items-start mb-3">
				<div class="col-5 text-xs">
					<table class="table table-sm w-100">
						<thead class="table-dark">
							<th>Bill To</th>
						</thead>
						<tbody>
							<tr>
								<td>
									<div>
										<span class="fw-bold">
											{{$payload['invoice']->client->name}}
										</span>
										<span>{!!$payload['invoice']->client->address!!}</span>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div class="col-5 text-xs">
					<div class="mb-2">
						<table class="table table-sm w-100">
							<thead class="table-dark">
								<th class="text-center">Invoice#</th>
								<th class="text-center">Date</th>
							</thead>
							<tbody>
								<tr>
									<td class="text-center">{{$payload['invoice']->invoice_no}}</td>
									<td class="text-center">
										{{ date('d/m/Y', strtotime($payload['invoice']->invoice_date)) }}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<table class="table table-sm">
						<thead class="table-dark">
							<th class="text-center">Due Date</th>
						</thead>
						<tbody>
							<tr>
								<td class="text-center">
									{{ date('d/m/Y', strtotime($payload['invoice']->due_date)) }}
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="row">
				<div class="col-12">
					{{-- <span class="fw-bold mb-2">Customer Order</span> --}}
					<table class="table table-bordered table-sm">
						<thead class="table-dark"
								@if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
									style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
								@endif>
							<tr>
								<th class="text-center" style="border: 1px solid #ddd">Description</th>
								<th class="text-end" style="border: 1px solid #ddd" width="15%">Qty</th>
								<th class="text-end" style="border: 1px solid #ddd" width="15%">Price</th>
								<th class="text-end" style="border: 1px solid #ddd" width="20%">Amount</th>
							</tr>
						</thead>
						<tbody>
							@foreach ($payload['invoice']->invoiceDetails as $items)
								<tr>
									<td class="vertical-middle">{!!$items->description!!}</td>
									<td class="text-end vertical-middle">{{number_format($items->quantity, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle">{{$items->invoice->currency->symbol}} {{number_format($items->price, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle">{{$items->invoice->currency->symbol}} {{number_format($items->sub_total, 2, '.', ',')}}</td>
								</tr>
							@endforeach
						</tbody>
						<tfoot
							@if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
								style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
							@endif>
							<tr class="">
								<td class="text-end" colspan="3">
									BOOKING FEE :
								</td>
								<td class="text-end fw-bold">
									<span>{{$payload['invoice']->currency->symbol}} {{number_format($payload['invoice']->booking_fee, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-end" colspan="3">
									RATE :
								</td>
								<td class="text-end fw-bold">
									<span>Rp {{number_format($payload['invoice']->rates, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-center fw-bold text-uppercase" colspan="2">Thank you for your business!</td>
								<td class="text-end fw-bold">
									TOTAL :
								</td>
								<td class="text-end fw-bold">
									<span>Rp {{number_format($payload['invoice']->invoice_amount, 2, '.', ',')}}</span>
								</td>
							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			<div class="col-12 text-center mb-5">
				{{ optional($payload['invoice']->company)->type === '1' ? 'Inword' : 'Terbilang' }}:
				<span class="fw-bold text-uppercase">{{$payload['invoice']->amount_inword}}</span>
			</div>
			<div class="row-mb-5 col-12">
				{{-- <div class="mb-3" style="font-style: italic">
					<span class="text-start">
						Payment must be completed within 14 calendar days from the invoice date.
					</span>
				</div> --}}
				<div class="row d-flex justify-content-between align-items-center">
					<div class="col-7">
						@include('partials.bankinfo')
					</div>
					<div class="col-5 text-center">
						@if($payload['invoice']->company->signature)
							<img src="{{ url('storage/' . $payload['invoice']->company->signature) }}" class="mx-2 mt-1 profile-image ml-auto" style="max-height:5rem"><br>
						@endif
						@if ($payload['invoice']->company->signature_name)
							<u>{{$payload['invoice']->company->signature_name}}</u>
						@endif
					</div>
				</div>
				@if ($payload['invoice']->remarks)
					<div>
						<span class="fw-bold d-block">Terms and Conditions</span>
						<p>Thank your for business. Please send payment within {{ $difference->days }} day(s) of receiving this invoice. There will be a 1,5% interest charge per month on late invoices.</p>
						{{-- <p class="m-0 text-sm">{!!$payload['invoice']->remarks!!}</p> --}}
					</div>
				@endif
			</div>
			{{-- <hr> --}}
		</div>
		<div class="footer">
			<div class="container mt-5">
				<div style="margin: 0 auto; text-align: end;">
					<div class="col-12" style="font-family: \'Courier New\', monospace; font-size: 11px;">
						<span class="text-end" style="font-size: 10px;">
							{{$payload['invoice']->company->name}}
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous">
	</script>
<script>
</script>
</body>

</html>
