<?php

namespace App\Filament\Admin\Resources\OrderResource\Pages;

use App\Filament\Admin\Resources\OrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ViewOrder extends ViewRecord
{
	protected static string $resource = OrderResource::class;
    public function getHeading(): string
	{
        return 'Order detail';
	}
	protected function getHeaderActions(): array
	{
		return [
			Actions\EditAction::make(),
		];
	}

	public function getSubheading(): string|Htmlable
	{
		return new HtmlString(view('components.order-quick-navigation', [
			'order' => $this->record,
			'mode' => 'view'
		])->render());
	}
}
