<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Currency;
use App\Models\Order;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Filament\Tables\View\TablesRenderHook;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderSummaryWidget extends BaseWidget
{
	protected static ?string $heading = '';
	protected int | string | array $columnSpan = 1;

	// public array $selectedCompanyIds = [];

	// protected $listeners = ['order-created' => '$refresh'];

	public function mount(): void
	{
		// Initialize with empty selection to show default data
		// if (empty($this->selectedCompanyIds)) {
		// 	$this->selectedCompanyIds = [];
		// }
	}


	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']);
	}

	public function table(Table $table): Table
	{
		return $table
			->query(Order::query())
			->modifyQueryUsing(function (Builder $query) {
				$query
					->leftJoin('currencies', 'orders.currency_id', '=', 'currencies.id')
					->whereNotNull('orders.currency_id')
					->whereDate('orders.created_at', today())
					->select([
						'orders.currency_id',
						'currencies.name as currency_name',
						'currencies.symbol as currency_symbol',
						DB::raw('SUM(orders.total) as total_amount'),
						DB::raw('COUNT(orders.id) as order_count'),
						DB::raw('MAX(orders.created_at) as latest_order_date'),
						DB::raw('MAX(orders.created_at) as created_at') // Add this to avoid ambiguity
					])
					->groupBy('orders.currency_id', 'currencies.name', 'currencies.symbol');
					// ->orderBy('total_amount', 'desc');

			})
			->columns([
				TextColumn::make('currency_name')
					->label('Order by Currency')
					->sortable(),

				TextColumn::make('total_amount')
					->label('Orders Amount')
					->numeric()
					->alignEnd()
					->sortable(),
			])
			->actions([
				//
			])
			->filters([
				// Filter::make('created_at')
				// 	->form([
				// 		DatePicker::make('from')
				// 			->label('From Date')
				// 			->default(now()->toDateString()),
				// 		DatePicker::make('until')
				// 			->label('Until Date')
				// 			->default(now()->toDateString()),
				// 	])
				// 	->query(function (Builder $query, array $data): Builder {
				// 		return $query
				// 			->when(
				// 				$data['from'],
				// 				fn (Builder $query, $date): Builder => $query->whereDate('orders.order_date', '>=', $date),
				// 			)
				// 			->when(
				// 				$data['until'],
				// 				fn (Builder $query, $date): Builder => $query->whereDate('orders.order_date', '<=', $date),
				// 			);
				// 	}),
			])
			->paginated(false)
			->striped()
			->defaultSort('total_amount', 'desc')
			->emptyStateHeading('No Orders Found')
			->emptyStateDescription('Made some orders today to see the summary.');
	}


	public function getTableRenderHooks(): array
	{
		return [
			TablesRenderHook::HEADER_BEFORE => fn() => null,
			TablesRenderHook::HEADER_AFTER => fn() => null,
		];
	}

	public function getTableRecordKey($record): string
	{
		return (string) ($record->currency_id ?? 'unknown');
	}

	protected function getDefaultTableFiltersState(): array
	{
		return [
			'order_date' => [
				'from' => now()->toDateString(),
				'until' => now()->toDateString(),
			],
		];
	}




}
