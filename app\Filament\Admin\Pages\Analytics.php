<?php

namespace App\Filament\Admin\Pages;

use App\Filament\Admin\Widgets\ProfitOverviewWidget;
use App\Filament\Admin\Widgets\CashFlowTrendsWidget;
use App\Filament\Admin\Widgets\CurrentRevenueWidget;
use App\Filament\Admin\Widgets\StackedCashFlowWidget;
use App\Filament\Admin\Widgets\DepositSummaryWidget;
use App\Filament\Admin\Widgets\OrderTrendsWidget;
use App\Filament\Admin\Widgets\ShortcutKeyWidget;
use App\Filament\Admin\Widgets\RejectionSummaryWidget;
use App\Filament\Admin\Widgets\trendsWidget;
use App\Filament\Admin\Widgets\UserRejectionAnalyticsWidget;
use Filament\Actions\Action;
use Filament\Forms\Components\{Placeholder, Select, TextInput, Repeater};
use Filament\Forms\Form;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\HtmlString;

class Analytics extends BaseDashboard
{
	use HasFiltersForm;
	protected static ?int $navigationSort = -2;
	protected static string $routePath = '/analytics';
	protected static ?string $navigationIcon = 'heroicon-o-chart-bar';
	public static function getNavigationLabel(): string
    {
        return 'Analytics';
    }

	public array $mountLazyWidgets = [];

    public function getWidgets(): array
    {
        return [
            CurrentRevenueWidget::class,
            StackedCashFlowWidget::class,
            trendsWidget::class,
            ShortcutKeyWidget::class,
        ];
    }

	public function getHeading(): string|Htmlable
    {
        return new HtmlString('Dashboard Analytics');
    }
	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-sm italic">Analysis and Monitoring</span>');
    }

    public function getColumns(): int | string | array
    {
        return 3;
    }
	protected function getHeaderActions(): array
	{
		return [
			// Placeholder::make('info')
			// 	->label('Quick Menu'),
			Action::make('report')
				->tooltip('Generate Report (Ctrl+Alt+r)')
				->url('#report-modal')
				->icon('heroicon-o-printer')
				->iconButton(),
			Action::make('deposit')
				->tooltip('Add new deposit (Ctrl+Alt+d)')
				->url('#deposit-modal')
				->icon('icon-piggy-bank')
				->iconButton(),
			Action::make('order')
				->tooltip('Add new order (Ctrl+Alt+o)')
				->url('#order-modal')
				->icon('icon-receipt-cutoff')
				->iconButton(),
			Action::make('invoice')
				->tooltip('New Invoice')
				->url('/admin/invoices')
				->icon('icon-file-ruled')
				->iconButton(),
		];
	}

}
