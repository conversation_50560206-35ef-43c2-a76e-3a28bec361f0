<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MasterInvoiceDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'description',
        'quantity',
        'unit',
        'price',
		'business_type'
    ];

    public function businessType(): BelongsTo
    {
        return $this->belongsTo(BusinessType::class, 'business_type', 'id');
    }
}
