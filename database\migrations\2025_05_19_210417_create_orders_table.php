<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_create_by')->nullable();
            $table->unsignedBigInteger('company_id')->nullable();
            $table->unsignedBigInteger('currency_id')->nullable();
            $table->decimal('order_amount', 18, 2)->nullable();
            $table->date('order_date')->nullable();
            $table->decimal('booking_fee', 10, 2)->nullable();
            $table->decimal('total', 18, 2)->nullable();
            $table->string('status')->nullable(); //Draft, Forwarded

            $table->timestamps();
            $table->softDeletes();
        });

		DB::statement("
			CREATE OR REPLACE VIEW profit_summary AS
			SELECT
				-- Hari ini
				SUM(
					CASE
						WHEN order_date = CURDATE() AND status = 'Invoiced'
						THEN COALESCE(booking_fee * rates, 0)
						ELSE 0
					END
				) AS today,

				-- Minggu ini
				SUM(
					CASE
						WHEN WEEK(order_date, 1) = WEEK(CURDATE(), 1)
							AND YEAR(order_date) = YEAR(CURDATE())
							AND status = 'Invoiced'
						THEN COALESCE(booking_fee * rates, 0)
						ELSE 0
					END
				) AS this_week,

				-- Bulan ini
				SUM(
					CASE
						WHEN MONTH(order_date) = MONTH(CURDATE())
							AND YEAR(order_date) = YEAR(CURDATE())
							AND status = 'Invoiced'
						THEN COALESCE(booking_fee * rates, 0)
						ELSE 0
					END
				) AS this_month,

				-- Tahun ini
				SUM(
					CASE
						WHEN YEAR(order_date) = YEAR(CURDATE())
							AND status = 'Invoiced'
						THEN COALESCE(booking_fee * rates, 0)
						ELSE 0
					END
				) AS this_year
			FROM orders
			WHERE deleted_at IS NULL;
		");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
