<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('google_search_logs', function (Blueprint $table) {
			$table->id();
			$table->string('query');
			$table->timestamp('created_at');
		});

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
