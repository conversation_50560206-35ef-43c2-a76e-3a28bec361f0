<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\InvoiceApprovalLog;
use App\Models\User;

class InvoiceApprovalLogPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, InvoiceApprovalLog $invoiceapprovallog): bool
    {
        return $user->checkPermissionTo('view InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, InvoiceApprovalLog $invoiceapprovallog): bool
    {
        return $user->checkPermissionTo('update InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, InvoiceApprovalLog $invoiceapprovallog): bool
    {
        return $user->checkPermissionTo('delete InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, InvoiceApprovalLog $invoiceapprovallog): bool
    {
        return $user->checkPermissionTo('restore InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, InvoiceApprovalLog $invoiceapprovallog): bool
    {
        return $user->checkPermissionTo('replicate InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, InvoiceApprovalLog $invoiceapprovallog): bool
    {
        return $user->checkPermissionTo('force-delete InvoiceApprovalLog');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any InvoiceApprovalLog');
    }
}
