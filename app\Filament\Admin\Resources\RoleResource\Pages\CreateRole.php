<?php

namespace App\Filament\Admin\Resources\RoleResource\Pages;

use App\Filament\Admin\Resources\RoleResource;
use Althinect\FilamentSpatieRolesPermissions\Resources\RoleResource\Pages\CreateRole as BaseCreateRole;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class CreateRole extends BaseCreateRole
{
    protected static string $resource = RoleResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Mulai transaksi database untuk memastikan semua operasi berhasil atau gagal bersama
        DB::beginTransaction();

        try {
            // Buat role baru
            $role = Role::create([
                'name' => $data['name'],
                'guard_name' => $data['guard_name'] ?? config('filament-spatie-roles-permissions.default_guard_name', 'web'),
            ]);

            // Ambil semua permission yang dipilih dari form
            $selectedPermissions = collect();

            // Kumpulkan semua permission yang dipilih dari berbagai checkbox list
            foreach ($data as $key => $value) {
                if ((str_starts_with($key, 'permissions_web_') || str_starts_with($key, 'permissions_api_')) && is_array($value)) {
                    $selectedPermissions = $selectedPermissions->merge($value);
                }
            }

            // Jika ada permission yang dipilih langsung (tidak melalui grup)
            if (isset($data['permissions']) && is_array($data['permissions'])) {
                $selectedPermissions = $selectedPermissions->merge($data['permissions']);
            }

            // Assign permission ke role
            if ($selectedPermissions->isNotEmpty()) {
                $role->syncPermissions($selectedPermissions->unique()->toArray());
            }

            DB::commit();

            Notification::make()
                ->title('Role berhasil dibuat')
                ->success()
                ->send();

            return $role;
        } catch (\Exception $e) {
            DB::rollBack();

            Notification::make()
                ->title('Gagal membuat role')
                ->body('Terjadi kesalahan: ' . $e->getMessage())
                ->danger()
                ->send();

            throw $e;
        }
    }
}
