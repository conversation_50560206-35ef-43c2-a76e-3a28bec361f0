<?php

namespace App\Filament\Mc\Resources\McAgentResource\Pages;

use App\Filament\Mc\Resources\McAgentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateMcAgent extends CreateRecord
{
	protected static string $resource = McAgentResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	public function getHeading(): string
	{
		return 'New Agent';
	}
	public function getRedirectUrl(): string
	{
		return url()->previous(); // atau route valid yang ringan
	}
}
