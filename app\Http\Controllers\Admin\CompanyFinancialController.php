<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CompanyDeposito;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Spatie\Browsershot\Browsershot;

class CompanyFinancialController extends Controller
{
	public function financialPerformancePayload(?string $startDate = null): array
	{
		$query = CompanyDeposito::with(['company', 'bank', 'order']);

		if ($startDate) {
			$query->whereDate('trx_date', $startDate);
		}

		$report = $query->get()->map(function ($item) {
			$isIncoming = $item->trx_type === 'in';
			$isOutgoing = !$isIncoming;

			return [
				'id'            => $item?->id,
				'company_id'    => $item?->company_id,
				'company'       => $item?->company?->name ?? 'Unknown Company',
				'bank_id'       => $item?->bank_id,
				'bank'          => $item?->bank?->bank_name ?? 'Unknown Bank',
				'date'          => $item?->trx_date,
				'type'          => $item?->trx_type,
				'in_amount'		=> $isIncoming ? $item?->amount : null,
				'out_amount'	=> $isOutgoing ? $item?->amount : null,
				'description'   => $item?->description,
			];
		});

		// Grouping & balance logic
		$grouped = $report->groupBy('company_id')->map(function ($companyGroup) {
			/** @var \Illuminate\Support\Collection $companyGroup */
			return $companyGroup->groupBy('bank_id')->map(function ($bankGroup) {
				$totalIn = $bankGroup->sum('in_amount');
				$totalOut = $bankGroup->sum('out_amount');

				$balance = $totalIn - $totalOut;
				return [
					'balance' => $balance,
					'transactions' => $bankGroup->values()->all(),
				];
			});
		});

		$reportArray = $grouped->toArray();

		$inTotal = 0;
		$outTotal = 0;
		$balanceTotal = 0;

		foreach ($reportArray as $company) {
			foreach ($company as $bank) {
				$balanceTotal += $bank['balance'];
				foreach ($bank['transactions'] as $trx) {
					$inTotal += $trx['in_amount'] ?? 0;
					$outTotal += $trx['out_amount'] ?? 0;
				}
			}
		}

		$previousBalance = 0;

		if ($startDate) {
			$previousQuery = CompanyDeposito::query()
				->whereDate('trx_date', '<', $startDate)
				->get();

			foreach ($previousQuery as $item) {
				$amount = $item->amount ?? 0;
				if ($item->trx_type === 'in') {
					$previousBalance += $amount;
				} else {
					$previousBalance -= $amount;
				}
			}
		}

		return [
			'report' => $reportArray,
			'balance' => $balanceTotal,
			'in_total' => $inTotal,
			'out_total' => $outTotal,
			'previous_balance' => $previousBalance,
		];
	}

	public function printFinancialPerformancePdf(Request $request)
	{
		$start = $request->query('start_date');

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->financialPerformancePayload($start);
		$unfilteredPayload = $this->financialPerformancePayload();

		$report = $payload['report'];
		$previousBalance = $payload['previous_balance'];
		$inTotal = $payload['in_total'];
		$outTotal = $payload['out_total'];
		$balanceTotal = $payload['balance'];
		$unfilteredReport = $unfilteredPayload['report'];

		$html = view('template.v2.financial-report-pdf', [
			'report'        => $report,
			'unfilteredReport' => $unfilteredReport,
			'previousBalance' => $previousBalance,
			'inTotal'       => $inTotal,
			'outTotal'      => $outTotal,
			'balanceTotal'  => $balanceTotal,

			'dateStart'     => $start ?? null,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		])->render();

		// Footer untuk browsershot
		$footerHtml = '<div style="font-family: Courier, monospace; font-size:10px; width:100%; text-align:center; margin:0 auto;">
			Generated by Workspace @ ' . now()->translatedFormat('d F Y') . ' &nbsp;&nbsp;|&nbsp;&nbsp; Page <span class="pageNumber"></span> of <span class="totalPages"></span>
		</div>';

		try {
			$pdf = Browsershot::html($html)
				->format('Legal')
				->margins(10, 10, 10, 10)
				->noSandbox()
				->showBackground()
				->waitUntilNetworkIdle()
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', true)
				->setOption('headerTemplate', '<div></div>')
				->setOption('footerTemplate', $footerHtml)
				->pdf();

			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="bank_statement_report.pdf"');
		} catch (\Exception $e) {
			return $html; // fallback debug
		}
	}
}
