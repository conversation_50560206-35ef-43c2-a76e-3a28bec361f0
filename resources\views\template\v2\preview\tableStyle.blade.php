<table class="w-full text-sm">
	<thead>
		<tr style="background-color: {{ $tableHeaderShading }}">
			@foreach ($visibleColumnsFiltered as $col)
				<th class="px-2 py-1 {{ getBorderClasses($tableBodyBorderStyle, true) }} {{ $headerTextColorClass }}"
					style="{{ getColumnWidthStyle($col['key']) }}">
					{{ $col['label'] }}
				</th>
			@endforeach
		</tr>
	</thead>
	<tbody>
		@foreach ($rows as $rowIndex => $row)
			@php
				$rowStyle = '';
				if ($tableRowShading === 'zebra' && $rowIndex % 2 === 1) {
					$rowStyle = "background-color: {$tableRowColor};";
				}
			@endphp

			<tr style="{{ $rowStyle }}">
				@foreach ($visibleColumnsFiltered as $colIndex => $col)
					@php
						$cellStyle = getColumnWidthStyle($col['key']);
						$cellClass = getBorderClasses($tableBodyBorderStyle);

						// Tambah border-t di baris pertama
						if ($rowIndex === 0) {
							$cellClass .= ' border-t';
						}

						// Tambah border-b di baris terakhir
						if ($rowIndex === count($rows) - 1) {
							$cellClass .= ' border-b';
						}

						// Column shading (zebra)
						if ($tableColumnShading === 'zebra' && $colIndex % 2 === 1) {
							$cellClass .= ' bg-gray-100';
						}

						// Last column shading
						if ($tableColumnShading === 'last' && $colIndex === $lastVisibleIndex) {
							$cellStyle .= "background-color: {$tableLastColumnColor};";
							$cellClass .= isDarkColor($tableLastColumnColor) ? ' text-white' : ' text-black';
						}
					@endphp

					<td class="px-2 py-4 {{ $cellClass }}" style="{{ $cellStyle }}">
						{{ $row[$col['key']] }}
					</td>
				@endforeach
			</tr>
		@endforeach
	</tbody>
	@if ($tableSummaryOffGrid === 'ongrid')
		<tfoot>
			@php
				function computeFooterClasses($loopLast, $style) {
					$baseLeft = getBorderClasses($style);
					$baseRight = getBorderClasses($style);

					if ($style === 'column') {
						$baseLeft .= ' border-s';
						$baseRight .= ' border-e';

						if ($loopLast) {
							$baseLeft .= ' border-b';
							$baseRight .= ' border-b';
						}
					}

					return [$baseLeft, $baseRight];
				}
			@endphp

			@if ($tableShowSummaryDetails)
				@foreach ($summaryDetails as $label => $value)
					@php
						[$footerClassLeft, $footerClassRight] = computeFooterClasses($loop->last, $footerBorderStyle);
						$isHighlight = in_array($label, ['Total']);
					@endphp
					<tr style="background-color: {{ $tableSummaryBg }}">
						<td colspan="{{ $colspan }}" class="text-right px-2 py-1 text-sm {{ $isHighlight ? 'font-bold' : 'font-medium' }} {{ $footerClassLeft }}">
							{{ $label }}:
						</td>
						<td class="text-right px-2 py-1 text-sm {{ $isHighlight ? 'font-bold' : 'font-medium' }} {{ $footerClassRight }}"
							style="
								background-color: {{ $tableColumnShading === 'last' ? $tableLastColumnColor : 'transparent' }};
								{{ isDarkColor($tableLastColumnColor) ? 'color:white;' : 'color:black;' }}
							">
							{{ $value }}
						</td>
					</tr>
				@endforeach
			@endif

			@foreach ($summaryMain as $label => $value)
				@php
					[$footerClassLeft, $footerClassRight] = computeFooterClasses($loop->last, $footerBorderStyle);
					$isHighlight = in_array($label, ['Total']);
				@endphp
				<tr style="background-color: {{ $tableSummaryBg }}">
					<td colspan="{{ $colspan }}" class="text-right px-2 py-1 text-sm {{ $isHighlight ? 'font-bold' : 'font-semibold' }} {{ $footerClassLeft }}">
						{{ $label }}:
					</td>
					<td class="text-right px-2 py-1 text-sm {{ $isHighlight ? 'font-bold' : 'font-medium' }} {{ $footerClassRight }}"
						style="
							background-color: {{ $tableColumnShading === 'last' ? $tableLastColumnColor : 'transparent' }};
							{{ isDarkColor($tableLastColumnColor) ? 'color:white;' : 'color:black;' }}
						">
						{{ $value }}
					</td>
				</tr>
			@endforeach
			@if ($tableShowInword)
				@php
					$terbilangClass = getBorderClasses($footerBorderStyle);
					if ($footerBorderStyle === 'column') {
						$terbilangClass .= ' border-s border-e border-b';
					} elseif ($footerBorderStyle === 'row') {
						$terbilangClass .= ' border-b';
					} elseif ($footerBorderStyle === 'full') {
						$terbilangClass .= ' border';
					}
				@endphp
				<tr>
					<td colspan="{{ $colspan + 1 }}" class="text-center px-2 py-4 text-sm {{ $terbilangClass }}">
						{{ ($companyType ?? null) == 1 ? 'Amount in words' : 'Terbilang' }}: <strong>Satu Miliar Sembilan Ratus Empat Belas Juta Empat Ratus Lima Puluh Ribu Rupiah</strong>
					</td>
				</tr>
			@endif
		</tfoot>
	@endif
</table>

{{-- table summary --}}
@if($tableSummaryOffGrid === 'offgrid')

@endif
