<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderFactory extends Factory
{
    protected $model = Order::class;

    public function definition(): array
    {
        return [
            'order_no' => 'ORD-' . $this->faker->unique()->numerify('####'),
            'order_create_by' => 1, // Default user ID
            'company_id' => 1, // Will be overridden in tests
            'currency_id' => 1, // Will be overridden in tests
            'bank_id' => 1, // Will be overridden in tests
            'order_amount' => $this->faker->randomFloat(2, 100, 10000),
            'order_date' => $this->faker->date(),
            'booking_fee' => $this->faker->randomFloat(2, 10, 100),
            'rates' => $this->faker->randomFloat(2, 14000, 16000),
            'total' => $this->faker->randomFloat(2, 100, 10000),
            'status' => $this->faker->randomElement(['Draft', 'Forwarded', 'Invoiced']),
        ];
    }
}
