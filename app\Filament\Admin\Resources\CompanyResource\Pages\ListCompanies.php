<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use App\Models\Company;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Filament\Tables\Actions\{BulkActionGroup, DeleteBulkAction, EditAction, ViewAction};
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;

class ListCompanies extends ListRecords
{
    protected static string $resource = CompanyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }


    public function getDefaultActiveTab(): string
    {
        return 'All';
    }

    protected function paginateTableQuery(Builder $query): Paginator
    {
        return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
    }

    public function getTabs(): array
    {
        return [
			'All' => Tab::make('All')->badge(Company::query()->count()),
            '1' => Tab::make('Clients')->modifyQueryUsing(function (Builder $query){
                $query->where('type', '1');
            })->badge(Company::query()->where('type', '1')->count()),
            '2' => Tab::make('Internal')->modifyQueryUsing(function (Builder $query){
                $query->where('type', '2');
            })->badge(Company::query()->where('type', '2')->count()),
        ];
    }

	public function table(Table $table): Table
    {
        return $table
			->query(
				Company::withCount('banks')
			)
            ->columns([
                TextColumn::make('name')
					->words(4)
					->badge()
					->color('gray')
					->tooltip(fn ($state) => $state)
					->sortable()
					->searchable(),
                TextColumn::make('type')->formatStateUsing(fn ($state) => $state == 1 ? 'Client' : 'Internal')
                    ->searchable()
					->badge()
					->sortable()
					->color(fn ($state) => $state == 1 ? 'warning' : 'success'),
				TextColumn::make('template')
					->label('Has Template')
					->badge()
					->color(fn (?string $state): string => match (true) {
						is_null($state) => 'danger',
						default => 'success',
					})
					->formatStateUsing(fn (?string $state): string => $state ? 'Attached' : 'No Template'),
				TextColumn::make('banks_count')
                ->label('Has Banks')
                ->badge()
				->sortable()
                ->color(fn (int $state) => $state === 0 ? 'danger' : 'success'),
            ])
            ->filters([
                //
            ])
            ->actions([
                // ViewAction::make(),
                EditAction::make()->iconButton()->tooltip('Edit'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
