<?php

namespace App\Filament\Mc\Resources\McDepositResource\Pages;

use App\Filament\Mc\Resources\McDepositResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditMcDeposit extends EditRecord
{
    protected static string $resource = McDepositResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	public function getHeading(): string
	{
		return 'Edit Customer Deposit';
	}
    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
