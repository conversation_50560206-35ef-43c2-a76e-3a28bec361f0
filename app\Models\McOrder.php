<?php

namespace App\Models;

use App\Http\Controllers\Admin\McCustomerClosingController;
use App\Services\RecalculateCustomerBalanceService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class McOrder extends Model
{
	use HasFactory, SoftDeletes;
	protected $table = 'mc_orders';

	protected $fillable = [
		'order_code',
		'order_date',
		'mc_customer_id',
		'currency_id',
		'bank_group_id',
		'bank_id',
		'amount',
		'buy_rates',
		'sell_rates',
		'charges',
		'total_order',
		'is_cancelled',
		'status',
	];

	// $amount = $order->amount;
	// $rateBuy = $order->buy_rates;
	// $rateSell = $order->sell_rates;
	// $buy = $amount * $rateBuy;
	// $sell = $amount * $rateSell;
	// $profitLoss = $sell - $buy;

	public function customer(): BelongsTo
	{
		return $this->belongsTo(McCustomer::class, 'mc_customer_id');
	}

	protected static function booted()
	{
		static::creating(function ($order) {
			//skip if order_code is already set to accomodate manual order_code
			if (!empty($order->order_code)) {
				return;
			}

			//normal run
			$monthMapping = [
				'01' => 'A', '02' => 'B', '03' => 'C', '04' => 'D', '05' => 'E', '06' => 'F', '07' => 'G', '08' => 'H', '09' => 'I', '10' => 'J', '11' => 'K', '12' => 'L',
			];

			// $tc = time();
			$tc = Str::uuid()->toString();
			$currentMonth = now()->format('m');
			$mappedMonth = $monthMapping[$currentMonth];
			$yearNumber = now()->format('y');
			$userId = Auth::user()->id;
			$prefix = "MCO{$yearNumber}{$mappedMonth}U{$userId}-";

			// Cari order_code terakhir dengan prefix itu
			$lastCode = McOrder::where('order_code', 'like', "{$prefix}%")
				->orderByDesc('order_code')
				->value('order_code');

			// Ambil 5 digit terakhir dari kode sebelumnya
			$lastNumber = 0;
			if ($lastCode && preg_match('/(\d{5})$/', $lastCode, $matches)) {
				$lastNumber = (int) $matches[1];
			}

			// Increment dan format jadi 5 digit
			$nextNumber = str_pad($lastNumber + 1, 5, '0', STR_PAD_LEFT);

			// Gabungkan jadi order_code baru
			$order->order_code = "{$prefix}{$nextNumber}";
		});

		static::created(function ($order) {
			if ($order->total_order && $order->bank_id) {
				McDeposit::create([
					'mc_customer_id' => $order->mc_customer_id,
					'bank_id' => $order->bank_id,
					'amount' => $order->total_order,
					'ref_code' => $order->order_code,
					'slip_date' => $order->order_date,
					'trx_type' => 'order',
				]);
			}
		});

		static::updating(function ($order) {
			// $order->old_customer_id = $order->getOriginal('mc_customer_id');
			if ($order->isDirty([
					'mc_customer_id', 'total_order', 'amount', 'buy_rates', 'sell_rates', 'bank_id', 'is_cancelled',
				])) {
				McDeposit::where('ref_code', $order->order_code)
					->where('trx_type', 'order')
					->delete();
			}
		});

		// Create new deposit after order is updated
		static::updated(function ($order) {

			if ($order->wasChanged([
					'mc_customer_id', 'total_order', 'amount', 'buy_rates', 'sell_rates', 'bank_id', 'is_cancelled',
				])) {
				if ($order->total_order && $order->buy_rates && $order->bank_id) {
					McDeposit::firstOrCreate([
						'ref_code' => $order->order_code,
						'trx_type' => 'order',
					], [
						'mc_customer_id' => $order->mc_customer_id,
						'bank_id' => $order->bank_id,
						'amount' => $order->total_order,
						'slip_date' => $order->order_date,
					]);
				}
			}
		});
		static::deleting(function ($order) {
			McDeposit::where('ref_code', $order->order_code)
				->where('trx_type', 'order')
				->delete();
		});
	}

	public function currency(): BelongsTo
	{
		return $this->belongsTo(Currency::class, 'currency_id');
	}

	public function bank():BelongsTo
	{
		return $this->belongsTo(McBank::class);
	}
}
