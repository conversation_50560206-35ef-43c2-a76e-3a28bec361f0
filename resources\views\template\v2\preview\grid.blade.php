@php
    $sizeClasses = [
        'h1' => 'text-4xl',     // besar
        'h2' => 'text-3xl',
        'h3' => 'text-2xl',
        'h4' => 'text-xl',
        'h5' => 'text-lg',
        'h6' => 'text-base',    // kecil
    ];

    $horizontalAlign = [
        'text-start' => 'text-start',
        'text-center' => 'text-center',
        'text-end' => 'text-end',
    ];

    $verticalAlign = [
        'align-items-start' => 'items-start',
        'align-items-center' => 'items-center',
        'align-items-end' => 'items-end',
    ];

    // Menentukan urutan flex children berdasarkan posisi logo
    $flexOrder = $logo_position === 'logo-right' ? ['company-info', 'logo'] : ['logo', 'company-info'];
@endphp


<div class="bg-white shadow p-4 rounded-2xl border border-gray-300 mb-2 mx-auto">
    <div class="flex flex-row {{ $verticalAlign[$vertical_align] ?? 'items-center' }} space-x-4 gap-2">
        @foreach($flexOrder as $item)
            @if($item === 'logo' && $headingShowLogo)
                <div class="w-32 h-32 p-1 box-border border border-gray-300 rounded overflow-hidden flex items-center justify-center gap-2">
                    <img src="{{ asset('images/logolight.png') }}" alt="Company Logo" class="w-24 h-auto object-contain">
                </div>
            @elseif($item === 'company-info')
                <div class="{{ $horizontalAlign[$horizontal_align] ?? 'text-start' }} flex-grow">
                    <span class="{{ $sizeClasses[$heading_size] ?? 'text-base' }} mt-2 {{ $heading_weight }}" style="color: {{ $heading_color }};">
                        Company Name
                    </span>

                    @if($show_address)
                        <ul class="text-xs text-gray-700 leading-snug mt-1 space-y-0">
                            <li>Company Address. Company Phone - Company Email</li>
                        </ul>
                    @endif
                </div>
            @endif
        @endforeach
    </div>
</div>
<div class="text-center text-xs text-gray-500">
    This layout is suitable for companies with square logo images
</div>
