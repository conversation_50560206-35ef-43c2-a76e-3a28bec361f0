<?php

namespace App\Filament\Admin\Pages;

use App\Models\CompanyDeposito;
use Filament\Forms\Components\{Actions, Select, Section, DatePicker, Group};
use Filament\Forms\Components\Actions\Action;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\ActionSize;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DepositClosing extends Page
{
	protected static ?string $title = 'Closing Deposit';
	protected static string $view = 'filament.admin.pages.closing';
	protected static ?string $navigationIcon = 'heroicon-o-archive-box-arrow-down';
	protected static bool $shouldRegisterNavigation = false;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	protected static ?string $navigationGroup = 'System';
	public ?array $formData = [];
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['Super Admin']);
    }
	public function form(Form $form): Form
	{
		return $form
			->schema([
				Group::make()
					->schema([])->columnSpanFull()->columns(4),
				DatePicker::make('end_date')
					->label('Sampai Tanggal')
					->inlineLabel()
					->columnSpan(2)
					->required(),
				Actions::make([
					Action::make('closing')
						->label('Close Transaction')
						->color('danger')
						->size(ActionSize::Small)
						->icon('heroicon-o-archive-box-arrow-down')
						->requiresConfirmation()
						->action(function () {
							$this->runClosing();
						})
						->modalHeading('Closing Transaction')
						->modalDescription('Are you sure you\'d like to close these transaction? This cannot be undone.')
						->modalSubmitActionLabel('Yes, close them'),
				])->columnSpan(2)
			])->columns(4)
			->statePath('formData');
	}

	public function runClosing()
	{
		$endDate = $this->formData['end_date'] ?? null;

		if (!$endDate) {
			Notification::make()
				->title('Tanggal tidak valid')
				->body('Silakan pilih tanggal akhir terlebih dahulu.')
				->danger()
				->send();
			return;
		}

		DB::beginTransaction();

		try {
			// Ambil semua data yang akan di-closing
			$depositos = CompanyDeposito::whereDate('trx_date', '<=', $endDate)->get();

			if ($depositos->isEmpty()) {
				Notification::make()
					->title('Tidak ada data untuk di-closing')
					->body('Tidak ditemukan data sebelum tanggal tersebut.')
					->warning()
					->send();
				return;
			}

			$userId = Auth::id();

			// Backup semua data ke tabel backup
			$backupData = $depositos->map(function ($deposit) use ($userId) {
				return [
					'original_id'   => $deposit->id,
					'company_id'    => $deposit->company_id,
					'order_id'      => $deposit->order_id,
					'bank_id'       => $deposit->bank_id,
					'description'   => $deposit->description,
					'trx_type'      => $deposit->trx_type,
					'trx_date'      => $deposit->trx_date,
					'amount'        => $deposit->amount,
					'backup_by'     => $userId,
					'backed_up_at'  => now(),
				];
			})->toArray();

			DB::table('company_deposito_backups')->insert($backupData);

			// Force delete dari tabel utama
			CompanyDeposito::whereIn('id', $depositos->pluck('id'))->forceDelete();

			// Ringkas data berdasarkan bank_id + trx_type
			$grouped = $depositos->groupBy(fn($item) => $item->bank_id . '|' . $item->trx_type);

			foreach ($grouped as $key => $items) {
				[$bankId, $trxType] = explode('|', $key);

				DB::table('company_depositos')->insert([
					'company_id' => (int) $items->first()->company_id,
					'order_id'   => null,
					'bank_id'    => (int) $bankId,
					'trx_type'   => $trxType,
					'trx_date'   => now()->toDateString(),
					'amount'     => $items->sum('amount'),
					'description' => "Ringkasan {$trxType} hasil closing",
					'created_at' => now(),
					'updated_at' => now(),
				]);
			}

			DB::commit();

			Notification::make()
				->title('Close Successfully')
				->body('Deposit transaction closed successfully.')
				->success()
				->send();
		} catch (\Throwable $e) {
			DB::rollBack();

			Notification::make()
				->title('Unsuccessful Action')
				->body('Terjadi kesalahan saat proses closing: ' . $e->getMessage())
				->danger()
				->send();
		}
	}
}
