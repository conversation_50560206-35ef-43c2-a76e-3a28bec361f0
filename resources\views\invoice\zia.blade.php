<!DOCTYPE html>
<html lang="en" class="root-text-sm">

<head>
	@include('invoice.metatitle')
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet"
		integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="{{ $payload['invoice']->company->font->source ?? 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap' }}" rel="stylesheet">

	@include('invoice.css')
</head>

<body style="font-family: {{$payload['invoice']->company->font->name ?? ''}} !important">
	<div class="main small">
		<div class="">
			<div class="row d-flex justify-content-between align-items-center px-3">
				<!-- Left Grid: Logo & Text -->
				<div class="d-flex align-items-center col-8">
						@if($payload['invoice']->company->logo)
							<img src="{{ url('storage/' . $payload['invoice']->company->logo) }}" class="mx-2 mt-1 profile-image ml-auto" style="width: 120px; height:auto">
						@endif
						{{-- <div
							@if($payload['invoice']->company->text_color)
								style="color: {{ $payload['invoice']->company->text_color }};"
							@endif>
							<h2 class="fw-bold">{{$payload['invoice']->company->name}}</h2>
						</div> --}}
					<div class="col-8 d-flex align-items-center">
					</div>
				</div>
				<span class="text-end h1 ml-auto col-4">INVOICE</span>
			</div>
			<hr class="mt-3">
		</div>

		{{-- <div class="pagebreak"></div> --}}
		<div class="px-3">
			<div class="row d-flex justify-content-between align-items-start">
				<div class="col-auto">
					<ul class="list-unstyled">
						<li class="fw-bold h5">
							<span class="{{$payload['invoice']->company->heading_size}}">{{$payload['invoice']->company->name}}</span>
						</li>
						<li>
							@if ($payload['invoice']->company->phone)
								<span>{{$payload['invoice']->company->phone}},</span>
							@endif
							@if ($payload['invoice']->company->email)
								<span>{{$payload['invoice']->company->email}}</span>
							@endif
						</li>
						<li class="d-flex justify-content-start align-items-start">
							<span>{!!$payload['invoice']->company->address!!}</span>
						</li>
					</ul>
				</div>
				<span class="col-4 text-xs">
					<div>
						<span class="d-flex justify-content-between">
							<span class="col-4 text-end">DATE:</span>
							<span class="col-7">{{ date('F d, Y', strtotime($payload['invoice']->invoice_date)) }}</span>
						</span>
						<span class="d-flex justify-content-between fw-bold">
							<span class="col-4 text-end">INVOICE #:</span>
							<span class="col-7">{{$payload['invoice']->invoice_no}}</span>
						</span>
					</div>
				</span>
				<hr>
			</div>
			<div class="row mb-2">
				<div class="d-flex justify-content-between align-items-end mb-3">
					<div class="col-8">
						<span>Bill to:</span>
						<div>
							<span class="fw-bold">
								{{$payload['invoice']->client->name}}
							</span>
							<span>{!!$payload['invoice']->client->address!!}</span>
						</div>
					</div>
					<div class="col-4">
						<div>
							<table class="table table-bordered table-sm w-100">
								<thead>
									<th class="text-center">Payment Terms</th>
									<th class="text-center">Due Date</th>
								</thead>
								<tbody>
									<tr>
										<?php
											$invoiceDate = date_create($payload['invoice']->invoice_date);
											$invoiceDue = date_create($payload['invoice']->due_date);
											$difference = date_diff($invoiceDate, $invoiceDue);
										?>
										<td class="text-center">
											{{ $difference->days }} day(s)
										</td>

										<td class="text-center">
											{{ date('F d, Y', strtotime($payload['invoice']->due_date)) }}
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<div class="col-12">
					{{-- <span class="fw-bold mb-2">Customer Order</span> --}}
					<table class="table table-bordered table-sm">
						<thead class=""
								@if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
									style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
								@endif>
							<tr>
								<th class="text-center" style="border: 1px solid #ddd">Description</th>
								<th class="text-end" style="border: 1px solid #ddd" width="15%">Qty</th>
								<th class="text-end" style="border: 1px solid #ddd" width="15%">Price</th>
								<th class="text-end" style="border: 1px solid #ddd" style="background-color: #F4F2F8" width="20%">Amount</th>
							</tr>
						</thead>
						<tbody>
							@foreach ($payload['invoice']->invoiceDetails as $items)
								<tr>
									<td class="vertical-middle">{!!$items->description!!}</td>
									<td class="text-end vertical-middle">{{number_format($items->quantity, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle">{{number_format($items->price, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle" style="background-color: #F4F2F8" >{{$items->invoice->currency->symbol}} {{number_format($items->sub_total, 2, '.', ',')}}</td>
								</tr>
							@endforeach
						</tbody>
						<tfoot
							@if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
								style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
							@endif>
							<tr class="">
								<td class="text-end" colspan="3">
									BOOKING FEE :
								</td>
								<td class="text-end fw-bold" style="background-color: #F4F2F8" >
									<span>{{$payload['invoice']->currency->symbol}} {{number_format($payload['invoice']->booking_fee, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-end" colspan="3">
									RATE :
								</td>
								<td class="text-end fw-bold" style="background-color: #F4F2F8" >
									<span>Rp {{number_format($payload['invoice']->rates, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-end fw-bold" colspan="3">
									TOTAL :
								</td>
								<td class="text-end fw-bold" style="background-color: #F4F2F8" >
									<span>Rp {{number_format($payload['invoice']->invoice_amount, 2, '.', ',')}}</span>
								</td>
							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			<table class="table table-bordered w-100" style="background-color: #F4F2F8" >
				<tbody>
					<tr>
						<td class="text-center">
							{{ optional($payload['invoice']->company)->type === '1' ? 'Inword' : 'Terbilang' }}:
							<span class="fw-semibold">{{$payload['invoice']->amount_inword}}</span>
						</td>
					</tr>
				</tbody>
			</table>
			{{-- <hr> --}}
			<div class="row-mb-5 col-12">
				{{-- <div class="mb-3" style="font-style: italic">
					<span class="text-start">
						Payment must be completed within 14 calendar days from the invoice date.
					</span>
				</div> --}}
				<div class="row d-flex justify-content-between align-items-start">
					<div class="col-7">
						@include('partials.bankinfo')
					</div>
					<div class="col-5 text-center">
						@if($payload['invoice']->company->signature)
							<img src="{{ url('storage/' . $payload['invoice']->company->signature) }}" class="mx-2 mt-1 profile-image ml-auto" style="max-height:5rem"><br>
						@endif
						@if ($payload['invoice']->company->signature_name)
							<u>{{$payload['invoice']->company->signature_name}}</u>
						@endif
					</div>
				</div>
				@if ($payload['invoice']->remarks)
					<div>
						<span class="fw-bold d-block">Remarks</span>
						<p class="m-0 text-sm">{!!$payload['invoice']->remarks!!}</p>
					</div>
				@endif
			</div>
			{{-- <hr> --}}
		</div>
		<div class="footer">
			<div class="container mt-5">
				<div style="margin: 0 auto; text-align: center;">
					<div class="col-12" style="font-family: \'Courier New\', monospace; font-size: 11px;">
						<span class="text-center" style="font-size: 10px;">
							{{$payload['invoice']->company->name}}
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous">
	</script>
<script>
</script>
</body>

</html>
