<!DOCTYPE html>
<html lang="en" class="root-text-sm">

<head>
	@include('invoice.metatitle')
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet"
		integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="{{ $payload['invoice']->company->font->source ?? 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap' }}" rel="stylesheet">

	@include('invoice.css')
</head>

<body style="font-family: {{$payload['invoice']->company->font->name ?? ''}} !important">
	<div class="main small">
		<div class="px-3">
			<div class="row justify-content-center align-items-start">
				<!-- Left Grid: Logo & Text -->
				<div class="d-flex align-items-start justify-content-between">
					<div class="d-flex align-items-start">
						@if($payload['invoice']->company->logo)
							<img src="{{ url('storage/' . $payload['invoice']->company->logo) }}" class="mx-2 mt-1 profile-image ml-auto" style="width: 100px; height:auto">
						@endif
						<div class="text-center justify-content-center"
							@if($payload['invoice']->company->text_color)
								style="color: {{ $payload['invoice']->company->text_color }};"
							@endif>
							<span class="fw-bold {{ $payload['invoice']->company->heading_size ?? 'h3' }}">{{$payload['invoice']->company->name}}</span>
                            <span>{!!$payload['invoice']->company->address!!}</span>
                            <ul class="list-unstyled">
                                @if ($payload['invoice']->company->phone)
                                <li><i class="bi bi-telephone-fill"></i> {{$payload['invoice']->company->phone}}</li>
                                @endif
                                @if ($payload['invoice']->company->email)
                                <li><i class="bi bi-envelope-at"></i> {{$payload['invoice']->company->email}}</li>
                                @endif
                            </ul>
						</div>
					</div>
				</div>
			</div>
			<hr>
		</div>

		{{-- <div class="pagebreak"></div> --}}
		<div class="px-3">
			<div class="row mb-2">
				<div class="d-flex justify-content-between align-items-start mb-3">
					<div class="col-7">
						<span>Bill to:</span>
						<div>
							<span class="fw-bold">
								{{$payload['invoice']->client->name}}
							</span>
							<span>{!!$payload['invoice']->client->address!!}</span>
						</div>
					</div>
					<div class="col-4">
						<div>
							<span class="d-flex justify-content-between fw-bold">
								<span class="col-5">Invoice No</span>
								<span class="col-7">: {{$payload['invoice']->invoice_no}}</span>
							</span>
							<span class="d-flex justify-content-between">
								<span class="col-5">Invoice Date</span>
								<span class="col-7">: {{date('d/m/Y', strtotime($payload['invoice']->invoice_date))}}</span>
							</span>
							<span class="d-flex justify-content-between">
								<span class="col-5">Due</span>
								<span class="col-7">: {{date('d/m/Y', strtotime($payload['invoice']->due_date))}}</span>
							</span>
						</div>
					</div>
				</div>
				<div class="col-12">
					{{-- <span class="fw-bold mb-2">Customer Order</span> --}}
					<table class="table table-bordered table-sm">
						<thead class=""
								@if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
									style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
								@endif>
							<tr>
								<th class="text-center" style="border: 1px solid #ddd">Description</th>
								<th class="text-end" style="border: 1px solid #ddd" width="10%">Qty</th>
								<th class="text-end" style="border: 1px solid #ddd" width="15%">Price</th>
								<th class="text-end" style="border: 1px solid #ddd" width="20%">Total</th>
							</tr>
						</thead>
						<tbody>
							@foreach ($payload['invoice']->invoiceDetails as $items)
								<tr>
									<td class="vertical-middle">{!!$items->description!!}</td>
									<td class="text-end vertical-middle">{{number_format($items->quantity, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle">{{number_format($items->price, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle">{{$items->invoice->currency->symbol}} {{number_format($items->sub_total, 2, '.', ',')}}</td>
								</tr>
							@endforeach
						</tbody>
                        <tfoot>
							<tr class="">
								<td class="text-end" colspan="3">
									BOOKING FEE :
								</td>
								<td class="text-end fw-bold">
									<span>{{$payload['invoice']->currency->symbol}} {{number_format($payload['invoice']->booking_fee, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-end" colspan="3">
									RATE :
								</td>
								<td class="text-end fw-bold">
									<span>Rp {{number_format($payload['invoice']->rates, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr @if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
								style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
							@endif>
								<td class="text-center fw-bold text-uppercase" colspan="2">
                                    {{ optional($payload['invoice']->company)->type === '1' ? 'Inword' : 'Terbilang' }}:
									<span class="fw-semibold">{{$payload['invoice']->amount_inword}}</span>
                                </td>
								<td class="text-end fw-bold">
									TOTAL :
								</td>
								<td class="text-end fw-bold">
									<span>Rp {{number_format($payload['invoice']->invoice_amount, 2, '.', ',')}}</span>
								</td>
							</tr>
						</tfoot>
						{{-- <tfoot
							@if($payload['company']->bg_color && $payload['company']->footer_color)
								style="background-color: {{ $payload['company']->bg_color }}; color: {{ $payload['company']->footer_color }};"
							@endif>
							<tr class="">
								<td class="text-start pe-5" colspan="2">
									{{ optional($payload['invoice']->company)->type === '1' ? 'Inword' : 'Terbilang' }}:
									 <span class="fw-semibold">{{$payload['invoice']->amount_inword}}</span>
								</td>
								<td class="text-end">
									TOTAL:
								</td>
								<td class="text-end fw-bold">
									<span>{{$payload['invoice']->currency->symbol}} {{number_format($payload['invoice']->invoice_amount, 2, '.', ',')}}</span>
								</td>
							</tr>
						</tfoot> --}}
					</table>
				</div>
			</div>
			<hr>
			<div class="row-mb-5 col-12">
				<div class="row d-flex justify-content-end align-items-start">
					<div class="col-5 text-center">
						@if($payload['invoice']->company->signature)
							<img src="{{ url('storage/' . $payload['invoice']->company->signature) }}" class="mx-2 mt-1 profile-image ml-auto" style="max-height:5rem"><br>
						@endif
						@if ($payload['invoice']->company->signature_name)
							<u>{{$payload['invoice']->company->signature_name}}</u>
						@endif
					</div>
				</div>
                <div class="text-small">
                    @include('partials.rabul')
                </div>
				<div class="mb-3" style="font-style: italic">
					<span class="text-start">
						Ensure payment is made within 7 calendar days from the invoice date.
					</span>
				</div>
				@if ($payload['invoice']->remarks)
					<div>
						<span class="fw-bold d-block">Remarks</span>
						<p class="m-0 text-sm">{!!$payload['invoice']->remarks!!}</p>
					</div>
				@endif
			</div>
			<hr>
		</div>
		{{-- <div class="footer">
			<div class="container mt-5">
				<div style="margin: 0 auto; text-align: center;">
					<div class="col-12" style="font-family: \'Courier New\', monospace; font-size: 11px;">
						<span class="text-center" style="font-size: 10px;">
							This is a digitally generated invoice, no authorization signature is required
						</span>
					</div>
				</div>
			</div>
		</div> --}}
	</div>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous">
	</script>
<script>
</script>
</body>

</html>
