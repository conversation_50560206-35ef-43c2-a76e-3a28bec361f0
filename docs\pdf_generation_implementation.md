# PDF Generation Implementation dengan Spatie Browsershot

## Overview
Implementasi PDF generation untuk invoice menggunakan Spatie Browsershot v5 tanpa menyimpan ke database.

## Fitur yang Diimplementasikan

### 1. **Dual Mode Operation**
- **Preview Mode**: `?preview=true` - Menampilkan HTML preview di browser
- **PDF Mode**: Default - Generate dan download PDF langsung

### 2. **PDF Configuration**
- Paper size dari `layout_config` invoice template (default: Legal)
- Margins: 10mm pada semua sisi
- Background images dan colors dipertahankan
- Print media emulation untuk styling yang optimal

### 3. **Error Handling**
- Fallback ke preview mode jika PDF generation gagal
- Logging error untuk debugging
- User-friendly error messages

## File yang Dimodifikasi

### 1. **InvoiceController.php**
```php
// Imports ditambahkan
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Log;

// Method printInvoice diupdate untuk support Request parameter
public function printInvoice($record, Request $request)

// Helper methods ditambahkan:
- generatePreviewView()
- generateInvoiceHtml()
- getPaperSize()
- generateFilename()
```

### 2. **ListInvoices.php (Filament Resource)**
```php
// Action printPreview diupdate untuk preview mode
Action::make('printPreview')
    ->url(fn (Invoice $record) => route('printInvoice', ['record' => $record, 'preview' => 'true']))

// Action baru untuk download PDF
Action::make('downloadPdf')
    ->url(fn (Invoice $record) => route('printInvoice', $record))
```

## Cara Penggunaan

### 1. **Preview Invoice**
- Klik tombol "Print Preview" di Filament table
- Membuka tab baru dengan HTML preview
- URL: `/management/{id}/printInvoice?preview=true`

### 2. **Download PDF**
- Klik tombol "Download PDF" di Filament table
- Langsung download PDF tanpa menyimpan ke database
- URL: `/management/{id}/printInvoice`

## Template Support

### V1 Templates
- Menggunakan template dari `resources/views/invoice/`
- Footer otomatis ditambahkan
- Template: `invoice.{company.template}`

### V2 Templates
- Menggunakan `template.v2.printPreview`
- Layout config dari invoice template
- Tailwind CSS support

## Paper Size Configuration

Paper size diambil dari `invoice_templates.layout_config`:
```json
{
  "paper_size": "Legal"  // A4, Letter, Legal, dll
}
```

Default: Legal jika tidak dikonfigurasi

## Filename Generation

Format: `Invoice_{CompanyName}_{InvoiceNo}.pdf`
- Special characters diganti dengan underscore
- Aman untuk semua OS

## Error Handling

1. **PDF Generation Gagal**
   - Fallback ke preview mode
   - Error message ditampilkan
   - Log error untuk debugging

2. **Template Tidak Ditemukan**
   - Fallback ke default template
   - Graceful degradation

## Dependencies

- **Spatie Browsershot v5**: Sudah terinstall
- **Puppeteer**: Sudah terinstall via npm
- **Chrome/Chromium**: Required oleh Browsershot

## Testing

### Manual Testing
1. Buka Filament Invoice list
2. Test "Print Preview" - harus buka HTML preview
3. Test "Download PDF" - harus download PDF file
4. Verify filename format
5. Check PDF content dan formatting

### Error Testing
1. Test dengan invoice tanpa template
2. Test dengan layout_config kosong
3. Test dengan company name yang mengandung special characters

## Performance Considerations

- PDF generation membutuhkan waktu 2-5 detik
- Memory usage sekitar 50-100MB per request
- Tidak ada caching (sesuai requirement)
- Network idle wait untuk memastikan assets loaded

## Security Notes

- Tidak ada file disimpan di server
- PDF langsung di-stream ke browser
- Filename di-sanitize untuk mencegah path traversal
- Error messages tidak expose sensitive information
