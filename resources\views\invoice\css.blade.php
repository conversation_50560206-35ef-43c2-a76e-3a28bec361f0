
<style>
	.vertical-middle {
		vertical-align: middle;
	}
	.vertical-middle > p {
		vertical-align: middle;
	}
	hr.s1 {
		height:5px;
		border-top:1px solid #000000;
		border-bottom:3px solid #000000;
	}
	.mono {
		font-family: 'Courier New', monospace;
		font-size: 15px;
	}
	tr.no-border .no-border {
		border: 0px !important;
	}

	.table-sm th,
	.table-sm td {
	padding: 0.3rem; }
	.container.page-cover {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100vh;
		width: 100%;
	}
	.root-text-sm{
		font-size: 13px;
	}
	.pagebreak {
		page-break-before: always
	}
	.gm-style-cc {
		display: none;
	}
	/* Alternatif: Memindahkan logo Google keluar layar - ini juga melanggar kebijakan Google */
	.gmnoprint.gm-bundled-control.gm-bundled-control-on-bottom {
		display: none;
	}

	@media print {
		body {
			margin: 0;
			padding: 0;
		}
		.container {
			width: 100%;
		}
		.footer {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			text-align: center;
		}
	}
</style>
