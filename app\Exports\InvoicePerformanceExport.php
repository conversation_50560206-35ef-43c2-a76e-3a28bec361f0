<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

use Maatwebsite\Excel\Concerns\FromArray;

class InvoicePerformanceExport implements FromArray, WithEvents
{
    protected $report;

    public function __construct($report)
    {
        $this->report = $report;
    }

    public function array(): array
    {
        $data = [];

        // Heading (akan berada di baris ke-2)
        $data[] = [
            'Invoice No', 'Created At', 'Invoice Date', 'Client', 'Agent',
            'Overseas Agent', 'Amount', 'Currency', 'Fee', 'Rate', 'Gross Profit'
        ];

        // Data rows
        foreach ($this->report as $row) {
            $data[] = [
                $row['invoice_no'],
                $row['created_at'],
                $row['invoice_date'],
                $row['client_name'],
                $row['nama_pt_name'],
                $row['overseas_agent'],
                (float) $row['amount'],
                $row['currency'],
                (float) $row['fee_amount'],
                (float) $row['rate'],
                (float) $row['gross_profit'],
            ];
        }

        // Footer
        $data[] = [''];
        $data[] = ['', '', '', '', '', '', '', '', '', 'Invoice Count', count($this->report)];
        $data[] = ['', '', '', '', '', '', '', '', '', 'Total Invoice Amount', collect($this->report)->sum('amount')];
        $data[] = ['', '', '', '', '', '', '', '', '', 'Gained Profit', collect($this->report)->sum('gross_profit')];

        return $data;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->insertNewRowBefore(1, 1); // Geser semua baris 1 ke bawah

                // Judul di A1:K1
                $event->sheet->mergeCells('A1:K1');
                $event->sheet->setCellValue('A1', 'Daily Performance Report');

                // Style
                $event->sheet->getStyle('A1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'size' => 14,
                        'color' => ['rgb' => '000000'],
                    ],
                    'alignment' => [
                        'horizontal' => Alignment::HORIZONTAL_CENTER,
                        'vertical' => Alignment::VERTICAL_CENTER,
                    ],
                ]);

                // Bold header (now di row 2)
                $event->sheet->getStyle('A2:K2')->getFont()->setBold(true);
				$event->sheet->getStyle('K:K')->applyFromArray([
					'font' => [
						'bold' => true,
					],
				]);
				foreach (['G', 'I', 'J', 'K'] as $column) { // G: Amount, I: Fee, J: Rate, K: Profit
					$event->sheet->getStyle("{$column}3:{$column}1000") // mulai dari row 3, anggap max 1000 baris
						->getNumberFormat()
						->setFormatCode(NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1); // 1,000.00
				}
            },
        ];
    }
}



