<?php

namespace App\Filament\Admin\Widgets;

use App\Models\InvoiceApprovalLog;
use App\Models\Invoice;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RejectionSummaryWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
    protected int | string | array $columnSpan = 4;
	protected static ?int $sort = 1;
    protected function getStats(): array
    {
        $currentMonth = now()->month;
        $currentYear = now()->year;

        // Total rejections this month
        $totalRejections = InvoiceApprovalLog::rejections()
            ->whereYear('created_at', $currentYear)
            ->whereMonth('created_at', $currentMonth)
            ->count();

        // Total invoices created this month
        $totalInvoices = Invoice::whereYear('created_at', $currentYear)
            ->whereMonth('created_at', $currentMonth)
            ->count();

        // Rejection rate
        $rejectionRate = $totalInvoices > 0 ? round(($totalRejections / $totalInvoices) * 100, 1) : 0;

        // Most rejected user this month
        $mostRejectedUser = InvoiceApprovalLog::rejections()
            ->whereYear('invoice_approval_logs.created_at', $currentYear)
            ->whereMonth('invoice_approval_logs.created_at', $currentMonth)
            ->join('users', 'invoice_approval_logs.creator_id', '=', 'users.id')
            ->select('users.name', DB::raw('COUNT(*) as rejection_count'))
            ->groupBy('users.id', 'users.name')
            ->orderBy('rejection_count', 'desc')
            ->first();

        // Most common rejection level
        $mostRejectedLevel = InvoiceApprovalLog::rejections()
            ->whereYear('created_at', $currentYear)
            ->whereMonth('created_at', $currentMonth)
            ->select('approval_type', DB::raw('COUNT(*) as count'))
            ->groupBy('approval_type')
            ->orderBy('count', 'desc')
            ->first();

        return [
            Stat::make('Total Rejections', $totalRejections)
                ->description('Rejections this month')
                ->descriptionIcon('heroicon-m-x-circle')
                ->color($totalRejections > 20 ? 'danger' : ($totalRejections > 10 ? 'warning' : 'success')),

            Stat::make('Rejection Rate', $rejectionRate . '%')
                ->description('Of total invoices created')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($rejectionRate > 30 ? 'danger' : ($rejectionRate > 15 ? 'warning' : 'success')),

            Stat::make('Most Rejected User', $mostRejectedUser ? $mostRejectedUser->name : 'None')
                ->description($mostRejectedUser ? "{$mostRejectedUser->rejection_count} rejections" : 'No rejections')
                ->descriptionIcon('heroicon-m-user-minus')
                ->color($mostRejectedUser && $mostRejectedUser->rejection_count > 5 ? 'danger' : 'info'),

            Stat::make('Common Rejection Level', $mostRejectedLevel ? ucfirst($mostRejectedLevel->approval_type) : 'None')
                ->description($mostRejectedLevel ? "{$mostRejectedLevel->count} rejections" : 'No rejections')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('info'),
        ];
    }

    protected function getHeading(): string
    {
        return 'Rejection Analytics - ' . now()->format('F Y');
    }
}
