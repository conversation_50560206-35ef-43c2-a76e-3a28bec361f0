<?php

namespace App\Filament\Admin\Resources\OrderResource\Pages;

use App\Filament\Admin\Resources\OrderResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class OrderManagement extends Page
{
    protected static string $resource = OrderResource::class;
    protected static string $view = 'filament.admin.resources.order-resource.pages.order-management';
    protected static ?string $title = 'Order Management';

    public function mount(): mixed
    {
        if (Auth::check() && Auth::user()->hasRole('Staff Invoice')) {
            return redirect(OrderResource::getUrl('list'));
        }
        return null;
    }

	protected function getHeaderActions(): array
    {
        return [
			Action::make('list')
				->tooltip('List Orders')
				->url(fn () => route('filament.admin.resources.orders.list'))
				->icon('heroicon-o-list-bullet')
				->iconButton(),
        ];
    }
}
