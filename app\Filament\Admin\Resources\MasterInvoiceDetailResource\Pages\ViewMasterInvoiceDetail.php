<?php

namespace App\Filament\Admin\Resources\MasterInvoiceDetailResource\Pages;

use App\Filament\Admin\Resources\MasterInvoiceDetailResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewMasterInvoiceDetail extends ViewRecord
{
    protected static string $resource = MasterInvoiceDetailResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
