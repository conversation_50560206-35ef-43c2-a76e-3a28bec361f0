<?php

namespace App\Filament\Mc\Widgets;

use App\Models\McCustomer;
use App\Models\McDeposit;
use App\Models\McOrder;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class McStatWidget extends BaseWidget
{
	protected int | string | array $columnSpan = 4;

	protected static string $view = 'filament.admin.widgets.mc-stat-widget';

	public function getViewData(): array
	{
		// return Cache::remember('mcdashboard.order.summary', now()->addMinutes(5), function () {

		$now = now('Asia/Jakarta');

		// tanggal dasar
		$yesterday     = $now->copy()->subDay()->toDateString();
		$today         = $now->toDateString();

		$lastWeekStart = $now->copy()->subWeek()->startOfWeek();
		$lastWeekEnd   = $now->copy()->subWeek()->endOfWeek();

		$thisWeekStart = $now->copy()->startOfWeek();
		$thisWeekEnd   = $now->copy()->endOfWeek();

		$thisMonthStart = $now->copy()->startOfMonth();
		$thisMonthEnd   = $now->copy()->endOfMonth();

		$lastMonthStart = $now->copy()->subMonth()->startOfMonth();
		$lastMonthEnd   = $now->copy()->subMonth()->endOfMonth();

		$thisYearStart = $now->copy()->startOfYear();
		$thisYearEnd   = $now->copy()->endOfYear();

		$lastYearStart = $now->copy()->subYear()->startOfYear();
		$lastYearEnd   = $now->copy()->subYear()->endOfYear();

		// helper kalkulasi
		$calcOrder = fn($query) =>
		$query->selectRaw('SUM(total_order) AS totalOrder')->value('totalOrder') ?? 0;

		$calcBalance = fn($query) =>
		$query->selectRaw('SUM(balance) AS totalBalance')->value('totalBalance') ?? 0;

		$calcPayment = fn($query) =>
		$query->selectRaw('SUM(amount) AS totalDeposit')->value('totalDeposit') ?? 0;

		// base query
		$baseOrderQuery = fn() => McOrder::query()->where('order_code', 'NOT LIKE', '%CLS%');
		$baseCustomerQuery = fn() => McCustomer::query();
		$baseDepositQuery = fn() => McDeposit::query()->where('trx_code', 'NOT LIKE', '%CLS%');

		// order
		$orderThisWeek = $calcOrder(
			$baseOrderQuery()->whereBetween('order_date', [$thisWeekStart, $thisWeekEnd])
		);
		$orderToday = $calcOrder(
			$baseOrderQuery()->whereDate('order_date', $today)
		);

		// deposit
		$balance = $calcBalance($baseCustomerQuery());
		$paymentToday = $calcPayment(
			$baseDepositQuery()->where('trx_type', 'incoming')
				->whereBetween('slip_date', [$thisWeekStart, $thisWeekEnd])
		);

		// periode
		$periods = [
			'today'      => [$today, $today],
			'yesterday'  => [$yesterday, $yesterday],
			'this_week'  => [$thisWeekStart, $thisWeekEnd],
			'last_week'  => [$lastWeekStart, $lastWeekEnd],
			'this_month' => [$thisMonthStart, $thisMonthEnd],
			'last_month' => [$lastMonthStart, $lastMonthEnd],
			'this_year'  => [$thisYearStart, $thisYearEnd],
			'last_year'  => [$lastYearStart, $lastYearEnd],
		];

		// hitung revenue dengan SQL langsung
		$results = collect($periods)->mapWithKeys(function ($range, $key) use ($baseOrderQuery) {
			[$startDate, $endDate] = $range;

			$profit = (clone $baseOrderQuery)()
				->whereBetween('order_date', [$startDate, $endDate])
				->selectRaw('SUM((sell_rates - buy_rates) * amount) AS total_profit')
				->value('total_profit') ?? 0;

			return [$key => $profit];
		});

		return [
			'deposit' => [
				'balance'  => $balance,
				'today'    => $paymentToday,
			],
			'order' => [
				'monthNum' => $thisMonthStart->copy()->format('m'),
				'yearNum'  => $thisYearStart->copy()->format('y'),
				'week'     => $orderThisWeek,
				'weekNum'  => $thisWeekStart->copy()->format('W'),
				'dayNum'   => $now->copy()->format('d'),
				'today'    => $orderToday,
			],
			'revenue' => [
				'year'  => $results['this_year'],
				'month' => $results['this_month'],
				'week'  => $results['this_week'],
				'day'   => $results['today'],
			]
		];
		// });
	}



	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
}
