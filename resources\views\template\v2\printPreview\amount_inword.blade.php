@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$invoice = $payload['invoice'];
	$company = $payload['invoice']->company;

	// Amount in word configuration
	$inwordStyle = $layoutConfig['inwordStyle'] ?? 'default';
	$inwordAlign = $layoutConfig['inwordAlign'] ?? 'text-left';
	$inwordWeight = $layoutConfig['inwordWeight'] ?? 'font-normal';
	$inwordBorder = $layoutConfig['inwordBorder'] ?? false;
	$inwordBg = $layoutConfig['inwordBg'] ?? 'transparent';

	// Use isDarkColor function from parent (printPreview.blade.php)
	$inwordTextColorClass = isDarkColor($inwordBg) ? 'text-white' : 'text-black';
@endphp

{{-- Amount in Words Section --}}
@if($invoice->amount_inword)
	<div class="amount-inword-section {{ $inwordAlign }}">
		<div class="{{ $inwordBorder ? 'border border-gray-300 p-2 rounded' : 'p-2' }} {{ $inwordTextColorClass }}" style="background-color: {{ $inwordBg }}">
			<span class="">
				{{ $company->type == 1 ? 'Amount in words' : 'Terbilang' }}:
			</span>
			<span class=" {{ $inwordWeight }} font-semibold">{{ $invoice->amount_inword }}</span>
		</div>
	</div>
@endif
