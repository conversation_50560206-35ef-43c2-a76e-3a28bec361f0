<?php

namespace App\Filament\Mc\Resources\McCustomerResource\Pages;

use App\Filament\Mc\Resources\McCustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListMcCustomers extends ListRecords
{
    protected static string $resource = McCustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('New Customer')->modalHeading('New Customer'),
        ];
    }
	public function getHeading(): string
	{
        return 'MC Customers';
	}
	public function getSubheading(): string|Htmlable|null
	{
		// return new HtmlString('<span class="italic text-sm text-gray-500">Agents of all mc customers.</span>');
		return null;
	}
}
