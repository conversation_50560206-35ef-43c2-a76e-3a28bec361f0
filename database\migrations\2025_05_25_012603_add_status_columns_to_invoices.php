<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->enum('verification_status', ['0', '1', '2'])->nullable()->before('verified_by');
            $table->enum('supervision_status', ['0', '1', '2'])->nullable()->before('supervised_by');
            $table->enum('approval_status', ['0', '1', '2'])->nullable()->before('approved_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            //
        });
    }
};
