<!DOCTYPE html>
<html lang="en" class="root-text-sm">

<head>
	@include('invoice.metatitle')
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet"
		integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="{{ $payload['invoice']->company->font->source ?? 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap' }}" rel="stylesheet">

	@include('invoice.css')
</head>

<body style="font-family: {{$payload['invoice']->company->font->name ?? ''}} !important">
	<div class="main small">
		<div class="">
			<div class="row justify-content-between align-items-center">
				<!-- Left Grid: Logo & Text -->
                <div class="col-4 d-flex align-items-start">
                    @if($payload['invoice']->company->logo)
                        <img src="{{ url('storage/' . $payload['invoice']->company->logo) }}" class="mx-2 mt-1 profile-image ml-auto" style="width: 100px; height:auto">
                    @endif
                </div>
				<span class="h2 fw-bold col-4">INVOICE</span>
                <span class="col-4 text-xs">
                    <div>
                        <span class="d-flex justify-content-between fw-bold">
                            <span class="col-5">Invoice No.:</span>
                            <span class="col-7">{{$payload['invoice']->invoice_no}}</span>
                        </span>
                        <span class="d-flex justify-content-between">
                            <span class="col-5">Invoice DATE:</span>
                            <span class="col-7">{{ date('d/m/Y', strtotime($payload['invoice']->invoice_date)) }}</span>
                        </span>
                        <span class="d-flex justify-content-between">
                            <span class="col-5">Due Date Invoice:</span>
                            <span class="col-7">{{ date('d/m/Y', strtotime($payload['invoice']->due_date)) }}</span>
                        </span>
                    </div>
                </span>
			</div>
			<hr class="mt-0">
		</div>

		<div class="px-3">
			<div class="row mb-2">
				<div class="d-flex justify-content-between align-items-start mb-3">
					<div class="col-7">
						<span>Bill from:</span>
						<div>
							<span class="fw-bold">
								{{$payload['invoice']->company->name}}
							</span>
							<p>{!!$payload['invoice']->company->address!!}</p>
						</div>
					</div>
					<div class="col-5">
						<span>Bill to:</span>
						<div>
							<span class="fw-bold">
								{{$payload['invoice']->client->name}}
							</span>
							<p>{!!$payload['invoice']->client->address!!}</p>
						</div>
					</div>
				</div>
				<div class="col-12">
					{{-- <span class="fw-bold mb-2">Customer Order</span> --}}
					<table class="table table-bordered table-sm">
						<thead class="table-dark"
								@if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
									style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
								@endif>
							<tr>
								<th class="text-center" style="border: 1px solid #ddd">Id</th>
								<th class="text-center" style="border: 1px solid #ddd">Description</th>
								<th class="text-end" style="border: 1px solid #ddd" width="15%">Qty</th>
								<th class="text-end" style="border: 1px solid #ddd" width="15%">Price</th>
								<th class="text-end" style="border: 1px solid #ddd" width="20%">Total</th>
							</tr>
						</thead>
						<tbody>
							@foreach ($payload['invoice']->invoiceDetails as $items)
								<tr>
                                    <td></td>
									<td class="vertical-middle">{!!$items->description!!}</td>
									<td class="text-end vertical-middle">{{number_format($items->quantity, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle">{{number_format($items->price, 2, '.', ',')}}</td>
									<td class="text-end vertical-middle">{{$payload['invoice']->currency->symbol}} {{number_format($items->sub_total, 2, '.', ',')}}</td>
								</tr>
							@endforeach
						</tbody>
						<tfoot
							@if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
								style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
							@endif>
							<tr class="">
								<td class="text-end" colspan="4">
									Booking Fee:
								</td>
								<td class="text-end fw-bold">
									<span>{{$payload['invoice']->currency->symbol}} {{number_format($payload['invoice']->booking_fee, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-end" colspan="4">
									Sub Total:
								</td>
								<td class="text-end fw-bold">
									<span>{{$payload['invoice']->currency->symbol}} {{number_format($payload['invoice']->inv_sub_total, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-end" colspan="4">
									Rates:
								</td>
								<td class="text-end fw-bold">
									<span>Rp {{number_format($payload['invoice']->rates, 2, '.', ',')}}</span>
								</td>
							</tr>
							<tr class="">
								<td class="text-end" colspan="4">
									Total (Sub Total x Rates):
								</td>
								<td class="text-end fw-bold">
									<span>Rp {{number_format($payload['invoice']->invoice_amount, 2, '.', ',')}}</span>
								</td>
							</tr>
						</tfoot>
					</table>
				</div>
			</div>
			{{ optional($payload['invoice']->company)->type === '1' ? 'Inword' : 'Terbilang' }}: <span class="fw-semibold">{{$payload['invoice']->amount_inword}}</span>
			<hr>
			<div class="row-mb-5 col-12">
				{{-- <div class="mb-3" style="font-style: italic">
					<span class="text-start">
						Ensure payment is made within 14 calendar days from the invoice date.
					</span>
				</div> --}}
				<div class="row d-flex justify-content-between align-items-start">
					<div class="col-7">
						@php
						    // Define bank fields with their labels and special handling
						    $bankFields = [
						        'bank_acc_name' => 'Account Name',
						        'bank_acc_no' => 'Account Number',
						        'bank_acc_address' => 'Account Address',
						        'bank_name' => 'Bank Name',
						        'bank_address' => 'Bank Address',
						        'bank_code' => 'Bank Code',
						        'swift' => 'Swift Code',
						        'swift_correspondent' => 'Swift Correspondent',
						        'routing_no' => 'Routing No',
						        'transit' => 'Transit',
						        'tt_charge' => 'TT Charge',
						        'iban' => 'IBAN',
						        'bsb' => 'BSB',
						        'branch_code' => 'Branch Code',
						        'branch_bank' => 'Branch Bank',
						        'institution' => 'Institution Code',
						        'sort_code' => 'Sort Code',
						        'ABA' => 'ABA',
						        'IFSC' => 'IFSC Code',
						        'bank_correspondent' => 'Bank Correspondent',
						    ];

						    // Fields that need text wrapping
						    $wrapFields = ['bank_acc_address', 'bank_address'];

						    // Fields that need strip_tags
						    $stripTagsFields = ['bank_acc_address', 'bank_address', 'swift'];
						@endphp

						{{-- Multi Bank Support --}}
						@if(isset($payload['banks']) && $payload['banks']->isNotEmpty())
						    <span class="fw-bold" style="font-size: 0.755rem">Please make payment to:</span>
						    @foreach ($payload['banks'] as $index => $bank)
						        <ul class="list-unstyled row {{ $index > 0 ? 'mt-4 pt-3 border-top' : '' }}" style="font-size: 0.755rem">
						            @foreach($bankFields as $field => $label)
						                @if(isset($bank[$field]))
						                    <li class="d-flex justify-content-start {{ $field === 'swift' ? 'mt-3' : '' }} {{ $field === 'swift_correspondent' ? 'mb-3' : '' }}">
						                        <span class="col-4">{{ $label }}:</span>
						                        <span class="{{ in_array($field, $wrapFields) ? 'col-6 text-wrap' : 'col-7' }}">
						                            @if(in_array($field, $stripTagsFields))
						                                {!! strip_tags($bank[$field], '<p><br><strong><b><em><i><u><ul><ol><li>') !!}
						                            @else
						                                {{ $bank[$field] }}
						                            @endif
						                        </span>
						                    </li>
						                @endif
						            @endforeach

						            {{-- Custom Columns Support --}}
						            @if(isset($bank['custom_columns']) && is_array($bank['custom_columns']))
						                @foreach($bank['custom_columns'] as $label => $field)
						                    @if(is_array($field) && isset($field['value']) && !empty($field['value']))
						                        <li class="d-flex justify-content-start">
						                            <span class="col-4">{{ ucwords(str_replace('_', ' ', $label)) }}:</span>
						                            <span class="col-7">
						                                @if(isset($field['type']) && $field['type'] === 'richtext')
						                                    {!! strip_tags($field['value'], '<p><br><strong><b><em><i><u><ul><ol><li>') !!}
						                                @else
						                                    {{ $field['value'] }}
						                                @endif
						                            </span>
						                        </li>
						                    @endif
						                @endforeach
						            @endif
						        </ul>
						    @endforeach
						@endif

					</div>
					<div class="col-5 text-center">
						@if($payload['invoice']->company->signature)
							<img src="{{ url('storage/' . $payload['invoice']->company->signature) }}" class="mx-2 mt-1 profile-image ml-auto" style="max-height:5rem"><br>
						@endif
						@if ($payload['invoice']->company->signature_name)
							<u>{{$payload['invoice']->company->signature_name}}</u>
						@endif
					</div>
				</div>
				@if ($payload['invoice']->remarks)
					<div class="py-1 px-2"  style="background-color: #f3f3f3 !important">
						<span class="fw-bold d-block">Terms and Conditions</span>
						<p class="m-0 text-sm"><em>{!!$payload['invoice']->remarks!!}</em></p>
					</div>
				@endif
			</div>
			<hr>
		</div>

		{{-- <div class="container mt-5">
			<div style="margin: 0 auto; text-align: center;">
				<div class="col-12" style="font-family: \'Courier New\', monospace; font-size: 11px;">
					<span class="text-center" style="font-size: 10px;">
						This is a digitally generated invoice, no authorization signature is required
					</span>
				</div>
			</div>
		</div> --}}
	</div>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"
		integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous">
	</script>
<script>
</script>
</body>

</html>
