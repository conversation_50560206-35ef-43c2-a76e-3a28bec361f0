<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\TeamInvitation;
use App\Models\User;

class TeamInvitationPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any TeamInvitation');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TeamInvitation $teaminvitation): bool
    {
        return $user->checkPermissionTo('view TeamInvitation');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create TeamInvitation');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TeamInvitation $teaminvitation): bool
    {
        return $user->checkPermissionTo('update TeamInvitation');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TeamInvitation $teaminvitation): bool
    {
        return $user->checkPermissionTo('delete TeamInvitation');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any TeamInvitation');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TeamInvitation $teaminvitation): bool
    {
        return $user->checkPermissionTo('restore TeamInvitation');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any TeamInvitation');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, TeamInvitation $teaminvitation): bool
    {
        return $user->checkPermissionTo('replicate TeamInvitation');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder TeamInvitation');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TeamInvitation $teaminvitation): bool
    {
        return $user->checkPermissionTo('force-delete TeamInvitation');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any TeamInvitation');
    }
}
