<?php

namespace App\Filament\Mc\Resources\McDepositResource\Pages;

use App\Filament\Mc\Resources\McDepositResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\Alignment;

class ListMcDeposits extends ListRecords
{
    protected static string $resource = McDepositResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	public function getHeading(): string
	{
		return 'Customer Transaction Histories';
	}
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
				->label('New Deposit')
				->modalHeading('New Deposit')
				->modalFooterActionsAlignment(Alignment::Right),
        ];
    }
}
