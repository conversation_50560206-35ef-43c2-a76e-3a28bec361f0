<?php

namespace App\Filament\Admin\Resources\CompanyDepositoResource\Pages;

use App\Filament\Admin\Resources\CompanyDepositoResource;
use App\Filament\Admin\Widgets\CompanyDepositSummaryWidget;
use Filament\Actions;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListCompanyDepositos extends ListRecords
{
    protected static string $resource = CompanyDepositoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->icon('heroicon-o-plus')
				->label('New Transaction')
				->modalHeading('New Internal Transaction'),
        ];
    }

	public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'Internal Bank Transactions';

    public function getHeading(): string
	{
        return 'Internal Bank Transactions';
	}

	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-md">Deposit and withdrawal transactions for internal company use.</span>');
    }

    protected function getHeaderWidgets(): array
    {
        return [
            CompanyDepositSummaryWidget::class,
        ];
    }
}
