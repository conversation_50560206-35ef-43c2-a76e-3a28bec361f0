@php
    $columns = collect([
        ['key' => 'item', 'label' => 'Item'],
        ['key' => 'description', 'label' => 'Description'],
        ['key' => 'qty', 'label' => 'Qty'],
        ['key' => 'price', 'label' => 'Price'],
        ['key' => 'total', 'label' => 'Total'], // <PERSON>an semb<PERSON>
    ])->map(function ($col) use ($visibleColumns) {
        $col['show'] = in_array($col['key'], $visibleColumns) || $col['key'] === 'total';
        return $col;
    });

    $visibleColumnsFiltered = $columns->filter(fn($c) => $c['show'])->values();
    $visibleCount = $visibleColumnsFiltered->count();
    $lastVisibleIndex = $visibleCount - 1;
    $colspan = $visibleCount - 1;

    $rows = [
        ['item' => 'A1', 'description' => 'Product A1', 'qty' => 2, 'price' => 50000, 'total' => 100000],
        ['item' => 'B1', 'description' => 'Product B1', 'qty' => 1, 'price' => 70000, 'total' => 70000],
    ];

    $summaries = [
        'Booking Fee' => 'Rp 100.000',
        'Subtotal' => 'Rp 170.000',
        'Rate (10%)' => 'Rp 17.000',
        'Total' => 'Rp 287.000',
    ];

    function getColumnWidthStyle($key) {
        return match ($key) {
            'description' => 'width: 40%; text-align: left;',
            'total' => 'width: 20%; text-align: right;',
            'price' => 'width: 15%; text-align: right;',
            'qty' => 'width: 10%; text-align: right;',
            default => 'width: 15%; text-align: left;',
        };
    }

    function isDarkColor($hexColor) {
        $hexColor = ltrim($hexColor, '#');
        $r = hexdec(substr($hexColor, 0, 2));
        $g = hexdec(substr($hexColor, 2, 2));
        $b = hexdec(substr($hexColor, 4, 2));
        $brightness = ($r * 299 + $g * 587 + $b * 114) / 1000;
        return $brightness < 128;
    }

    function getBorderClasses($style, $isTh = false) {
        return match ($style) {
            'none' => '',
            'row' => $isTh ? 'border-b border-gray-300' : 'border-b border-gray-300',
            'column' => 'border-s border-gray-300',
            'full' => 'border border-gray-300',
            default => '',
        };
    }

    $headerTextColorClass = isDarkColor($headerShading) ? 'text-white' : 'text-black';
@endphp

<table class="w-full table-auto text-sm">
    <thead>
        <tr style="background-color: {{ $headerShading }}">
            @foreach ($visibleColumnsFiltered as $col)
                <th class="px-2 py-1 {{ getBorderClasses($bodyBorderStyle, true) }} {{ $headerTextColorClass }}"
                    style="{{ getColumnWidthStyle($col['key']) }}">
                    {{ $col['label'] }}
                </th>
            @endforeach
        </tr>
    </thead>

    <tbody>
        @foreach ($rows as $rowIndex => $row)
            @php
                $rowStyle = '';
                if ($rowShading === 'zebra' && $rowIndex % 2 === 1) {
                    $rowStyle = "background-color: {$rowColor};";
                }
            @endphp

            <tr style="{{ $rowStyle }}">
                @foreach ($visibleColumnsFiltered as $colIndex => $col)
                    @php
                        $cellStyle = getColumnWidthStyle($col['key']);
                        $cellClass = getBorderClasses($bodyBorderStyle);

                        // Column shading (zebra)
                        if ($columnShading === 'zebra' && $colIndex % 2 === 1) {
                            $cellClass .= ' bg-gray-100';
                        }

                        // Last column shading
                        if ($columnShading === 'last' && $colIndex === $lastVisibleIndex) {
                            $cellStyle .= "background-color: {$lastColumnColor};";
                            $cellClass .= isDarkColor($lastColumnColor) ? ' text-white' : ' text-black';
                        }
                    @endphp

                    <td class="px-2 py-1 {{ $cellClass }}" style="{{ $cellStyle }}">
                        {{ $row[$col['key']] }}
                    </td>
                @endforeach
            </tr>
        @endforeach
    </tbody>

    <tfoot>
        @foreach ($summaries as $label => $value)
            <tr>
                <td colspan="{{ $colspan }}" class="text-right font-semibold px-2 py-1 {{ getBorderClasses($footerBorderStyle) }}">
                    {{ $label }}
                </td>
                <td class="text-right font-semibold px-2 py-1 {{ getBorderClasses($footerBorderStyle) }}"
                    style="background-color: {{ $columnShading === 'last' ? $lastColumnColor : 'transparent' }}; {{ isDarkColor($lastColumnColor) ? 'color:white;' : 'color:black;' }}">
                    {{ $value }}
                </td>
            </tr>
        @endforeach
    </tfoot>
</table>
