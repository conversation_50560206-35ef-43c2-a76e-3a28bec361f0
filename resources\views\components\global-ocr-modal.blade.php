<div x-cloak x-data="globalOcrModal()" x-init="init()" @open-ocr-modal.window="openModal()"
	@close-modal.window="close()"
    class="global-ocr-modal">
    <div x-show="open" x-transition class="fixed inset-0 z-50 flex items-center justify-center p-4" style="display: none;">
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" @click="close()"></div>

        <div x-show="open"
            x-transition
            @keydown.escape.window="close()"
            class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-4 w-full max-w-3xl mx-auto z-60 overflow-y-auto max-h-[90vh]"
        >
            {{-- Livewire ocr Create Form --}}
            @livewire(\App\Filament\Admin\Pages\Ocr::class)
        </div>
    </div>
</div>
<script>
    function globalOcrModal() {
        return {
            open: false,
            init() {
                // Shortcut: Ctrl+Shift+O
                document.addEventListener('keydown', (e) => {
                    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
                    const metaKey = isMac ? e.metaKey : e.ctrlKey;

					//alfabet
                    if (metaKey && e.altKey && e.key.toLowerCase() === ',') {
                        e.preventDefault();
                        this.openModal();
                    }

					console.log('key:', e.key, 'code:', e.code);
                });

                // From menu click
                document.addEventListener('click', (e) => {
                    if (e.target.closest('a[href="#ocr-modal"]')) {
                        e.preventDefault();
                        this.openModal();
                    }
                });
            },
            openModal() {
                this.open = true;
            },
            close() {
                this.open = false;
            }
        }
    }
</script>
