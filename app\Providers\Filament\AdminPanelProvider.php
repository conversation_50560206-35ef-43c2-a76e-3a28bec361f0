<?php

namespace App\Providers\Filament;

use Althinect\FilamentSpatieRolesPermissions\FilamentSpatieRolesPermissionsPlugin;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Search\GoogleSearchProvider;
use Filament\Navigation\MenuItem;
use Filament\Pages\Auth\EditProfile;

class AdminPanelProvider extends PanelProvider
{

    public function panel(Panel  $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->breadcrumbs(false)
            ->sidebarCollapsibleOnDesktop()
			->globalSearch(GoogleSearchProvider::class)
			->topNavigation()
			->favicon(asset('assets/logo/butterfly.png'))
			->brandLogo(asset('assets/logo/logolight.png'))
			->darkModeBrandLogo(asset('assets/logo/logodark.png'))
			->profile(EditProfile::class)
            ->colors([
				'gray' => Color::Gray,
				'primary' => Color::Indigo,
				'info' => Color::Blue,
				'success' => Color::Emerald,
				'warning' => Color::Orange,
				'danger' => Color::Rose,
				'critical' => 'rgb(185, 28, 28)',
			])
            ->viteTheme('resources/css/theme.css')
            ->discoverResources(in: app_path('Filament/Admin/Resources'), for: 'App\\Filament\\Admin\\Resources')
            ->discoverPages(in: app_path('Filament/Admin/Pages'), for: 'App\\Filament\\Admin\\Pages')
			->resources([
				// \App\Filament\Admin\Resources\OrderResource::class,
				// \App\Filament\Admin\Resources\CompanyResource::class,
			])
            ->pages([
                // Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Admin/Widgets'), for: 'App\\Filament\\Admin\\Widgets')
            ->widgets([
                // Widgets\AccountWidget::class,
                // Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                // FilamentSpatieRolesPermissionsPlugin::make()
            ])
			->userMenuItems([
				'profile' => MenuItem::make()->label('Edit profile'),
				MenuItem::make()
					->label('Calculator (Ctrl+Shift+k)')
					->url('#simple-calculator')
                    ->icon('heroicon-o-calculator')
                    ->sort(0),
				MenuItem::make()
					->label('MC Apps')
					->url('/mc')
                    ->icon('heroicon-o-building-storefront')
                    ->visible(fn () => Auth::check() && Auth::user()->hasRole('Super Admin')),
			])
            ->navigationItems([
                NavigationItem::make('Super Admin Menu')
                    ->url('/management/health')  // URL atau route yang dituju
                    ->icon('heroicon-o-chart-bar')
                    ->sort(1)
                    ->visible(fn () => Auth::check() && Auth::user()->hasRole('Super Admin'))
					->group('System'),
            ]);
    }
}
