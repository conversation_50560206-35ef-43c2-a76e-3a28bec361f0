<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mc_customers', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('agent_id')->nullable();
            $table->string('customer_code');
            $table->string('name');
            $table->string('phone')->nullable();
            $table->string('note')->nullable();
			$table->decimal('total_deposits', 18, 2)->nullable();
			$table->decimal('total_orders', 18, 2)->nullable();
			$table->decimal('balance', 18, 2)->nullable();
            $table->timestamps();
			$table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mc_customers');
    }
};
