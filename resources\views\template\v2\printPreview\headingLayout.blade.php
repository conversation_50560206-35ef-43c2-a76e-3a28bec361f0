@php
	// Extract layout configuration from payload
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$company = $payload['invoice']->company;
	$invoice = $payload['invoice'];

	// Heading configuration with defaults
	$headingStyle = $layoutConfig['headingStyle'] ?? 'grid';
	$headingShowLogo = $layoutConfig['showLogo'] ?? true;
	$headingLogoPosition = $layoutConfig['logoPosition'] ?? 'logo-left';
	$headingVerticalAlign = $layoutConfig['headingVerticalAlign'] ?? 'items-center';
	$headingHorizontalAlign = $layoutConfig['headingHorizontalAlign'] ?? 'text-start';
	$headingSize = $layoutConfig['headingSize'] ?? 'h3';
	$headingWeight = $layoutConfig['headingWeight'] ?? 'font-bold';
	$headingColor = $layoutConfig['headingColor'] ?? '#000000';
	$headingShowAddress = $layoutConfig['showAddress'] ?? true;
	$headingAddressPosition = $layoutConfig['addressPosition'] ?? 'near';

	// Invoice title configuration
	$headingShowInvoiceNumber = $layoutConfig['showInvoiceNumber'] ?? true;
	$headingInvoiceTitlePosition = $layoutConfig['invoiceTitlePosition'] ?? 'right';
	$headingInvoiceTitleOnly = $layoutConfig['titleOnly'] ?? false;
	$headingInvoiceTitleSize = $layoutConfig['invoiceTitleSize'] ?? 'h3';
	$headingInvoiceTitleWeight = $layoutConfig['invoiceTitleWeight'] ?? 'font-bold';
	$headingInvoiceNumberWeight = $layoutConfig['invoiceNumberWeight'] ?? 'font-bold';

	// Spacer configuration
	$headingSingleLineSpacer = $layoutConfig['showSingleLineSpacer'] ?? true;
	$headingDoubleLineSpacer = $layoutConfig['showDoubleLineSpacer'] ?? false;

	// Company data
	$companyName = $company->name;
	$companyLogo = $company->logo;
	$companyAddress = $company->address;
	$companyPhone = $company->phone;
	$companyEmail = $company->email;

	// CSS classes mapping
	$sizeCompanyClasses = [
		'h1' => 'text-4xl',
		'h2' => 'text-3xl',
		'h3' => 'text-2xl',
		'h4' => 'text-xl',
		'h5' => 'text-lg',
		'h6' => 'text-base',
	];

	$sizeInvoiceClasses = [
		'h3' => 'text-2xl',
		'h4' => 'text-xl',
		'h5' => 'text-lg',
		'h6' => 'text-base',
	];

	$horizontalAlign = [
		'text-start' => 'text-left',
		'text-center' => 'text-center',
		'text-end' => 'text-right',
	];

	$verticalAlign = [
		'items-start' => 'items-start',
		'items-center' => 'items-center',
		'items-end' => 'items-end',
	];

	// Determine flex order based on logo position
	$flexOrder = $headingLogoPosition === 'logo-right' ? ['company-info', 'logo'] : ['logo', 'company-info'];
@endphp

{{-- Letterhead Section --}}
<div class="letterhead-section">
	@if ($headingStyle === 'grid')
		<div class="flex flex-row {{ $verticalAlign[$headingVerticalAlign] ?? 'items-center' }} space-x-4 gap-2">
			@foreach($flexOrder as $item)
				@if($item === 'logo' && $headingShowLogo && $companyLogo)
					<div class="flex-shrink-0">
						<img src="{{ asset('storage/' . $companyLogo) }}" alt="Company Logo" class="w-24 h-24 object-contain">
					</div>
				@elseif($item === 'company-info')
					<div class="{{ $horizontalAlign[$headingHorizontalAlign] ?? 'text-left' }} flex-grow">
						<div class="{{ $sizeCompanyClasses[$headingSize] ?? 'text-2xl' }} {{ $headingWeight }}" style="color: {{ $headingColor }};">
							{{ $companyName }}
						</div>

						@if($headingShowAddress)
							@if ($headingAddressPosition !== 'far')
								<div class="text-compact text-gray-700 leading-snug mt-1">
									<div>{!! $companyAddress !!}</div>
									@if($companyPhone || $companyEmail)
										<div class="mt-1">
											@if($companyPhone){{ $companyPhone }}@endif
											@if($companyPhone && $companyEmail) - @endif
											@if($companyEmail){{ $companyEmail }}@endif
										</div>
									@endif
								</div>
							@endif
						@endif
					</div>

					@if ($headingShowAddress && $headingAddressPosition === 'far')
						<div class="text-right text-wrap break-words max-w-md">
							<div class="text-xs-compact text-gray-700 leading-snug space-y-1">
								<div>{!! $companyAddress !!}</div>
								@if($companyPhone)<div>{{ $companyPhone }}</div>@endif
								@if($companyEmail)<div>{{ $companyEmail }}</div>@endif
							</div>
						</div>
					@endif

					@if ($headingShowInvoiceNumber && $headingInvoiceTitlePosition === 'right')
						<div class="text-right">
							<div class="{{ $sizeInvoiceClasses[$headingInvoiceTitleSize] ?? 'text-2xl' }} {{ $headingInvoiceTitleWeight }} uppercase">
								Invoice
							</div>
							@if (!$headingInvoiceTitleOnly)
								<div class="text-right {{ $headingInvoiceNumberWeight }} mt-1">
									{{ $invoice->invoice_no }}
								</div>
							@endif
						</div>
					@endif
				@endif
			@endforeach
		</div>
	@elseif ($headingStyle === 'stacked')
		<div class="flex flex-col items-center text-center space-y-2">
			@if($headingShowLogo && $companyLogo)
				<img src="{{ asset('storage/' . $companyLogo) }}" alt="Company Logo" class="w-24 h-24 object-contain">
			@endif

			<div class="{{ $sizeCompanyClasses[$headingSize] ?? 'text-2xl' }} {{ $headingWeight }}" style="color: {{ $headingColor }};">
				{{ $companyName }}
			</div>

			@if($headingShowAddress)
				<div class="text-compact text-gray-700 leading-snug">
					<div>{!! $companyAddress !!}</div>
					@if($companyPhone || $companyEmail)
						<div class="mt-1">
							@if($companyPhone){{ $companyPhone }}@endif
							@if($companyPhone && $companyEmail) - @endif
							@if($companyEmail){{ $companyEmail }}@endif
						</div>
					@endif
				</div>
			@endif
		</div>
	@endif

	{{-- Line Spacers --}}
	@if ($headingSingleLineSpacer)
		<div class="mt-2 mb-3">
			<div class="w-full {{ $headingDoubleLineSpacer ? 'border-t-2' : 'border-t' }} border-gray-600"></div>
			@if ($headingDoubleLineSpacer)
				<div class="w-full border-t border-gray-600 mt-1"></div>
			@endif
		</div>
	@endif

	{{-- Invoice Title at Top/Center --}}
	@if ($headingShowInvoiceNumber && $headingInvoiceTitlePosition === 'first')
		<div class="text-center mt-3">
			<div class="underline {{ $sizeInvoiceClasses[$headingInvoiceTitleSize] ?? 'text-2xl' }} {{ $headingInvoiceTitleWeight }} uppercase">
				Invoice
			</div>
			@if (!$headingInvoiceTitleOnly)
				<div class="{{ $headingInvoiceNumberWeight }} mt-1">
					{{ $invoice->invoice_no }}
				</div>
			@endif
		</div>
	@endif
</div>
