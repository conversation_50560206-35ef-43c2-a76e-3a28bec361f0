import preset from "./vendor/filament/filament/tailwind.config.preset.js"; // pastikan ada .js dan path benar
const defaultTheme = require('tailwindcss/defaultTheme');


/** @type {import('tailwindcss').Config} */
export default {
	presets: [preset],

	content: [
		"./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
		"./vendor/laravel/jetstream/**/*.blade.php",
		"./vendor/filament/**/*.blade.php",
		"./storage/framework/views/*.php",
		"./resources/views/**/*.blade.php",
		"./resources/css/**/*.css",
		"./node_modules/flowbite/**/*.js",
		"./vendor/awcodes/filament-table-repeater/resources/**/*.blade.php",
	],

	theme: {
		extend: {
			colors: {
				secondary: {
					50: "rgba(var(--secondary-50), <alpha-value>)",
					100: "rgba(var(--secondary-100), <alpha-value>)",
					200: "rgba(var(--secondary-200), <alpha-value>)",
					300: "rgba(var(--secondary-300), <alpha-value>)",
					400: "rgba(var(--secondary-400), <alpha-value>)",
					500: "rgba(var(--secondary-500), <alpha-value>)",
					600: "rgba(var(--secondary-600), <alpha-value>)",
					700: "rgba(var(--secondary-700), <alpha-value>)",
					800: "rgba(var(--secondary-800), <alpha-value>)",
					900: "rgba(var(--secondary-900), <alpha-value>)",
				},
				primary: {
					50: "rgba(var(--primary-50), <alpha-value>)",
					100: "rgba(var(--primary-100), <alpha-value>)",
					200: "rgba(var(--primary-200), <alpha-value>)",
					300: "rgba(var(--primary-300), <alpha-value>)",
					400: "rgba(var(--primary-400), <alpha-value>)",
					500: "rgba(var(--primary-500), <alpha-value>)",
					600: "rgba(var(--primary-600), <alpha-value>)",
					700: "rgba(var(--primary-700), <alpha-value>)",
					800: "rgba(var(--primary-800), <alpha-value>)",
					900: "rgba(var(--primary-900), <alpha-value>)",
				},
				danger: {
					50: "rgba(var(--danger-50), <alpha-value>)",
					100: "rgba(var(--danger-100), <alpha-value>)",
					200: "rgba(var(--danger-200), <alpha-value>)",
					300: "rgba(var(--danger-300), <alpha-value>)",
					400: "rgba(var(--danger-400), <alpha-value>)",
					500: "rgba(var(--danger-500), <alpha-value>)",
					600: "rgba(var(--danger-600), <alpha-value>)",
					700: "rgba(var(--danger-700), <alpha-value>)",
					800: "rgba(var(--danger-800), <alpha-value>)",
					900: "rgba(var(--danger-900), <alpha-value>)",
				},
			},
			gridTemplateColumns: {
				5: "repeat(5, minmax(0, 1fr))",
				6: "repeat(6, minmax(0, 1fr))",
			},
			gridColumn: {
				"span-3": "span 3 / span 3",
			},
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
		},
	},

	plugins: [
		require("@tailwindcss/forms"),
		require("@tailwindcss/typography"),
		require("flowbite/plugin"),
	],

	darkMode: "class",
};
