<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\McCustomer;
use App\Models\McDeposit;
use App\Models\McOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class McCustomerClosingController extends Controller
{
	/*
		Rekalkulasi saldo customer, hanya menggunakan tabel McDeposit
	*/
	public function recalcMcCustomerBalances()
	{
		DB::beginTransaction();
		try {
			// Ambil semua deposit yang dibutuhkan sekaligus
			$deposits = McDeposit::select('mc_customer_id', 'trx_type', 'amount')
				->get()
				->groupBy('mc_customer_id');

			// Ambil semua customer yang ada di deposit
			$customerIds = $deposits->keys();
			$customers = McCustomer::whereIn('id', $customerIds)->get();

			foreach ($customers as $customer) {
				$customerDeposits = $deposits->get($customer->id, collect());

				$totalOut = $customerDeposits
					->filter(fn($d) => str_contains($d->trx_type, 'out') || str_contains($d->trx_type, 'order'))
					->sum('amount');

				$totalIn = $customerDeposits
					->filter(fn($d) => str_contains($d->trx_type, 'incoming'))
					->sum('amount');

				$balance = $totalIn - $totalOut;

				$customer->update([
					'total_orders'   => $totalOut,
					'total_deposits' => $totalIn,
					'balance'        => $balance,
				]);
			}

			// $zeroBalanceCustomerIds = McCustomer::where('balance', 0)->pluck('id');
			// McDeposit::whereIn('mc_customer_id', $zeroBalanceCustomerIds)->forceDelete();

			DB::commit();

			return response()->json([
				'message' => 'Rekalkulasi saldo customer berhasil.',
			]);
		} catch (\Exception $e) {
			DB::rollBack();

			return response()->json([
				'message' => 'Rekalkulasi saldo customer gagal.',
				'error'   => $e->getMessage(),
			], 500);
		}
	}


	//backup snapshot untuk semua order dari customer
	public function mcCustomerOrderSnapshot()
	{
		$timestamp = now()->format('Ymd_His');
		$backupPath = "backups";
		Storage::makeDirectory($backupPath);

		try {
			Storage::put("{$backupPath}/orders-{$timestamp}.json", json_encode(
				McOrder::withTrashed()->lockForUpdate()->get()->toArray(),
				JSON_PRETTY_PRINT
			));

			DB::commit();

			return response()->json([
				'message'       => 'Customer backup Orders berhasil.',
				'backup_path'   => $backupPath,
			]);
		} catch (\Exception $e) {
			DB::rollBack();

			return response()->json([
				'message' => 'Customer backup Orders gagal.',
				'error'   => $e->getMessage(),
			], 500);
		}
	}

	//backup snapshot untuk semua deposit dari customer
	public function mcCustomerDepositSnapshot()
	{
		// Step 0: Buat cadangan seluruh data (full backup per tabel)
		$timestamp = now()->format('Ymd_His');
		$backupPath = "backups";
		Storage::makeDirectory($backupPath);


		// Step 1: Mulai transaksi utama
		DB::beginTransaction();

		try {

			Storage::put("{$backupPath}/deposits-{$timestamp}.json", json_encode(
				McDeposit::withTrashed()->lockForUpdate()->get()->toArray(),
				JSON_PRETTY_PRINT
			));
			DB::commit();

			return response()->json([
				'message'       => 'Customer deposit berhasil.',
				'backup_path'   => $backupPath,
			]);
		} catch (\Exception $e) {
			DB::rollBack();

			return response()->json([
				'message' => 'Customer deposit gagal.',
				'error'   => $e->getMessage(),
			], 500);
		}
	}

	//starter, run once
	public function mcCustomerClosingStarter()
	{
		// Step 0: Buat cadangan seluruh data (full backup per tabel)
		$timestamp = now()->format('Ymd_His');
		$backupPath = "backups/{$timestamp}";
		Storage::makeDirectory($backupPath);

		Storage::put("{$backupPath}/orders.json", json_encode(
			McOrder::withTrashed()->get()->toArray(),
			JSON_PRETTY_PRINT
		));

		Storage::put("{$backupPath}/deposits.json", json_encode(
			McDeposit::withTrashed()->get()->toArray(),
			JSON_PRETTY_PRINT
		));

		// Step 1: Mulai transaksi utama
		DB::beginTransaction();

		try {
			// Step 2: Ambil summary data sebagai snapshot (dengan lock)
			$orderSummaries = McOrder::select('mc_customer_id', 'bank_id', DB::raw('SUM(total_order) as total_order'))
				->whereNotNull('bank_id')
				->groupBy('mc_customer_id', 'bank_id')
				->lockForUpdate()
				->get();

			$depositSummaries = McDeposit::select('mc_customer_id', 'bank_id', 'trx_type', DB::raw('SUM(amount) as total_deposit'))
				// ->whereNotNull('bank_id')
				->groupBy('mc_customer_id', 'bank_id', 'trx_type')
				->lockForUpdate()
				->get();

			// Step 3: Simpan snapshot summary (ringkasan untuk proses insert ulang)
			$snapshotData = [
				'timestamp' => now()->toDateTimeString(),
				'orders'    => $orderSummaries->toArray(),
				'deposits'  => $depositSummaries->toArray(),
			];

			$snapshotFile = 'snapshots/closing-' . $timestamp . '-' . Str::uuid() . '.json';
			Storage::put($snapshotFile, json_encode($snapshotData, JSON_PRETTY_PRINT));

			// Step 4: Force delete seluruh order & deposit
			McOrder::query()->forceDelete();
			McDeposit::query()->forceDelete();

			// Step 5: Load ulang snapshot dari file
			$snapshot = json_decode(Storage::get($snapshotFile), true);

			// Step 6: Insert ulang deposit summary (trx_type = 'incoming')
			foreach ($snapshot['deposits'] as $deposit) {
				McDeposit::create([
					'mc_customer_id' => $deposit['mc_customer_id'],
					'bank_id'        => $deposit['bank_id'],
					'amount'         => $deposit['total_deposit'],
					'trx_type'       => $deposit['trx_type'],
					'slip_date'      => now(),
				]);
			}

			// Step 7: Insert ulang order summary (memicu deposit otomatis via event)
			foreach ($snapshot['orders'] as $order) {
				McOrder::create([
					'mc_customer_id' => $order['mc_customer_id'],
					'bank_id'        => $order['bank_id'],
					'total_order'    => $order['total_order'],
					'order_date'     => now(),
					'amount'         => null,
					'buy_rates'      => null,
					'sell_rates'     => null,
				]);
			}

			DB::commit();

			return response()->json([
				'message'       => 'Customer closing berhasil.',
				'snapshot_file' => $snapshotFile,
				'backup_path'   => $backupPath,
			]);
		} catch (\Exception $e) {
			DB::rollBack();

			return response()->json([
				'message' => 'Customer closing gagal.',
				'error'   => $e->getMessage(),
			], 500);
		}
	}


	//affect none starter or production
	public function mcCustomerClosingOrder()
	{
		// Step 0: Buat cadangan seluruh data (full backup per tabel)
		$timestamp = now()->format('Ymd_His');

		// Step 1: Mulai transaksi utama
		DB::beginTransaction();

		try {
			// Step 2: Ambil summary data sebagai snapshot (dengan lock)
			$orderSummaries = McOrder::select('mc_customer_id', 'bank_id', DB::raw('SUM(total_order) as total_order'))
				->whereNotNull('bank_id')
				->groupBy('mc_customer_id', 'bank_id')
				->lockForUpdate()
				->get();

			// Step 3: Simpan snapshot summary (ringkasan untuk proses insert ulang)
			$snapshotData = [
				'timestamp' => now()->toDateTimeString(),
				'orders'    => $orderSummaries->toArray(),
			];

			$snapshotFile = 'snapshots/closing-' . $timestamp . '-' . Str::uuid() . '.json';
			Storage::put($snapshotFile, json_encode($snapshotData, JSON_PRETTY_PRINT));

			// Step 4: Force delete seluruh order & deposit
			McOrder::query()->forceDelete();
			$snapshot = json_decode(Storage::get($snapshotFile), true);

			// Step 7: Insert ulang order summary (memicu deposit otomatis via event)
			foreach ($snapshot['orders'] as $order) {
				McOrder::create([
					'mc_customer_id' => $order['mc_customer_id'],
					'bank_id'        => $order['bank_id'],
					'total_order'    => $order['total_order'],
					'order_date'     => now(),
					'amount'         => null,
					'buy_rates'      => null,
					'sell_rates'     => null,
				]);
			}
			DB::commit();

			return response()->json([
				'message'       => 'Customer closing berhasil.',
				'snapshot_file' => $snapshotFile,
			]);
		} catch (\Exception $e) {
			DB::rollBack();

			return response()->json([
				'message' => 'Customer closing gagal.',
				'error'   => $e->getMessage(),
			], 500);
		}
	}

	//production
	public function mcCustomerClosingDeposit()
	{
		// Step 0: Buat cadangan seluruh data (full backup per tabel)
		$timestamp = now()->format('Ymd_His');

		// Step 1: Mulai transaksi utama
		DB::beginTransaction();

		try {

			$depositSummaries = McDeposit::select('mc_customer_id', 'bank_id', 'trx_type', DB::raw('SUM(amount) as total_deposit'))
				// ->whereNotNull('bank_id')
				->groupBy('mc_customer_id', 'bank_id', 'trx_type')
				->lockForUpdate()
				->get();

			// Step 3: Simpan snapshot summary (ringkasan untuk proses insert ulang)
			$snapshotData = [
				'timestamp' => now()->toDateTimeString(),
				'deposits'  => $depositSummaries->toArray(),
			];

			$snapshotFile = 'snapshots/closing-' . $timestamp . '-' . Str::uuid() . '.json';
			Storage::put($snapshotFile, json_encode($snapshotData, JSON_PRETTY_PRINT));

			McDeposit::query()->forceDelete();

			// Step 5: Load ulang snapshot dari file
			$snapshot = json_decode(Storage::get($snapshotFile), true);

			// Step 6: Insert ulang deposit summary (trx_type = 'incoming')
			foreach ($snapshot['deposits'] as $deposit) {
				McDeposit::create([
					'mc_customer_id' => $deposit['mc_customer_id'],
					'bank_id'        => $deposit['bank_id'],
					'amount'         => $deposit['total_deposit'],
					'trx_type'       => $deposit['trx_type'],
					'slip_date'      => now(),
				]);
			}

			DB::commit();

			return response()->json([
				'message'       => 'Customer closing berhasil.',
				'snapshot_file' => $snapshotFile,
			]);
		} catch (\Exception $e) {
			DB::rollBack();

			return response()->json([
				'message' => 'Customer closing gagal.',
				'error'   => $e->getMessage(),
			], 500);
		}
	}

	public function restoreDepositBackup(string $timestamp)
	{
		$path = "backups/{$timestamp}/deposits.json";

		if (!Storage::exists($path)) {
			return response()->json([
				'message' => 'Backup file tidak ditemukan.',
				'path' => $path,
			], 404);
		}

		DB::beginTransaction();

		try {
			// Optional: hapus semua data dulu
			McDeposit::query()->forceDelete();

			// Ambil data dari file JSON
			$data = json_decode(Storage::get($path), true);

			foreach ($data as $item) {
				unset($item['id']); // Hindari konflik ID
				unset($item['deleted_at']); // Hindari timestamp soft-delete

				// Buat ulang record
				McDeposit::create($item);
			}

			DB::commit();

			return response()->json([
				'message' => 'Restore data deposit berhasil.',
				'restored' => count($data),
				'source' => $path,
			]);
		} catch (\Exception $e) {
			DB::rollBack();

			return response()->json([
				'message' => 'Restore data deposit gagal.',
				'error' => $e->getMessage(),
			], 500);
		}
	}
}
