<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class McBank extends Model
{
	use HasFactory, SoftDeletes;

    protected $table = 'mc_banks';

	protected $fillable = [
		'mc_bank_group_id',
		'name',
		'account_no',
		'account_name',
	];

	public function bankGroup(): BelongsTo
	{
		return $this->belongsTo(McBank::class, 'mc_bank_group_id', 'id');
	}
}
