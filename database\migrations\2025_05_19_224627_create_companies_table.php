<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->text('address')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('type')->nullable();
            $table->string('business_type')->nullable();

            $table->string('template')->nullable();

            $table->string('templateheading')->nullable();
            $table->string('templatebillto')->nullable();
            $table->string('templatetable')->nullable();
            $table->string('templateinword')->nullable();
            $table->string('templatebankinfo')->nullable();

            $table->string('logo')->nullable();
            $table->string('signature')->nullable();
            $table->string('signature_name')->nullable();
            $table->string('bg_color')->nullable();
            $table->string('font_id')->nullable();
            $table->string('heading_size')->nullable();
            $table->string('text_color')->nullable();
            $table->string('footer_color')->nullable();
            $table->boolean('can_replicate')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
