<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\CompanyDepositSummary;
use App\Models\User;

class CompanyDepositSummaryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any CompanyDepositSummary');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CompanyDepositSummary $companydepositsummary): bool
    {
        return $user->checkPermissionTo('view CompanyDepositSummary');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create CompanyDepositSummary');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CompanyDepositSummary $companydepositsummary): bool
    {
        return $user->checkPermissionTo('update CompanyDepositSummary');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CompanyDepositSummary $companydepositsummary): bool
    {
        return $user->checkPermissionTo('delete CompanyDepositSummary');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any CompanyDepositSummary');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CompanyDepositSummary $companydepositsummary): bool
    {
        return $user->checkPermissionTo('restore CompanyDepositSummary');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any CompanyDepositSummary');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, CompanyDepositSummary $companydepositsummary): bool
    {
        return $user->checkPermissionTo('replicate CompanyDepositSummary');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder CompanyDepositSummary');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CompanyDepositSummary $companydepositsummary): bool
    {
        return $user->checkPermissionTo('force-delete CompanyDepositSummary');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any CompanyDepositSummary');
    }
}
