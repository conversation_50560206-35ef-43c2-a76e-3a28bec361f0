<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDFShift Test Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 50px;
            background-color: #f8f9fa;
            color: #333;
        }
        .error-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid #dc3545;
        }
        .error-title {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .info-box {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #bee5eb;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .back-link:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-title">
            🚫 PDFShift Test Failed
        </div>
        
        <p>Sorry, the PDFShift PDF generation test encountered an error.</p>
        
        <div class="error-message">
            <strong>Error Details:</strong><br>
            {{ $error }}
        </div>
        
        <div class="info-box">
            <strong>Test Information:</strong><br>
            • Record ID: {{ $record }}<br>
            • Test Time: {{ now()->format('Y-m-d H:i:s') }}<br>
            • Service: PDFShift API<br>
            • Content: Hello World Test
        </div>
        
        <p>This was a simple test with "Hello World" content to verify PDFShift API connectivity.</p>
        
        <p><strong>Possible causes:</strong></p>
        <ul>
            <li>Invalid API key</li>
            <li>Network connectivity issues</li>
            <li>PDFShift service temporarily unavailable</li>
            <li>API quota exceeded</li>
        </ul>
        
        <a href="javascript:history.back()" class="back-link">← Go Back</a>
    </div>
</body>
</html>
