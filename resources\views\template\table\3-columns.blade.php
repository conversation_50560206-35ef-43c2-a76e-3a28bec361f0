<table class="table table-bordered table-sm">
    <thead class=""
            @if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
                style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
            @endif>
        <tr>
            <th class="text-center" style="border: 1px solid #ddd">Description</th>
            {{-- <th class="text-center" style="border: 1px solid #ddd" width="10%">Qty</th> --}}
            <th class="text-center" style="border: 1px solid #ddd" width="15%">Price</th>
            <th class="text-center" style="border: 1px solid #ddd" width="20%">Total</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($payload['invoice']->invoiceDetails as $items)
            <tr>
                <td class="vertical-middle ps-3">{!!$items->description!!}</td>
                <td class="text-end vertical-middle">{{number_format($items->price, 2, '.', ',')}}</td>
                <td class="text-end vertical-middle">{{$payload['invoice']->currency->symbol}} {{number_format($items->sub_total, 2, '.', ',')}}</td>
            </tr>
        @endforeach
    </tbody>
    <tfoot
        @if($payload['invoice']->company->bg_color && $payload['invoice']->company->footer_color)
            style="background-color: {{ $payload['invoice']->company->bg_color }}; color: {{ $payload['invoice']->company->footer_color }};"
        @endif>
        <tr class="">
            <td class="text-end" colspan="2">
                TOTAL:
            </td>
            <td class="text-end fw-bold">
                <span>{{$payload['invoice']->currency->symbol}} {{number_format($payload['invoice']->invoice_amount, 2, '.', ',')}}</span>
            </td>
        </tr>
    </tfoot>
</table>
