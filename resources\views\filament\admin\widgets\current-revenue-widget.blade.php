<x-filament-widgets::widget>

	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 w-full gap-4">
			<x-filament::section class="">
				<div class="grid grid-cols-6 gap-x-4 mb-4">
					<div class="col-span-1">
						<span class="w-10 h-10 flex items-center justify-center rounded-full border bg-gray-100 dark:bg-gray-500 text-indigo-400 font-medium">
							{{ $datenumbering['year'] }}
						</span>
					</div>
					<div class="col-span-5 flex flex-col">
						<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200">This Year Revenue</span>
						<span class="text-2xl font-semibold">IDR {{ number_format($revenue['year'],2,'.',',') }}</span>
					</div>
				</div>
				<div class="grid grid-cols-3 gap-x-4 items-center justify-between">
					<span class="text-sm col-span-2 text-gray-400 dark:text-gray-200">Against Previous Year</span>
					<span class="col-span-1">
						@php
							$yearDiff = $difference['year'] ?? 0;
							$badgeColor = $yearDiff > 0 ? 'success' : ($yearDiff < 0 ? 'danger' : 'gray');
							$badgeIcon = $yearDiff > 0 ? 'heroicon-s-arrow-up-circle' : ($yearDiff < 0 ? 'heroicon-s-arrow-down-circle' : 'heroicon-s-minus-circle');
						@endphp

						<x-filament::badge
							size="sm"
							:color="$badgeColor"
							:icon="$badgeIcon"
						>
							{{ $yearDiff > 0 ? '+' : '' }}{{ number_format($yearDiff, 0, '.', ',') }}%
						</x-filament::badge>

					</span>
				</div>
			</x-filament::section>

			<x-filament::section class="">
				<div class="grid grid-cols-6 gap-x-4 mb-4">
					<div class="col-span-1">
						<span class="w-10 h-10 flex items-center justify-center rounded-full border bg-gray-100 dark:bg-gray-500 text-teal-300 font-medium">
							{{ $datenumbering['month'] }}
						</span>
					</div>
					<div class="col-span-5 flex flex-col">
						<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200">This Month Revenue</span>
						<span class="text-2xl font-semibold">IDR {{ number_format($revenue['month'],2,'.',',') }}</span>
					</div>
				</div>
				<div class="grid grid-cols-3 gap-x-4 items-center justify-between">
					<span class="text-sm col-span-2 text-gray-400 dark:text-gray-200">Against Previous Month</span>
					<span class="col-span-1">
						@php
							$yearDiff = $difference['month'] ?? 0;
							$badgeColor = $yearDiff > 0 ? 'success' : ($yearDiff < 0 ? 'danger' : 'gray');
							$badgeIcon = $yearDiff > 0 ? 'heroicon-s-arrow-up-circle' : ($yearDiff < 0 ? 'heroicon-s-arrow-down-circle' : 'heroicon-s-minus-circle');
						@endphp

						<x-filament::badge
							size="sm"
							:color="$badgeColor"
							:icon="$badgeIcon"
						>
							{{ $yearDiff > 0 ? '+' : '' }}{{ number_format($yearDiff, 0, '.', ',') }}%
						</x-filament::badge>
					</span>
				</div>
			</x-filament::section>

			<x-filament::section class="">
				<div class="grid grid-cols-6 gap-x-4 mb-4">
					<div class="col-span-1">
						<span class="w-10 h-10 flex items-center justify-center rounded-full border bg-gray-100 dark:bg-gray-500 text-yellow-500 font-medium">
							{{ $datenumbering['week'] }}
						</span>
					</div>
					<div class="col-span-5 flex flex-col">
						<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200">This Week Revenue</span>
						<span class="text-2xl font-semibold">IDR {{ number_format($revenue['week'],2,'.',',') }}</span>
					</div>
				</div>
				<div class="grid grid-cols-3 gap-x-4 items-center justify-between">
					<span class="text-sm col-span-2 text-gray-400 dark:text-gray-200">Against Previous Week</span>
					<span class="col-span-1">
						@php
							$yearDiff = $difference['week'] ?? 0;
							$badgeColor = $yearDiff > 0 ? 'success' : ($yearDiff < 0 ? 'danger' : 'gray');
							$badgeIcon = $yearDiff > 0 ? 'heroicon-s-arrow-up-circle' : ($yearDiff < 0 ? 'heroicon-s-arrow-down-circle' : 'heroicon-s-minus-circle');
						@endphp

						<x-filament::badge
							size="sm"
							:color="$badgeColor"
							:icon="$badgeIcon"
						>
							{{ $yearDiff > 0 ? '+' : '' }}{{ number_format($yearDiff, 0, '.', ',') }}%
						</x-filament::badge>
					</span>
				</div>
			</x-filament::section>

			<x-filament::section class="">
				<div class="grid grid-cols-6 gap-x-4 mb-4">
					<div class="col-span-1">
						<span class="w-10 h-10 flex items-center justify-center rounded-full border bg-gray-100 dark:bg-gray-500 text-rose-500 font-medium">
							{{ $datenumbering['day'] }}
						</span>
					</div>
					<div class="col-span-5 flex flex-col">
						<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200">Today Revenue</span>
						<span class="text-2xl font-semibold">IDR {{ number_format($revenue['day'],2,'.',',') }}</span>
					</div>
				</div>
				<div class="grid grid-cols-3 gap-x-4 items-center justify-between">
					<span class="text-sm col-span-2 text-gray-400 dark:text-gray-200">Against Yesterday</span>
					<span class="col-span-1">
						@php
							$yearDiff = $difference['day'] ?? 0;
							$badgeColor = $yearDiff > 0 ? 'success' : ($yearDiff < 0 ? 'danger' : 'gray');
							$badgeIcon = $yearDiff > 0 ? 'heroicon-s-arrow-up-circle' : ($yearDiff < 0 ? 'heroicon-s-arrow-down-circle' : 'heroicon-s-minus-circle');
						@endphp

						<x-filament::badge
							size="sm"
							:color="$badgeColor"
							:icon="$badgeIcon"
						>
							{{ $yearDiff > 0 ? '+' : '' }}{{ number_format($yearDiff, 0, '.', ',') }}%
						</x-filament::badge>
					</span>
				</div>
			</x-filament::section>
	</div>
</x-filament-widgets::widget>
{{-- @livewire(\App\Filament\Admin\Widgets\DepositSummaryWidget::class) --}}
