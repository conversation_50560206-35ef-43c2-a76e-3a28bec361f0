<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Order;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class OrderTrendsWidget extends ChartWidget
{
protected function getData(): array
{
    return Cache::remember('order_trends_widget', 120, function () {
        $weeks = [];
        $orderCounts = [];
        $orderValues = [];

        $startDate = now()->subWeeks(3)->startOfWeek();
        $endDate = now()->endOfWeek();

        $orders = Order::withoutGlobalScope('order')
            ->whereBetween('order_date', [$startDate, $endDate])
            ->selectRaw('YEARWEEK(order_date, 1) as year_week')
            ->selectRaw('MIN(order_date) as week_start')
            ->selectRaw('MAX(order_date) as week_end')
            ->selectRaw('COUNT(*) as total_orders')
            ->selectRaw('SUM(total * rates) as total_value')
            ->groupBy('year_week')
            ->orderBy('year_week')
            ->get();

        $period = collect();
        for ($i = 3; $i >= 0; $i--) {
            $period->push([
                'start' => now()->subWeeks($i)->startOfWeek(),
                'end'   => now()->subWeeks($i)->endOfWeek(),
            ]);
        }

        foreach ($period as $p) {
            $weeks[] = $p['start']->format('M d') . ' - ' . $p['end']->format('M d');

            $found = $orders->first(fn ($o) =>
                \Carbon\Carbon::parse($o->week_start)->startOfWeek()->eq($p['start'])
            );

            $orderCounts[] = $found->total_orders ?? 0;
            $orderValues[] = isset($found->total_value)
                ? round($found->total_value / 1_000_000, 2)
                : 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Order Count',
                    'data' => $orderCounts,
                    'type' => 'line',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'yAxisID' => 'y',
                    'tension' => 0.3,
                ],
                [
                    'label' => 'Order Value (M IDR)',
                    'data' => $orderValues,
                    'type' => 'bar',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.6)',
                    'borderColor' => 'rgb(16, 185, 129)',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $weeks,
        ];
    });
}


    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
					'grid' => [
						'color' => 'rgba(0, 0, 0, 0.1)',
					],
                    'title' => [
                        'display' => true,
                        'text' => 'Order Count',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
					'grid' => [
						'color' => 'rgba(0, 0, 0, 0.1)',
					],
                    'title' => [
                        'display' => true,
                        'text' => 'Value (Million IDR)',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
        ];
    }
    // protected static ?string $heading = 'Weekly Trends';
    protected static ?string $pollingInterval = '120s';
    protected int | string | array $columnSpan = 3;

	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
}
