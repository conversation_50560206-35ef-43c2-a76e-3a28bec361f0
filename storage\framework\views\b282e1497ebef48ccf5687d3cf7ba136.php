<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
	<div class="grid grid-cols-3 text-xs gap-4"
		style="font-family: 'Courier New', Courier, monospace !important">
		<?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
			<div class="col-span-1">
				 <?php $__env->slot('heading', null, []); ?> 
					Shortcut key
				 <?php $__env->endSlot(); ?>
				<table class="table-auto w-full">
					<thead>
						<tr>
							<th class="border border-gray-300 px-4 py-3 text-center uppercase font-bold" width="40%">Key</th>
							<th class="border border-gray-300 px-4 py-3 text-center uppercase font-bold">Action</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td class="border border-gray-300 px-4 py-3">
								<div>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Ctrl</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Alt</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">a</span>
								</div>
							</td>
							<td class="border border-gray-300 px-4 py-3">Create New Agent Window</td>
						</tr>
						<tr>
							<td class="border border-gray-300 px-4 py-3">
								<div>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Ctrl</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Alt</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">b</span>
								</div>
							</td>
							<td class="border border-gray-300 px-4 py-3">Create New Business Type</td>
						</tr>
						<tr>
							<td class="border border-gray-300 px-4 py-3">
								<div>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Ctrl</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Alt</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">c</span>
								</div>
							</td>
							<td class="border border-gray-300 px-4 py-3">Create New Company Window</td>
						</tr>
						<tr>
							<td class="border border-gray-300 px-4 py-3">
								<div>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Ctrl</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Alt</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">d</span>
								</div>
							</td>
							<td class="border border-gray-300 px-4 py-3">Create New Deposit</td>
						</tr>
						<tr>
							<td class="border border-gray-300 px-4 py-3">
								<div>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Ctrl</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">Alt</span>
									<span class="border rounded bg-indigo-200  dark:bg-indigo-500 shadow p-1">k</span>
								</div>
							</td>
							<td class="border border-gray-300 px-4 py-3">Simple Calculator Window</td>
						</tr>
						<tr>
							<td class="border border-gray-300 px-4 py-3">
								<div>
									<span class="border rounded bg-indigo-200 dark:bg-indigo-500 shadow p-1">Ctrl</span>
									<span class="border rounded bg-indigo-200 dark:bg-indigo-500 shadow p-1">Alt</span>
									<span class="border rounded bg-indigo-200 dark:bg-indigo-500 shadow p-1">o</span>
								</div>
							</td>
							<td class="border border-gray-300 px-4 py-3">Create New Order Window</td>
						</tr>
						<tr>
							<td class="border border-gray-300 px-4 py-3">
								<div>
									<span class="border rounded bg-indigo-200 dark:bg-indigo-500 shadow p-1">Ctrl</span>
									<span class="border rounded bg-indigo-200 dark:bg-indigo-500 shadow p-1">Alt</span>
									<span class="border rounded bg-indigo-200 dark:bg-indigo-500 shadow p-1">r</span>
								</div>
							</td>
							<td class="border border-gray-300 px-4 py-3">Open Report Window</td>
						</tr>
					</tbody>
				</table>
			</div>
		 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
	</div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH D:\sites\starterkit\web_starter\resources\views/filament/admin/widgets/shortcut-key-widget.blade.php ENDPATH**/ ?>