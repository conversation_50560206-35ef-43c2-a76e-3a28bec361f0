@php
	// Extract layout configuration for CSS setup
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$company = $payload['invoice']->company;

	// Paper and margin configuration
	$paperSize = $layoutConfig['paper_size'] ?? 'legal';
	$marginTop = $layoutConfig['margin_top'] ?? 10;
	$marginLeft = $layoutConfig['margin_left'] ?? 10;
	$marginBottom = $layoutConfig['margin_bottom'] ?? 10;
	$marginRight = $layoutConfig['margin_right'] ?? 10;

	// Font configuration - get from invoicetemplate font_id
	$font = $invoiceTemplate?->font; // This should load the Font model via relationship
	$fontFamily = $font?->name ?? 'Montserrat';
	$fontSource = $font?->source ?? 'https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap';

	// Text Color Helper Function
	if (!function_exists('isDarkColor')) {
		function isDarkColor($hexColor) {
			if (empty($hexColor) || $hexColor === 'transparent') return false;
			$hexColor = ltrim($hexColor, '#');
			if (strlen($hexColor) !== 6) return false;

			$r = hexdec(substr($hexColor, 0, 2));
			$g = hexdec(substr($hexColor, 2, 2));
			$b = hexdec(substr($hexColor, 4, 2));
			$brightness = ($r * 299 + $g * 587 + $b * 114) / 1000;
			return $brightness < 128;
		}
	}

	// Calculate text color classes based on background colors
	$inwordBg = $layoutConfig['inwordBg'] ?? 'transparent';
	$bankInfoBg = $layoutConfig['bankInfoBg'] ?? 'transparent';
	$remarkBg = $layoutConfig['remarkBg'] ?? 'transparent';
	$tableHeaderShading = $layoutConfig['tableHeaderShading'] ?? '#f3f4f6';
	$tableSummaryBg = $layoutConfig['tableSummaryBg'] ?? 'transparent';
	$companyType = $payload['invoice']->company->type;
	$company = $payload['invoice']->company;
	if($companyType == 1) {
		$title = $company->name .' - Invoice '. $payload['invoice']->invoice_no . ' - '.$payload['invoice']->currency->symbol.' '. number_format($payload['invoice']->invoice_amount, 2, '.', ',');
	}else{
		$title = $payload['invoice']->invoice_no;
	}
@endphp

<!DOCTYPE html>
<html lang="en" class="">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<meta name="description" content="Invoice">
		<meta name="author" content="rebrandz.xyz">
		<title>{{$title}}</title>
		{{-- Font Loading --}}
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="{{ $fontSource }}" rel="stylesheet">

		{{-- Tailwind CSS --}}
		@vite('resources/css/app.css')

		@php
			$bodyFontSize = ($font?->name === "Unna") ? 14 : 13;
			$xsFontSize = ($font?->name === "Unna") ? 14 : 12;
			$compactFontSize = ($font?->name === "Unna") ? 14 : 12;
		@endphp
		<style>
			@page {
				size: '{{ $paperSize }}';
				margin: {{ $marginTop ?? 10 }}mm {{ $marginRight ?? 10 }}mm {{ $marginBottom ?? 10 }}mm {{ $marginLeft ?? 10 }}mm;
			}

			body {
				font-family: '{{ $fontFamily }}', sans-serif;
				-webkit-print-color-adjust: exact;
				print-color-adjust: exact;
				font-size: {{ $bodyFontSize }}px;
				line-height: 1.5;
			}

			/* Single page optimization - only for print */
			@media print {
				.invoice-container {
					max-height: calc(100vh - {{ $marginTop + $marginBottom }}mm);
					overflow: visible;
				}
			}

			/* Screen view - allow scrolling */
			@media screen {
				.invoice-container {
					min-height: 100vh;
					overflow: visible;
				}
			}

			/* Compact spacing for single page */
			.letterhead-section {
				margin-bottom: 1rem;
			}

			.section-spacing {
				margin-top: 3rem;
			}

			/* Table optimization */
			.invoice-table {
				font-size: {{ $xsFontSize }}px;
			}

			.invoice-table th,
			.invoice-table td {
				padding: 6px 8px;
				line-height: 1.5;
			}

			/* Compact text sizes */
			.text-compact {
				font-size: {{ $xsFontSize }}px;
				line-height: 1.5;
			}

			.text-xs-compact {
				font-size: {{ $compactFontSize }}px;
				line-height: 1.5;
			}

			@media print {
				body {
					margin: 0;
					padding: 0;
				}

				.no-print {
					display: none !important;
				}

				/* Force single page */
				.invoice-container {
					page-break-inside: avoid;
					break-inside: avoid;
				}

				/* Prevent page breaks in critical sections */
				.letterhead-section,
				.invoice-table,
				.bank-info-section {
					page-break-inside: avoid;
					break-inside: avoid;
				}
			}
		</style>
	</head>
	<body class="bg-white text-gray-900 w-full text-xs" style="font-family: '{{ $fontFamily }}', sans-serif;">
		<div class="invoice-container p-4 pb-20">
			{{--
				"invoicetemplate": {
					"id": 1,
					"company_id": 474,
					"font_id": 11,
					"layout_config": {
						"inwordBg": "#ffffff",
						"remarkBg": null,
						"showLogo": true,
						"titleOnly": false,
						"bankInfoBg": "#e3e3e3",
						"margin_top": "10",
						"paper_size": "legal",
						"showFooter": true,
						"Margin (mm)": null,
						"footerAlign": "justify-center",
						"footerDecor": null,
						"headingSize": "h2",
						"inwordAlign": "justify-center",
						"inwordStyle": "italic",
						"margin_left": "5",
						"remarkAlign": "justify-center",
						"remarkStyle": "italic",
						"showAddress": true,
						"footerBorder": true,
						"headingColor": "#ffcaca",
						"headingStyle": "grid",
						"inwordBorder": true,
						"inwordWeight": "font-medium",
						"logoPosition": "logo-right",
						"margin_right": "5",
						"remarkBorder": false,
						"remarkWeight": "font-normal",
						"bankInfoDecor": "not-italic",
						"billtoContact": "stacked",
						"footerContent": "This is a digitally generated invoice, no authorization signature is required",
						"footerFontFam": "inherit",
						"headingWeight": "font-bold",
						"margin_bottom": "5",
						"tableRowColor": "#ffffff",
						"bankInfoBorder": true,
						"bankInfoLayout": "flex",
						"bankInfoWeight": "font-bold",
						"footerFontSize": "10",
						"signatureStyle": "style1",
						"tableSummaryBg": "#f3f4f6",
						"addressPosition": "below",
						"repeater_layout": [
							{
								"column_size": "2",
								"row_position": "before_table",
								"column_1_content": [
									"bill_to"
								],
								"column_2_content": [
									"invoice_info"
								]
							},
							{
								"column_size": "1",
								"row_position": "after_table",
								"column_1_content": [
									"custom_input"
								]
							},
							{
								"column_size": "2",
								"row_position": "after_table",
								"column_1_content": [
									"invoicesummary",
									"amount_inword"
								],
								"column_2_content": [
									"signature"
								]
							},
							{
								"column_size": "1",
								"row_position": "after_table",
								"column_1_content": [
									"remark"
								]
							}
						],
						"signatureHeight": "10",
						"tableRowShading": "none",
						"tableShowInword": false,
						"customInputAlign": "justify-center",
						"customInputDecor": "not-italic",
						"footerFontWeight": "font-bold",
						"invoiceDateStyle": "j F Y",
						"invoiceHasBorder": true,
						"invoiceTitleSize": null,
						"customInputWeight": "font-normal",
						"footerBorderStyle": "none",
						"invoiceInfoLayout": "flex",
						"invoiceInfoWeight": "font-bold",
						"showInvoiceNumber": false,
						"customContentAlign": null,
						"customContentDecor": null,
						"customContentInput": null,
						"customInputContent": "Please make payment within 7 days of receiving this invoice.",
						"invoiceTitleWeight": "font-bold",
						"tableColumnShading": "none",
						"tableHeaderShading": "#f3f4f6",
						"customContentWeight": null,
						"invoiceNumberWeight": "font-bold",
						"signatureFontWeight": "font-bold",
						"tableSummaryOffGrid": "offgrid",
						"tableVisibleColumns": [
							"description",
							"price",
							"item"
						],
						"customInputAlignment": "justify-center",
						"headingVerticalAlign": "items-center",
						"invoiceInfoAlignment": "justify-start",
						"invoiceTitlePosition": null,
						"showDoubleLineSpacer": true,
						"showSingleLineSpacer": true,
						"tableBodyBorderStyle": "column",
						"tableLastColumnColor": "#ffffff",
						"tableSummaryAlignment": "left",
						"headingHorizontalAlign": "text-start",
						"signatureNameUppercase": "uppercase",
						"tableShowSummaryDetails": true,
						"signatureHorizontalAlign": "items-center"
					},
			--}}
			{{-- letterhead section --}}
			@include('template.v2.printPreview.headingLayout')

			{{-- body content section --}}
			<div class="section-spacing space-y-6">
				@include('template.v2.printPreview.beforeTable')
				@include('template.v2.printPreview.tableStyle')
				@include('template.v2.printPreview.afterTable')
			</div>

			{{-- footer section --}}
			@include('template.v2.printPreview.footer')
		</div>
	</body>
</html>
