<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_templates', function (Blueprint $table) {
            $table->id();
			$table->unsignedBigInteger('company_id')->nullable();
			$table->unsignedBigInteger('font_id')->nullable();
			$table->text('header_style')->nullable();
			$table->text('line_style')->nullable();
			$table->text('billto_style')->nullable();
			$table->text('body_style')->nullable();
			$table->text('table_style')->nullable();
			$table->text('inword_style')->nullable();
			$table->text('bankinfo_style')->nullable();
			$table->text('remarks_style')->nullable();
			$table->text('footer_style')->nullable();
            $table->timestamps();
			$table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoce_templates');
    }
};
