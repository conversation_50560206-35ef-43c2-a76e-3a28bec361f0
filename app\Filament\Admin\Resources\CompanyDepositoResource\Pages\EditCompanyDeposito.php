<?php

namespace App\Filament\Admin\Resources\CompanyDepositoResource\Pages;

use App\Filament\Admin\Resources\CompanyDepositoResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCompanyDeposito extends EditRecord
{
    protected static string $resource = CompanyDepositoResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
