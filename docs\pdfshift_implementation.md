# PDFShift Implementation

## Overview
Implementasi PDFShift sebagai alternatif PDF generation untuk invoice, berjalan parallel dengan Browsershot tanpa merusak kode yang sudah ada.

## Features Implemented

### 1. **Dual PDF Engine Support**
- **Browsershot**: Local PDF generation menggunakan Chrome headless
- **PDFShift**: Cloud-based PDF generation menggunakan API

### 2. **PDFShift Configuration**
- API Key: `sk_5c461d1273623eb7110803e89530d0994ab76894`
- API URL: `https://api.pdfshift.io/v3/convert/pdf`
- Timeout: 30 seconds
- Authentication: Basic Auth dengan API key

### 3. **Paper Size Support**
- **Standard Sizes**: A4, Letter, Legal
- **Custom F4**: 210 × 330 mm (custom dimensions)
- **Dynamic Margins**: Configurable per invoice template

### 4. **Auto Scaling Integration**
- Reuses existing `calculateOptimalScale()` method
- Applies scaling using CSS `zoom` property
- Same intelligent scaling logic as Browsershot

## Files Modified

### 1. **InvoiceController.php**
```php
// New method added
public function printInvoicePdfShift($record)

// New private methods
private function generatePdfWithPdfShift(...)
private function applyScaleToHtml($html, $scale)
```

### 2. **routes/web.php**
```php
// New route added
Route::get('/{record}/printInvoicePdfShift', [InvoiceController::class, 'printInvoicePdfShift'])
    ->name('printInvoicePdfShift');
```

### 3. **ListInvoices.php**
```php
// New action button added
Action::make('pdfShiftPreview')
    ->label('PDF Preview (PDFShift)')
    ->icon('heroicon-o-cloud')
    ->color('info')
    ->tooltip('PDF Preview using PDFShift API')
    ->url(fn (Invoice $record) => route('printInvoicePdfShift', $record))
    ->openUrlInNewTab()
```

## Usage

### 1. **From Filament Interface**
- Navigate to Invoice list
- Click action dropdown on any invoice
- Choose "PDF Preview (PDFShift)" option
- PDF will open in new tab

### 2. **Direct URL Access**
```
/management/{invoice_id}/printInvoicePdfShift
```

## API Request Structure

### Request Payload
```json
{
    "source": "<html_content>",
    "landscape": false,
    "use_print": true,
    "wait_until": "networkidle0",
    "wait_for": 1000,
    "format": "Legal",
    "margin": {
        "top": "10mm",
        "right": "10mm",
        "bottom": "10mm",
        "left": "10mm"
    },
    "options": {
        "print_background": true,
        "prefer_css_page_size": false,
        "display_header_footer": false
    }
}
```

### F4 Custom Format
```json
{
    "format": {
        "width": "210mm",
        "height": "330mm"
    }
}
```

## Scaling Implementation

### CSS Zoom Method
```php
private function applyScaleToHtml($html, $scale)
{
    $scaleStyle = "<style>body { zoom: {$scale}; }</style>";
    
    if (strpos($html, '</head>') !== false) {
        $html = str_replace('</head>', $scaleStyle . '</head>', $html);
    } else {
        $html = $scaleStyle . $html;
    }
    
    return $html;
}
```

## Error Handling

### API Error Response
```php
if ($response->successful()) {
    return $response->body();
} else {
    $errorMessage = $response->json()['error'] ?? 'Unknown PDFShift error';
    throw new \Exception("PDFShift API Error: {$errorMessage}");
}
```

### Fallback Strategy
- If PDFShift fails, returns HTML preview instead of PDF
- Logs detailed error information for debugging
- Same fallback logic as Browsershot implementation

## Logging

### Success Logging
```php
Log::info('PDFShift API Success', [
    'response_size' => strlen($response->body()),
    'content_type' => $response->header('Content-Type')
]);
```

### Error Logging
```php
Log::error('PDFShift Generation Failed', [
    'error' => $e->getMessage(),
    'paper_size' => $paperSize,
    'is_f4' => $isF4,
    'invoice_id' => $invoice->id
]);
```

## Comparison: Browsershot vs PDFShift

| Feature | Browsershot | PDFShift |
|---------|-------------|----------|
| **Location** | Local | Cloud API |
| **Dependencies** | Chrome, Node.js | Internet connection |
| **Performance** | Fast (local) | Network dependent |
| **Reliability** | Server dependent | API uptime dependent |
| **Cost** | Free | Paid API calls |
| **Scaling** | Server resources | API limits |
| **Customization** | Full control | API limitations |

## Benefits of Dual Implementation

✅ **Redundancy**: Backup option if one fails
✅ **Performance Testing**: Compare speed and quality
✅ **Flexibility**: Choose based on requirements
✅ **Zero Disruption**: Existing Browsershot code untouched
✅ **Easy Switching**: Same interface, different engine

## Testing

### Manual Testing Steps
1. Test both PDF generation methods on same invoice
2. Compare output quality and formatting
3. Test F4 paper size on both engines
4. Verify scaling works correctly
5. Test error handling scenarios

### Performance Testing
- Measure generation time for both methods
- Test with various invoice complexities
- Monitor API response times
- Check resource usage differences

## Future Enhancements

1. **Configuration Toggle**: Admin setting to choose default PDF engine
2. **Batch Processing**: Support for multiple invoices
3. **Caching**: Cache generated PDFs for repeated requests
4. **Queue Integration**: Background PDF generation for large invoices
5. **A/B Testing**: Automatic quality comparison between engines
