<?php

namespace App\Filament\Mc\Resources\McOrderResource\Pages;

use App\Filament\Mc\Resources\McOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Enums\Alignment;

class ListMcOrders extends ListRecords
{
    protected static string $resource = McOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
				->label('New Order')
				->modalHeading('New Order')
				->modalFooterActionsAlignment(Alignment::Right),
        ];
    }
	public function getHeading(): string
	{
        return 'New Orders';
	}
}
