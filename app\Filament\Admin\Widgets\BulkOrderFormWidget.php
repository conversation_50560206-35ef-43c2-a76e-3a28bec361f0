<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\CompanyDepositSummary;
use App\Models\Currency;
use App\Models\Invoice;
use App\Models\McAgent;
use App\Models\Order;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms\Components\{DatePicker, Fieldset, Group, Hidden, Placeholder, Repeater, Select, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class BulkOrderFormWidget extends Widget implements HasForms
{
	use InteractsWithForms;

	protected static string $view = 'filament.admin.widgets.bulk-order-form-widget';
	protected static ?string $heading = 'Bulk Order';

	// protected int | string | array $columnSpan = 3;
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin', 'Staff Order']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin', 'Staff Order']);
    }

	public ?array $data = [];

	public function mount(): void
	{
		$this->form->fill();
	}

	public function form(Form $form): Form
	{
		return $form
			->schema([
				Hidden::make('data'),
				Repeater::make('orders')
					->hiddenLabel()
					->columnSpanFull()->collapsible()
					->columns(1)
					->reorderable(false)
					->addActionLabel('Add Order')
					->extraAttributes(['class' => 'block md:hidden'])
					->schema([
						Hidden::make('order_create_by')
							->default(Auth::user()->id),
						Hidden::make('total'),
						Hidden::make('status')->default('Draft'),
						Hidden::make('balance'),
						TextInput::make('order_num')->integer()->required(),
						Select::make('agent_id')
							->searchable()
							->label('Agent')
							->default(2)
							->columnSpanFull()
							->placeholder(null)
							->required()
							->options(McAgent::pluck('name', 'id')),

						Select::make('company_id')
							->searchable()
							->label('Company')
							->placeholder(null)
							->columnSpanFull()
							->reactive()
							->options(
									Company::where('name','like','PT%')->get()
										->mapWithKeys(fn($company) => [
											$company->id => Str::limit($company->name, 10)
										])
										->toArray()
							),

						DatePicker::make('order_date')
							->default(today())
							->timezone('Asia/Jakarta')
							->columnSpanFull()
							->required()
							->native(false),

						Select::make('currency_id')
							->searchable()
							->label('Currency')
							->required()
							->placeholder(null)
							->live(onBlur: true)
							->columnSpanFull()
							->options(
								Currency::get()
									->mapWithKeys(fn($currency) => [
										$currency->id => "{$currency->symbol}"
									])
									->toArray()
							),

						TextInput::make('order_amount')
							->numeric()
							->required()
							->live(onBlur: true)
							->columnSpanFull()
							->extraInputAttributes(['class' => 'text-end'])
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								$bookingFee = 0;
								if ($state <= 100000) {
									$bookingFee = 10;
								}
								if ($state >= 100000) {
									$bookingFee = 15;
								}
								if ($state >= 150000) {
									$bookingFee = 30;
								}
								$set('booking_fee', $bookingFee);
								$total = $state;
								$set('total', $total);
							}),


						TextInput::make('booking_fee')
							->numeric()
							->live(onBlur: true)
							->columnSpanFull()
							->extraInputAttributes(['class' => 'text-end']),

						TextInput::make('rates')
							->numeric()
							->prefix('IDR')
							->live(onBlur: true)
							->columnSpanFull()
							->extraInputAttributes(['class' => 'text-end']),

						Placeholder::make('totalView')
							->hiddenLabel()
							->reactive()
							->content(function ($get) {
								$currency = $get('currency_id');
								if ($currency) {
									$currency = Currency::find($currency);
								}
								$symbol = $currency?->symbol ?? '';
								$total = number_format($get('total') ?? 0, 2, ',', '.');
								return new HtmlString("<div>{$symbol} {$total}</div>");
							})
							->extraAttributes(['class' => 'text-end font-bold text-2xl'])
							->columnSpanFull(),
					]),
				TableRepeater::make('orders')
					->hiddenLabel()
					->columnSpanFull()
					->reorderable(false)
					->addActionLabel('Add Order')
					->extraAttributes(['class' => 'hidden sm:block'])
					->headers([
						Header::make('Or. #')->width('7%'),
						Header::make('Agent')->width('12%'),
						Header::make('Company')->width('15%'),
						Header::make('Or. Date')->width('13%'),
						Header::make('Cur.')->width('10%'),
						Header::make('Or. Amount')->width('15%'),
						Header::make('Booking Fee')->width('10%'),
						Header::make('Rate')->width('15%'),
					])
					->schema([
						Hidden::make('order_create_by')
							->default(Auth::user()->id),
						Hidden::make('total'),
						Hidden::make('status')->default('Draft'),
						Hidden::make('balance'),
						TextInput::make('order_num')->integer()->required(),
						Select::make('agent_id')
							->searchable()
							->default(2)
							->placeholder(null)
							->required()
							->options(McAgent::pluck('name', 'id')),

						Select::make('company_id')
							->searchable()
							->placeholder(null)
							->columnSpan([
								'default' => 8,
								'lg' => 4,
							])
							->reactive()
							->options(
								Company::where('name','like','PT%')->get()
									->mapWithKeys(fn($company) => [
										$company->id => Str::limit($company->name, 10)
									])
									->toArray()
							),

						DatePicker::make('order_date')
							->default(today())
							->timezone('Asia/Jakarta')
							->required()
							->native(false),

						Select::make('currency_id')
							->searchable()
							->required()
							->placeholder(null)
							->live(onBlur: true)
							->options(
								Currency::get()
									->mapWithKeys(fn($currency) => [
										$currency->id => "{$currency->symbol}"
									])
									->toArray()
							),

						TextInput::make('order_amount')
							->numeric()
							->required()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end'])
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								$bookingFee = 0;
								if ($state <= 100000) {
									$bookingFee = 10;
								}
								if ($state >= 100000) {
									$bookingFee = 15;
								}
								if ($state >= 150000) {
									$bookingFee = 30;
								}
								$set('booking_fee', $bookingFee);
								$total = $state;
								$set('total', $total);
							}),


						TextInput::make('booking_fee')
							->numeric()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end']),

						TextInput::make('rates')
							->numeric()
							->prefix('IDR')
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end']),

						TextInput::make('charges')
							->numeric()
							->hidden()
							->prefix('IDR')
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end']),
					])
			])
			->statePath('data');
	}

	public function createBulkOrder(): void
	{
		$data = $this->form->getState();
		$orders = $data['orders'] ?? [];
		try {
			foreach ($orders as $order) {
				Order::create($order);
			}
			Notification::make()
				->title('Order Created Successfully')
				->success()
				->send();
				$this->form->fill();
		} catch (\Exception $e) {
			Notification::make()
				->title('Error Creating Order')
				->body($e->getMessage())
				->danger()
				->send();
		}
	}
}
