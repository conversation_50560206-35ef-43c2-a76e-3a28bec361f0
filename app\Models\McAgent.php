<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class McAgent extends Model
{
	use HasFactory, SoftDeletes;

	protected $fillable = [
		'name',
		'agent_code',
		'email',
		'phone',
	];

	public function members(): HasMany
	{
		return $this->hasMany(McCustomer::class, 'agent_id', 'id');
	}

	protected static function booted()
	{
		static::creating(function (McAgent $agent) {
			if (filled($agent->agent_code)) return;

			if (blank($agent->name)) {
				throw new \Exception('Nama agen harus diisi sebelum membuat agent_code.');
			}

			$words = explode(' ', strtoupper($agent->name));
			$prefix = count($words) >= 2
				? substr($words[0], 0, 1) . substr($words[1], 0, 1)
				: substr($words[0], 0, 2);

			$maxAttempts = 5;
			$attempt = 0;

			do {
				$suffix = strtoupper(substr(dechex(crc32($agent->name . microtime() . $attempt)), 0, 2));
				$code = $prefix . $suffix;
				$attempt++;
			} while (
				self::where('agent_code', $code)->exists() && $attempt < $maxAttempts
			);

			if (self::where('agent_code', $code)->exists()) {
				throw new \Exception('Gagal menghasilkan agent_code unik. Silakan coba lagi.');
			}

			$agent->agent_code = $code;
		});
	}
}
