<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\OrderResource\Pages;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\CompanyDepositSummary;
use App\Models\Currency;
use App\Models\Invoice;
use App\Models\McAgent;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Forms\Components\{DatePicker, Fieldset, Group, Hidden, Placeholder, Section, Select, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'icon-receipt-cutoff';
	protected static ?int $navigationSort = 0;
	protected static ?string $recordTitleAttribute = 'bank_acc_no';

	public function mount(): void
    {
        parent::mount();
        if (Auth::user()->hasRole('Staff Invoice')) {
            redirect(static::getResource()::getUrl('list'));
        }
    }

    public static function form(Form $form): Form
    {
		return $form
			->schema([
				Hidden::make('order_create_by')
					->default(Auth::user()->id),
				Hidden::make('total'),
				Hidden::make('status')->default('Draft'),
				Hidden::make('balance'),

				Group::make()
					->columns(2)
					->columnSpanFull()
					->schema([
						Select::make('agent_id')
							->label('Select Agent')
							->searchable()
							->required()
							->reactive()
							->columnSpan([
								'default' => 8,
								'lg' => 4,
							])
							->options(fn() => McAgent::pluck('name', 'id'))
							->suffixAction(
								ActionsAction::make('addAgent')
									->icon('heroicon-m-plus')
									->tooltip('Add New Agent')
									->url('#agent-modal'),
							),
					]),
				Select::make('invoice_id')
					->label('Order for Invoice')
					->searchable()
					->reactive()
					->dehydrated()
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->helperText('Attach this order to an invoice')
					->options(function() {
						return Invoice::where('parent_invoice_id', null)
							//where invoice_date after 18 June 2025. record di database type DATE 2025-07-18
							// ->whereDate('created_date', '>', '2025-07-19')
							->whereDate('created_at', '>=', Carbon::today())
							->where('order_id', null)
							->whereDoesntHave('order')
							->pluck('invoice_no', 'id');
					})
					->afterStateUpdated(function ($get, $set, $state) {
						if (!$state) {
							$set('order_date', null);
							$set('company_id', null);
							$set('currency_id', null);
							$set('order_amount', null);
							$set('rates', null);
							$set('sell_rates', null);
							$set('total', null);
							$set('status', 'Draft');
							return;
						}
						$invoice = Invoice::find($state);
						//set order data
						// $set('order_date', $invoice->invoice_date);
						$set('company_id', $invoice->client_id);
						$set('currency_id', $invoice->currency_id);
						$set('order_amount', $invoice->invoice_amount);
						$set('rates', $invoice->rates);
						$set('total', $invoice->invoice_amount);
						$set('status', 'Invoiced');
						if ($get('sell_rates') === null) {
							$selRates = $invoice->rates + 100;
							$set('sell_rates', $selRates);
						}
					}),

				DatePicker::make('order_date')
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->default(today())
					->timezone('Asia/Jakarta')
					->required()
					->native(false),

				Select::make('company_id')
					->label('Select Client')
					->helperText('Note: This will become the client in the invoice')
					->searchable()
					->required()
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->reactive()
					->options(fn() => Company::where('type', 2)->where('name','like','PT%')->pluck('name', 'id'))
					->suffixAction(
						ActionsAction::make('addCompany')
							->icon('heroicon-m-plus')
							->tooltip('Add New Company')
							->url('/admin/companies/create')
							// ->url('#company-modal')
							->openUrlInNewTab(),
					),

				Select::make('bank_id')
					->label('Select Bank')
					->searchable()
					->required()
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->helperText('The order amount will be debited from the selected bank account.')
					->reactive()
					->options(fn($get) => CompanyBank::where('company_id', $get('company_id'))
						->pluck('bank_name', 'id'))
					->afterStateUpdated(function (callable $set, $state, callable $get) {
						$balance = CompanyDepositSummary::where('bank_id', $state)->first()->balance ?? 0;
						$set('balance', $balance);
					}),

				Placeholder::make('warning')
					->hiddenLabel()
					->columnSpanFull()
					->reactive()
					->hidden(fn($get) => $get('bank_id') === null)
					->visible(fn() => Auth::user()->hasAnyRole(['admin', 'Super Admin']))
					->content(function ($get) {
						$balance = number_format($get('balance'), 2, ',', '.');
						return 'Balance available: IDR ' . $balance;
					})
					->extraAttributes(fn($get) => [
						'class' => ($get('balance') <= 0
							? 'text-danger-500'
							: 'text-primary-500'
						) . ' mb-3 mt-3 text-center uppercase border rounded border-gray-200 p-4 font-bold',
					]),
				Fieldset::make('Order')
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					// ->columnStart([
					// 	'default'=>1,
					// 	'lg'=>3,
					// ])
					->schema([
						Select::make('currency_id')
							->searchable()
							->label('Currency')
							->inlineLabel()
							->columnSpanFull()
							->required()
							->placeholder(null)
							->live(onBlur: true)
							->options(
								Currency::get()
									->mapWithKeys(fn($currency) => [
										$currency->id => "{$currency->symbol} - {$currency->name}"
									])
									->toArray()
							),

						TextInput::make('order_amount')
							->numeric()
							->inlineLabel()
							->columnSpanFull()
							->required()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end'])
							->prefix(fn($get) => $get('currency_id') ? (Currency::find($get('currency_id'))?->symbol ?? '') : '')
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								$bookingFee = 0;
								if ($state <= 100000) {
									$bookingFee = 10;
								}
								if ($state >= 100000) {
									$bookingFee = 15;
								}
								if ($state >= 150000) {
									$bookingFee = 30;
								}
								$set('booking_fee', $bookingFee);
								$total = $state;
								$set('total', $total);
							}),

						TextInput::make('booking_fee')
							->numeric()
							->inlineLabel()
							->prefix(fn($get) => $get('currency_id') ? (Currency::find($get('currency_id'))?->symbol ?? '') : '')
							->columnSpanFull()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end'])
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								// $set('total', $state + $get('order_amount'));
							}),

						Placeholder::make('totalView')
							->label('Total Order')
							->reactive()
							->content(function ($get) {
								$currency = $get('currency_id');
								if ($currency) {
									$currency = Currency::find($currency);
								}
								$symbol = $currency?->symbol ?? '';
								$total = number_format($get('total') ?? 0, 2, ',', '.');
								return new HtmlString("<div>{$symbol} {$total}</div>");
							})
							->inlineLabel()
							->extraAttributes(['class' => 'text-end font-bold'])
							->columnSpanFull(),
					]),
				Fieldset::make('Bill')
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->schema([
						TextInput::make('rates')
							->numeric()
							->label('Bank Rates')
							->inlineLabel()
							->columnSpanFull()
							->prefix('IDR')
							->live(onBlur: true)
							->required()
							->extraInputAttributes(['class' => 'text-end'])
							->afterStateUpdated(function ($get, $set, $state) {
								if ($get('sell_rates') === null) {
									$selRates = $state + 100;
									$set('sell_rates', $selRates);
								}
							}),
						TextInput::make('sell_rates')
							->numeric()
							->label('Sell Rates')
							->inlineLabel()
							->columnSpanFull()
							->prefix('IDR')
							->required()
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end']),
						TextInput::make('charges')
							->numeric()
							->inlineLabel()
							->columnSpanFull()
							->prefix('IDR')
							->live(onBlur: true)
							->extraInputAttributes(['class' => 'text-end']),

						Placeholder::make('totalBill')
							->label('Total Bill')
							->reactive()
							->content(function ($get) {
								$orderAfterRate = (($get('total') ?? 0) * floatval($get('sell_rates') ?? 0));

								$totalIdr = number_format($orderAfterRate + floatval($get('charges') ?? 0), 2, ',', '.');
								return new HtmlString("<div>IDR {$totalIdr}</div>");
							})
							->inlineLabel()
							->extraAttributes(['class' => 'text-end font-bold'])
							->columnSpanFull(),
					]),
			])
			->columns(8);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([])
            ->filters([])
            ->actions([])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\OrderManagement::route('/'),
            'list' => Pages\ListOrders::route('/list'),
            // 'create' => Pages\CreateOrder::route('/create'),
            'view' => Pages\ViewOrder::route('/{record}'),
            // 'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }
}
