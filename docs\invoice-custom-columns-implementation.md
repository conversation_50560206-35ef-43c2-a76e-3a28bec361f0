# Custom Bank Columns Implementation - InvoiceResource

## 📋 Overview

Implementasi custom bank columns untuk Invoice yang memungkinkan pengguna menampilkan custom fields dari bank account yang dipilih dan menambahkan custom fields tambahan sesuai kebutuhan.

## 🎯 Features Implemented

### ✅ **Core Features**
1. **JSON-based Custom Bank Fields** - Menggunakan field `bank_custom_columns` dengan tipe JSON
2. **Duplicate Key Prevention** - Mencegah duplikasi dengan delivered bank columns dan existing custom keys
3. **Field Type Selection** - Support untuk Text, TextArea, dan RichText fields
4. **Character Limit** - Maximum 20 karakter untuk field keys
5. **Form-only Display** - Custom fields hanya ditampilkan di form untuk simplicity

### ✅ **Integration Features**
1. **Bank Account Integration** - Custom fields terintegrasi dengan bank information section
2. **Auto-load from Bank** - Custom fields otomatis ter-load saat mengganti company
3. **Template Support** - Dapat menampilkan custom fields dari bank account yang dipilih
4. **Additional Fields** - User dapat menambah custom fields tambahan
5. **Data Persistence** - Custom fields tersimpan di database invoice

## 🔧 Technical Implementation

### **Database Structure**
```sql
-- Field sudah ada dari migrasi sebelumnya
bank_custom_columns JSON NULL
```

### **Data Structure**
```json
{
  "reference_number": {
    "type": "text",
    "value": "BANK-REF-2024-001"
  },
  "special_instructions": {
    "type": "richtext",
    "value": "<p>Handle with <strong>special care</strong></p>"
  },
  "contact_person": {
    "type": "text",
    "value": "John Doe - Account Manager"
  }
}
```

### **Model Updates**
- **Invoice.php**:
  - Added `bank_custom_columns` to `$casts` array
  - JSON casting untuk proper data handling

### **Form Components**
- **Repeater Component** untuk input key-value pairs
- **Type Selector** untuk memilih Text atau RichText
- **Real-time Validation** untuk duplicate prevention
- **Character Counter** untuk key field (max 20 chars)

## 🎨 User Experience

### **Adding Custom Bank Fields**
1. User membuka form Create/Edit Invoice
2. Navigate ke section "Bank Information"
3. Expand "Custom Bank Fields" fieldset
4. Click "Add Custom Bank Field"
5. Input field name (max 20 chars) + pilih type
6. Input value sesuai type yang dipilih
7. Real-time validation untuk duplicate keys

### **Viewing Custom Bank Fields**
1. Custom fields ditampilkan dalam fieldset "Custom Bank Fields"
2. Collapsible section untuk better organization
3. Type-based input (Text/RichText) sesuai selection

## 🔄 Data Flow

### **Input Processing**
```php
// User Input (Repeater)
[
  ['key' => 'reference_number', 'type' => 'text', 'value' => 'BANK-REF-2024-001'],
  ['key' => 'special_notes', 'type' => 'richtext', 'value' => '<p>Important</p>']
]

// Processed to JSON
{
  "reference_number": {"type": "text", "value": "BANK-REF-2024-001"},
  "special_notes": {"type": "richtext", "value": "<p>Important</p>"}
}
```

### **Output Processing**
```php
// From Database JSON
{
  "reference_number": {"type": "text", "value": "BANK-REF-2024-001"}
}

// Converted to Repeater Format
[
  ['key' => 'reference_number', 'type' => 'text', 'value' => 'BANK-REF-2024-001']
]
```

## 🛡️ Validation & Security

### **Duplicate Prevention**
- Check against delivered bank columns (existing invoice bank fields)
- Check against existing custom keys dalam record yang sama
- Visual warning dengan helper text dan icon
- Auto-null value jika duplicate detected

### **Delivered Bank Columns**
```php
$deliveredBankColumns = [
    'bank_acc_name', 'bank_code', 'bank_acc_no', 'bank_acc_address',
    'bank_name', 'bank_address', 'bank_correspondent', 'swift', 'swift_correspondent',
    'routing_no', 'transit', 'tt_charge', 'iban', 'institution', 'bsb',
    'branch_code', 'sort_code', 'branch_bank', 'ABA', 'IFSC'
];
```

### **Data Validation**
- Required field name
- Maximum 20 characters untuk keys
- Type selection required
- Value required (sesuai type)

## 📊 Implementation Details

### **Files Modified**

1. **`app/Models/Invoice.php`**:
   - Added JSON casting untuk `bank_custom_columns`

2. **`app/Filament/Admin/Resources/InvoiceResource.php`**:
   - Added custom bank fields form section
   - Added data processing methods
   - Added import untuk Repeater component

3. **`app/Filament/Admin/Resources/InvoiceResource/Pages/EditInvoice.php`**:
   - Added data mutation hooks untuk edit operations

4. **`app/Filament/Admin/Resources/InvoiceResource/Pages/CreateInvoice.php`**:
   - Added data mutation hook untuk create operations

### **Key Methods**

#### **processBankCustomColumnsData()**
- Converts repeater data to JSON format
- Handles duplicate prevention
- Cleans up temporary fields

#### **mutateBankCustomColumnsDataBeforeFill()**
- Converts JSON data to repeater format for editing
- Prepares data for form display

#### **loadBankCustomColumns()**
- Loads custom columns from bank account to invoice form
- Called when company selection changes
- Automatically populates custom fields from default bank account

## 🚀 Usage Examples

### **Common Use Cases**
1. **Bank Reference Numbers**: `bank_reference` (text)
2. **Special Instructions**: `special_instructions` (richtext)
3. **Contact Person**: `contact_person` (text)
4. **Internal Notes**: `internal_notes` (richtext)
5. **Processing Codes**: `processing_code` (text)

### **Sample Data**
```json
{
  "bank_reference": {
    "type": "text",
    "value": "BANK-REF-INV-2024-001"
  },
  "contact_person": {
    "type": "text",
    "value": "Jane Smith - International Transfer Specialist"
  },
  "special_instructions": {
    "type": "richtext",
    "value": "<p>This invoice requires <strong>priority processing</strong> for international transfer.</p>"
  }
}
```

## 🔮 Future Enhancements

### **Phase 2 Possibilities**
1. **Bank Template Integration**: Load custom fields from selected bank account
2. **Field Templates**: Preset custom field templates for common bank scenarios
3. **Conditional Logic**: Show/hide fields based on bank type or currency
4. **Bulk Operations**: Mass update custom fields across multiple invoices

### **Advanced Features**
1. **Field Validation Rules**: Custom validation per field type
2. **Field Dependencies**: Fields that depend on bank selection
3. **Field Grouping**: Organize custom fields in logical groups
4. **Field Permissions**: Role-based access to custom fields

## ✅ Testing Checklist

- [ ] Create invoice dengan custom bank fields
- [ ] Edit invoice dengan existing custom bank fields
- [ ] Duplicate key prevention working
- [ ] Type selection (text/richtext) working
- [ ] Character limit enforcement
- [ ] Data persistence across sessions
- [ ] Integration dengan bank information section

## 📝 Notes

- Custom bank columns bersifat **optional** dan tidak mengganggu workflow existing
- Terintegrasi dengan bank information section
- Form-only display untuk simplicity
- Extensible untuk future bank-related enhancements
- Compatible dengan existing invoice templates dan bank account selection
