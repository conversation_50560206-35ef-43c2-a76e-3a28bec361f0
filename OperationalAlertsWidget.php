<?php

namespace App\Filament\Admin\Widgets;

use App\Models\CompanyDepositSummary;
use App\Models\Order;
use Filament\Widgets\Widget;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OperationalAlertsWidget extends Widget
{
    protected static bool $isDiscovered = false; // Hide widget
    protected static string $view = 'filament.admin.widgets.operational-alerts';
    protected static ?string $pollingInterval = '30s';
    protected int | string | array $columnSpan = 1;
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
    public function getViewData(): array
    {
        // 1. Low Balance Banks Alert
        $lowBalanceThreshold = 1000000; // 1 million IDR
        $lowBalanceBanks = CompanyDepositSummary::where('balance', '<', $lowBalanceThreshold)
            ->join('company_banks', 'view_company_deposit_summary.bank_id', '=', 'company_banks.id')
            ->select('company_banks.bank_name', 'view_company_deposit_summary.balance')
            ->get();

        // 2. Stale Orders Alert (Draft orders older than 7 days)
        $staleOrdersThreshold = Carbon::now()->subDays(7);
        $staleOrders = Order::where('status', 'Draft')
            ->where('created_at', '<', $staleOrdersThreshold)
            ->count();

        // 3. High Activity Alert (More than 10 transactions today)
        $today = Carbon::now()->format('Y-m-d');
        $todayTransactions = DB::table('company_depositos')
            ->whereNull('deleted_at')
            ->whereDate('trx_date', $today)
            ->count();

        // 4. Zero Balance Banks
        $zeroBalanceBanks = CompanyDepositSummary::where('balance', '<=', 0)
            ->join('company_banks', 'view_company_deposit_summary.bank_id', '=', 'company_banks.id')
            ->count();

        return [
            'lowBalanceBanks' => $lowBalanceBanks,
            'staleOrders' => $staleOrders,
            'todayTransactions' => $todayTransactions,
            'zeroBalanceBanks' => $zeroBalanceBanks,
            'lowBalanceThreshold' => $lowBalanceThreshold,
        ];
    }
}
