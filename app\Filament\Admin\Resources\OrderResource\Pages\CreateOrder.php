<?php

namespace App\Filament\Admin\Resources\OrderResource\Pages;

use App\Filament\Admin\Resources\OrderResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class CreateOrder extends CreateRecord
{
    protected static string $resource = OrderResource::class;
    public static string | Alignment $formActionsAlignment = Alignment::Right;
    protected static ?string $title = 'Order Management';
	public function getHeading(): string | Htmlable
	{
        // return new HtmlString('<span class="text-lg">New Order</span>');
        return 'New Order';
	}


	protected static bool $canCreateAnother = false;
	protected function getCancelFormAction(): Actions\Action
    {
        return Actions\Action::make('cancel')
            ->label('Cancel')
            ->color('gray')
            ->action(fn () => $this->dispatch('close-modal'));
    }
	public function getRedirectUrl(): string
	{
		return '/admin';
	}
}
