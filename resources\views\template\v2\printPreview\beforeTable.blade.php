@php
	// Extract layout configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$repeaterLayout = $layoutConfig['repeater_layout'] ?? [];

	// Content type mapping for print preview
	$contentPartials = [
		'amount_inword' => 'amount_inword',
		'bank_info' => 'bankinfo',
		'bill_to' => 'billto',
		'company_info' => 'companyinfo',
		'footer' => 'footer',
		'invoice_info' => 'invoice_info',
		'invoicesummary' => 'invoicesummary',
		'remark' => 'remark',
		'signature' => 'signature',
	];
@endphp

{{-- Before Table Content Section --}}
@foreach ($repeaterLayout as $row)
	@if ($row['row_position'] === 'before_table')
		<div class="section-spacing">
			<table class="w-full table-auto text-compact border-separate border-spacing-y-1">
				<tr>
					<td class="align-top {{ $row['column_size'] == 1 ? 'w-full' : 'w-1/3' }}  pe-2 space-y-3">
						@if (!empty($row['column_1_content']))
							@foreach ((array) $row['column_1_content'] as $content)
								@if (isset($contentPartials[$content]))
									@include('template.v2.printPreview.' . $contentPartials[$content])
								@endif
							@endforeach
						@endif
					</td>
					@if (!empty($row['column_2_content']) && $row['column_size'] == 2)
						<td width="3%" class=""></td>
						<td class="align-top w-1/2 space-y-3 ">
							@foreach ((array) $row['column_2_content'] as $content)
								@if (isset($contentPartials[$content]))
									@include('template.v2.printPreview.' . $contentPartials[$content])
								@endif
							@endforeach
						</td>
					@endif
				</tr>
			</table>
		</div>
	@endif
@endforeach
