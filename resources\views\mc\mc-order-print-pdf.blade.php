<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ $payload['orderCode'] ?? 'no data' }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="{{ $payload['fontSource'] }}" rel="stylesheet">
    @vite('resources/css/app.css')

    <style>
        @page {
            size: 210mm 148mm;
            margin: {{ $payload['marginTop'] }}mm {{ $payload['marginRight'] }}mm {{ $payload['marginBottom'] }}mm {{ $payload['marginLeft'] }}mm;
        }

		@media print {
			.page-break {
				page-break-before: always;
			}
		}

		th.main, td.main {
            border: 1px solid #d1d5db;
            padding: 6px 8px;
        }

        thead.main {
            background-color: #f3f4f6;
            font-weight: 600;
        }
    </style>
</head>
<body class="p-1 space-y-3 text-xs">
	<div class="grid grid-cols-4 items-start">
		<div class="flex justify-between items-center">
			<div class="flex flex-col">
				<span class="text-xl font-bold">{{$payload['customer']}}</span>
				<span class="italic text-sm">{{$payload['customerId']}}</span>
				@if (str_contains($payload['orderCode'], 'CLS'))
					<span class="italic text-blue-500 text-xs">This statement serves as the closing summary of all transactions related to this account.</span>
				@endif
			</div>
		</div>
		<div class="col-span-2"></div>
		<div class="flex flex-col">
			{{-- <span>Order Information:</span> --}}
			<table >
				<tbody>
					<tr>
						<td width="25%">Order #</td>
						<td width="5%">:</td>
						<th class="text-end">{{$payload['orderCode']}}</th>
					</tr>
					<tr>
						<td width="25%">Date</td>
						<td width="5%">:</td>
						<th width="70%" class="text-end">{{date('F d, Y', strtotime($payload['orderDate']))}}</th>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<hr>
	<table class="w-full">
		<thead class="main">
			<tr>
				<th class="main">Cur</th>
				<th class="main" width="20%">Amount</th>
				<th class="main" width="15%">Rate</th>
				<th class="main" width="15%">Charges</th>
				<th class="main" width="25%">Sub Total</th>
			</tr>
		</thead>
		<tbody>
			<tr class="main">
				<td class="text-center main">{{$payload['fxName']}} ({{$payload['fxSymbol']}})</td>
				<td class="text-end main">{{$payload['fxSymbol']}} {{number_format($payload['amount'], 2,',','.')}}</td>
				<td class="text-end main">IDR {{number_format($payload['rate'], 2,',','.')}}</td>
				<td class="text-end main">IDR {{number_format($payload['charges'], 2,',','.')}}</td>
				<td class="text-end main">IDR {{number_format($payload['totalCurrent'], 2,',','.')}}</td>
			</tr>
		</tbody>
		<tfoot>
			<tr class="">
				<td class="text-end main" colspan="4">
					CURRENT ORDER:
				</td>
				<td class="text-end font-bold main">
					<span>IDR {{number_format($payload['totalCurrent'], 2,',','.')}}</span>
				</td>
			</tr>
			<tr class="">
				<td class="text-end main" colspan="4">
					AVAILABLE DEPOSIT:
				</td>
				<td class="text-end font-bold main">
					<span>IDR {{number_format($payload['deposit'], 2,',','.')}}</span>
				</td>
			</tr>
			<tr class="">
				<td class="text-end main" colspan="4">
					FINAL BALANCE:
				</td>
				<td class="text-end font-bold main">
					<span>IDR {{number_format($payload['balance'], 2,',','.')}}</span>
				</td>
			</tr>
		</tfoot>
	</table>

	<div class="row-mb-5 col-12">
		<div class="mb-3" style="font-style: italic">
			<span class="text-start">
				Ensure payment is made immediately within the same day before 12.00 PM.
			</span>
		</div>
		<div class="row d-flex justify-content-between align-items-start">
			<div class="col-7">
				<ul class="list-unstyled row" style="font-size: 0.755rem">
					<li class="font-bold">Bank Informations</li>
					<li class="d-flex justify-content-start">
						<span class="col-4">Bank:</span>
						<span class="col-7">{{$payload['bankName']}}</span>
					</li>
					<li class="d-flex justify-content-start">
						<span class="col-4">Account #:</span>
						<span class="col-7">{{$payload['bankAccNo']}}</span>
					</li>
					<li class="d-flex justify-content-start">
						<span class="col-4">Account Name:</span>
						<span class="col-7">{{$payload['bankAccName']}}</span>
					</li>
				</ul>
			</div>
		</div>
	</div>

	{{-- <div class="footer">
		<div class="container mt-5">
			<div style="margin: 0 auto; text-align: center;">
				<div class="col-12" style="font-family: \'Courier New\', monospace; font-size: 11px;">
					<span class="text-center" style="font-size: 10px;">
						This is a digitally generated invoice, no authorization signature is required
					</span>
				</div>
			</div>
		</div>
	</div> --}}
</body>
</html>
