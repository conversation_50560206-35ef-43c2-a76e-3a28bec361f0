<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\CompanyDepositSummary;
use App\Models\Currency;
use App\Models\Invoice;
use App\Models\MasterInvoiceDetail;
use App\Models\McAgent;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\{DatePicker, Fieldset, Group, Hidden, Placeholder, Select, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Notifications\Notification;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\{SelectColumn, TextColumn, TextInputColumn};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class OrderHistoryWidget extends BaseWidget
{
	protected static ?string $heading = 'Order History';
	protected int | string | array $columnSpan = 'full';

	protected $listeners = ['order-created' => '$refresh'];
	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['Staff Order', 'Staff Invoice', 'Admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['Staff Order', 'Staff Invoice', 'Admin', 'Super Admin']);
	}

	public function table(Table $table): Table
	{
		return $table
			->poll('10s')
			->recordClasses(function (Model $record) {
				$classes = [];
				// Ambil daftar ID bank milik company
				$companyBanks = $record->company?->banks->pluck('id') ?? collect();

				// Validasi apakah bank_id dari order ada di daftar bank company
				$isBankValid = $companyBanks->contains($record->bank_id);

				// Prioritas merah dulu: bank invalid
				if (!$isBankValid) {
					$classes[] = 'bg-red-50 dark:bg-red-600';
				}
				// Prioritas kedua: semua field kosong → orange/kuning
				elseif (
					is_null($record->company_id) ||
					is_null($record->bank_id) ||
					is_null($record->rates) ||
					is_null($record->sell_rates)
				) {
					$classes[] = 'bg-orange-50 dark:bg-orange-600';
				}

				return implode(' ', $classes);
			})
			->query(function () {
				$start = Carbon::now()->startOfMonth()->subDays(7); // 2 hari sebelum awal bulan ini
				$end = Carbon::now()->endOfMonth(); // akhir bulan ini

				$query = Order::query()
					->with(['agent', 'company', 'currency', 'user'])
					->whereBetween('order_date', [$start, $end]);
				return $query;
			})
			->columns([
				SelectColumn::make('company_id')
					->label('Company')
					->searchable()
					->sortable()
					->options(
						Company::where('name','like','PT%')->get()
							->mapWithKeys(fn($company) => [
								$company->id => Str::limit($company->name, 10)
							])
							->toArray()
					),

				SelectColumn::make('bank_id')
					->searchable()
					->sortable()
					->options(function ($record) {
						if (! $record?->company_id) {
							return [];
						}

						return CompanyBank::where('company_id', $record->company_id)
							->pluck('bank_name', 'id')
							->map(fn ($name) => Str::limit($name, 10))
							->toArray();
					}),

				TextColumn::make('order_date')
					->label('Order Date')
					->date()
					->sortable(),
				TextColumn::make('currency.symbol')
					->label('Cur.')
					->alignCenter()
					->sortable(),
				TextColumn::make('order_amount')
					->label('Amount')
					->formatStateUsing(fn($record, $state) => number_format($state, 2, ',', '.'))
					->alignEnd()
					->searchable()
					->sortable(),
				TextColumn::make('booking_fee')
					->label('Fee')
					->formatStateUsing(fn($record, $state) => number_format($state, 2, ',', '.'))
					->alignEnd()
					->sortable(),
				TextColumn::make('total')
					->label('Total')
					->formatStateUsing(fn($record, $state) => number_format($state, 2, ',', '.'))
					->alignEnd()
					->sortable(),
				TextInputColumn::make('rates')
					->label('Rate')
					->type('number')
					->alignEnd()
					->sortable(),
				TextInputColumn::make('rates')
					->label('Rate')
					->type('number')
					->alignEnd()
					->sortable(),
				TextColumn::make('status')
					->searchable()
					->badge()
					->color(fn($record) => match ($record->status) {
						'Draft' => 'danger',
						'Forwarded' => 'warning',
						'Invoiced' => 'success',
					})
					->description(function ($record) {
						$companyBanks = $record->company?->banks->pluck('id') ?? collect();

						if (!$record->bank || !$companyBanks->contains($record->bank_id)) {
							return new HtmlString('<span class="text-red-600 font-semibold">⚠ Bank Data!</span>');
						}

						return null; // bank valid
					})
					->formatStateUsing(function ($state) {
						if (Auth::user()->hasRole('Staff Invoice')) {
							return match ($state) {
								'Draft' => 'Draft',
								'Forwarded' => 'New',
								'Invoiced' => 'Invoiced',
							};
						}
						return match ($state) {
							'Draft' => 'Draft',
							'Forwarded' => 'New',
							'Invoiced' => 'Invoiced',
						};
					}),
				TextColumn::make('user.name')
					->label('Created By')
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
				TextColumn::make('created_at')
					->label('Created At')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
			])
			->filters([
				Filter::make('order_date')
					->form([
						DatePicker::make('from')->default(today()),
						DatePicker::make('until')->default(today()),
					])
					->query(function (Builder $query, array $data): Builder {
						return $query
							->when(
								$data['from'],
								fn(Builder $query, $date): Builder => $query->whereDate('order_date', '>=', $date),
							)
							->when(
								$data['until'],
								fn(Builder $query, $date): Builder => $query->whereDate('order_date', '<=', $date),
							);
					}),
				SelectFilter::make('status')
					->options([
						'Draft' => 'Draft',
						'Forwarded' => 'Forwarded',
						'Invoiced' => 'Invoiced',
					]),
				SelectFilter::make('company_id')
					->label('Company')
					->options(
						Company::where('type', 2)->where('name','like','PT%')->get()
							->mapWithKeys(fn($company) => [
								$company->id => Str::limit($company->name, 10)
							])
							->toArray()
					)
					->searchable(),
			])
			->actions([
				Action::make('forward')
					->label('Forward')
					->icon('heroicon-o-forward')
					->iconButton()
					->tooltip('Send to Invoicing')
					->color('success')
					->requiresConfirmation()
					->modalHeading('Forward Order to Invoicing')
					->modalDescription('You are about to forward this order to the Invoicing Team. Are you sure you want to continue?.')
					->modalSubmitActionLabel('Yes, Proceed')
					->visible(fn() => Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']))
					->hidden(fn (Order $record) =>
						!(
							$record->status === 'Draft'
							&& !is_null($record->company_id)
							&& !is_null($record->rates)
							&& !is_null($record->sell_rates)
							&& !is_null($record->bank_id)
						)
					)
					->action(function (Order $record) {
						try {
							$record->update(['status' => 'Forwarded']);

							Notification::make()
								->title('Berhasil')
								->body('Order forwarded to the Invoicing Team.')
								->success()
								->send();
						} catch (\Throwable $e) {
							Notification::make()
								->title('Gagal')
								->body('Terjadi kesalahan saat mencoba mem-forward order.')
								->danger()
								->send();
						}
					}),

				Action::make('invoice')
					->label('Invoice')
					->icon('heroicon-o-clipboard-document-check')
					->iconButton()
					->tooltip('Create Invoice')
					->color('primary')
					->requiresConfirmation()
					->modalHeading('Create Invoice')
					->modalDescription('You are about to create an invoice for this order. Are you sure you want to continue?.')
					->modalSubmitActionLabel('Yes, Proceed')
					->form([
						Select::make('client_id')
							->label('Select Company')
							->helperText('Note: This will become the company issuing the invoice')
							->searchable()
							->options(function ($record) {
								return Company::whereNotIn('id', [
									$record->company_id, // Hindari client asal
								])->where('type', 1)->pluck('name', 'id');
							}) // sesuaikan model & kolom
							->required()
							->suffixAction(
								ActionsAction::make('addCompany')
									->icon('heroicon-m-plus')
									->tooltip('Add New Company')
									->url('/admin/companies/create')
									// ->url('#company-modal')
									->openUrlInNewTab(),
							),
					])
					->visible(fn() => Auth::user()->hasAnyRole(['Staff Invoice', 'admin', 'Super Admin']))
					->hidden(fn(Order $record) => $record->status !== 'Forwarded')
					->action(function (Order $record, array $data) {
						$invoice = DB::transaction(function () use ($record, $data) {
							$record->update(['status' => 'Invoiced']);
							// Perbaikan: company_id di order seharusnya adalah client_id di invoice
							// dan client_id yang dipilih seharusnya adalah company_id di invoice
							$client = Company::find($record->company_id); // Client dari order
							$company = Company::find($data['client_id']); // Company yang dipilih

							// Detail invoice diambil berdasarkan business type company yang mengeluarkan invoice
							$detail = MasterInvoiceDetail::where('business_type', $company->business_type)->get();
							// Buat invoice terlebih dahulu (tanpa old bank fields)
							$invoice = \App\Models\Invoice::create([
								'order_id' => $record->id,
								'company_id' => $data['client_id'], // Company yang dipilih
								'client_id' => $record->company_id, // Client dari order
								// 'booking_fee' => $record->booking_fee,
								'rates' => $record->rates,
								'order_amount' => $record->order_amount,
								'invoice_amount' => $record->total,
								'status' => 'Draft',
								'invoice_date' => $record->order_date,
								'due_date' => Carbon::parse($record->order_date)->addDays(7),
								'invoice_create_by' => Auth::user()->id,
								'currency_id' => $record->currency_id,
								//include client address (sesuai client address)
								'client_address' => $client->address,
							]);

							// Banks will be empty initially - user must manually select banks in invoice form
							// No automatic bank creation to avoid CompanyBank duplication

							// Tambahkan detail invoice dari master invoice detail sesuai business type company yang mengeluarkan invoice
							if ($detail->isNotEmpty()) {
								foreach ($detail as $item) {
									$invoice->invoiceDetails()->create([
										'company_id' => $data['client_id'], // Company yang mengeluarkan invoice
										'client_id' => $record->company_id, // Client penerima invoice (dari order)
										'description' => $item->description,
										'quantity' => $item->quantity,
										'unit' => $item->unit,
										'price' => $item->price,
										'sub_total' => $item->quantity * $item->price,
										'status' => 'Active',
									]);
								}

								// Hitung ulang total invoice
								$total = $invoice->invoiceDetails()->sum('sub_total');
								$invoice->invoice_amount = $total;
								$invoice->inv_sub_total = $total;
								$invoice->save();
							}

							return $invoice;
						});
						Notification::make()
							->title('Invoice created successfully. Please edit the invoice to complete the process.')
							->success()
							->send();
						// 3. Redirect ke halaman edit invoice
						return redirect()->route('filament.admin.resources.invoices.edit', ['record' => $invoice->id]);
					}),
				ActionGroup::make([
					ViewAction::make()
						->tooltip('View Order Details')
						->modalWidth('3xl')
						->modalFooterActionsAlignment(Alignment::Right)
						->modalHeading(fn($record) => 'Order Details: ' . $record->order_no)
						->form($this->getOrderForm())
						->fillForm(function (Order $record): array {
							return [
								'order_no' => $record->order_no,
								'order_create_by' => $record->order_create_by,
								'company_id' => $record->company_id,
								'bank_id' => $record->bank_id,
								'order_date' => $record->order_date,
								'currency_id' => $record->currency_id,
								'order_amount' => $record->order_amount,
								'booking_fee' => $record->booking_fee,
								'rates' => $record->rates,
								'agent_id' => $record->agent_id,
								'sell_rates' => $record->sell_rates,
								'charges' => $record->charges,
								'total' => $record->total,
								'status' => $record->status,
								'balance' => CompanyDepositSummary::where('bank_id', $record->bank_id)->first()->balance ?? 0,
							];
						})
						->disabledForm()
						->modalSubmitAction(false)
						->extraModalFooterActions([
							Action::make('viewInvoice')
								->label('View Detail')
								->color('info')
								->visible(fn(Order $record) => $record->invoice)
								->url(fn($record) => route('filament.admin.resources.orders.view', $record)) // sesuaikan dengan route kamu
								->openUrlInNewTab(true)
								->icon('heroicon-o-document-text'),
						]),
					EditAction::make()
						->tooltip('Edit Order')
						->modalWidth('3xl')
						->modalFooterActionsAlignment(Alignment::Right)
						->modalHeading(fn($record) => 'Edit Order: ' . $record->order_no)
						->visible(fn() => Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']))
						// ->hidden(fn (Order $record) => $record->status !== 'Draft')
						->form($this->getOrderForm())
						->fillForm(function (Order $record): array {
							return [
								'order_no' => $record->order_no,
								'order_create_by' => $record->order_create_by,
								'company_id' => $record->company_id,
								'invoice_id' => $record->invoice_id,
								'bank_id' => $record->bank_id,
								'order_date' => $record->order_date,
								'currency_id' => $record->currency_id,
								'order_amount' => $record->order_amount,
								'booking_fee' => $record->booking_fee,
								'rates' => $record->rates,
								'agent_id' => $record->agent_id,
								'sell_rates' => $record->sell_rates,
								'charges' => $record->charges,
								'total' => $record->total,
								'status' => $record->status,
								'balance' => CompanyDepositSummary::where('bank_id', $record->bank_id)->first()->balance ?? 0,
							];
						})
						->action(function (Order $record, array $data): void {
							try {
								$record->update([
									'agent_id' => $data['agent_id'],
									'company_id' => $data['company_id'],
									'invoice_id' => $data['invoice_id'],
									'bank_id' => $data['bank_id'],
									'order_date' => $data['order_date'],
									'currency_id' => $data['currency_id'],
									'order_amount' => $data['order_amount'],
									'booking_fee' => $data['booking_fee'],
									'rates' => $data['rates'],
									'sell_rates' => $data['sell_rates'],
									'charges' => $data['charges'],
									'total' => $data['total'],
								]);

								Notification::make()
									->title('Order Updated Successfully')
									->success()
									->send();

								// Dispatch event to refresh other widgets
								$this->dispatch('order-created');
							} catch (\Exception $e) {
								Notification::make()
									->title('Error Updating Order')
									->body($e->getMessage())
									->danger()
									->send();
							}
						}),
				]),
			])
			->defaultSort('created_at', 'desc')
			->striped()
			->paginated([10, 25, 50])
			->defaultPaginationPageOption(10)
			->emptyStateHeading('No Orders Found')
			->emptyStateDescription('Create your first order using the form above.');
	}

	protected function getOrderForm(): array
	{
		return [
			Group::make()
				->schema([
					Hidden::make('order_create_by')
						->default(Auth::user()->id),
					Hidden::make('total'),
					Hidden::make('status')->default('Draft'),
					Hidden::make('balance'),
					TextInput::make('order_no')
						->label('Order No')
						->inlineLabel()
						->extraInputAttributes(['class' => 'text-center font-bold'])
						->default(function () {
							$userId = Auth::id();
							$today = now()->format('Ymd');
							$prefix = 'ORD';
							$countToday = Order::where('order_create_by', $userId)
								->whereDate('created_at', today())
								->count() + 1;
							return sprintf('%s-%s-U%d-%05d', $prefix, $today, $userId, $countToday);
						})
						->disabled()
						->dehydrated()
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						])
						->columnStart([
							'default' => 1,
							'lg' => 5
						]),

					Select::make('agent_id')
						->label('Select Agent')
						->searchable()
						->reactive()
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						])
						->options(fn() => McAgent::pluck('name', 'id'))
						->suffixAction(
							ActionsAction::make('addAgent')
								->icon('heroicon-m-plus')
								->tooltip('Add New Agent')
								->url('#agent-modal'),
						),

					Select::make('company_id')
						->label('Select Client')
						->helperText('Note: This will become the client in the invoice')
						->searchable()
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						])
						->reactive()
						->options(fn() => Company::where('type', 2)->pluck('name', 'id'))
						->suffixAction(
							ActionsAction::make('addCompany')
								->icon('heroicon-m-plus')
								->tooltip('Add New Company')
								->url('/admin/companies/create')
								// ->url('#company-modal')
								->openUrlInNewTab(),
						),


				Select::make('invoice_id')
					->label('Order for Invoice')
					->searchable()
					->reactive()
					->dehydrated()
					->columnSpan([
						'default' => 8,
						'lg' => 4,
					])
					->helperText('Attach this order to an invoice')
					->options(function() {
						return Invoice::where('parent_invoice_id', null)
							//where invoice_date after 18 June 2025. record di database type DATE 2025-07-18
							// ->whereDate('created_date', '>', '2025-07-19')
							->whereDate('created_at', '>=', Carbon::today())
							->where('order_id', null)
							->whereDoesntHave('order')
							->pluck('invoice_no', 'id');
					})
					->afterStateUpdated(function ($get, $set, $state) {
						if (!$state) {
							$set('order_date', null);
							$set('company_id', null);
							$set('currency_id', null);
							$set('order_amount', null);
							$set('rates', null);
							$set('sell_rates', null);
							$set('total', null);
							$set('status', 'Draft');
							return;
						}
						$invoice = Invoice::find($state);
						//set order data
						// $set('order_date', $invoice->invoice_date);
						$set('company_id', $invoice->client_id);
						$set('currency_id', $invoice->currency_id);
						$set('order_amount', $invoice->invoice_amount);
						$set('rates', $invoice->rates);
						$set('total', $invoice->invoice_amount);
						$set('status', 'Invoiced');
						if ($get('sell_rates') === null) {
							$selRates = $invoice->rates + 100;
							$set('sell_rates', $selRates);
						}
					}),

					Select::make('bank_id')
						->label('Select Bank')
						->searchable()
						->columnSpan([
							'default' => 8,
							'lg' => 6,
						])
						->helperText('The order amount will be debited from the selected bank account.')
						->reactive()
						->options(fn($get) => CompanyBank::where('company_id', $get('company_id'))
							->pluck('bank_name', 'id'))
						->afterStateUpdated(function (callable $set, $state, callable $get) {
							$balance = CompanyDepositSummary::where('bank_id', $state)->first()->balance ?? 0;
							$set('balance', $balance);
						}),

					DatePicker::make('order_date')
						->columnSpan([
							'default' => 8,
							'lg' => 2,
						])
						->default(today())
						->timezone('Asia/Jakarta')
						->required()
						->native(false),

					Placeholder::make('warning')
						->hiddenLabel()
						->hidden()
						->columnSpanFull()
						->reactive()
						->content(fn($get) => 'Balance available: IDR ' . number_format($get('balance'), 2, ',', '.'))
						->visible(fn($get) => $get('bank_id'))
						->extraAttributes(fn($get) => [
							'class' => ($get('balance') <= 0
								? 'text-danger-500'
								: 'text-primary-500'
							) . ' mb-3 mt-3 text-center uppercase border rounded border-gray-200 p-4 font-bold',
						]),
					Fieldset::make('Order')
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						])
						// ->columnStart([
						// 	'default'=>1,
						// 	'lg'=>3,
						// ])
						->schema([
							Select::make('currency_id')
								->searchable()
								->label('Currency')
								->inlineLabel()
								->columnSpanFull()
								->required()
								->placeholder(null)
								->live(onBlur: true)
								->options(
									Currency::get()
										->mapWithKeys(fn($currency) => [
											$currency->id => "{$currency->symbol} - {$currency->name}"
										])
										->toArray()
								),

							TextInput::make('order_amount')
								->numeric()
								->inlineLabel()
								->columnSpanFull()
								->required()
								->live(onBlur: true)
								->extraInputAttributes(['class' => 'text-end'])
								->prefix(fn($get) => $get('currency_id') ? (Currency::find($get('currency_id'))?->symbol ?? '') : '')
								->afterStateUpdated(function (callable $set, $state, callable $get) {
									// if($state >= 100000) {
									// 	$set('booking_fee', 100);
									// } elseif($state >= 50000) {
									// 	$set('booking_fee', 50);
									// } else {
									// 	$set('booking_fee', 30);
									// }
									// $set('total', $state + $get('booking_fee'));
									$set('total', $state);
								}),

							TextInput::make('booking_fee')
								->numeric()
								->inlineLabel()
								->prefix(fn($get) => $get('currency_id') ? (Currency::find($get('currency_id'))?->symbol ?? '') : '')
								->columnSpanFull()
								->live(onBlur: true)
								->extraInputAttributes(['class' => 'text-end'])
								->afterStateUpdated(function (callable $set, $state, callable $get) {
									// $set('total', $state + $get('order_amount'));
								}),

							Placeholder::make('totalView')
								->label('Total Order')
								->reactive()
								->content(function ($get) {
									$currency = $get('currency_id');
									if ($currency) {
										$currency = Currency::find($currency);
									}
									$symbol = $currency?->symbol ?? '';
									$total = number_format($get('total') ?? 0, 2, ',', '.');
									$totalIdr = number_format(($get('total') ?? 0) * ($get('rates') ?? 0), 2, ',', '.');
									return new HtmlString("<div>{$symbol} {$total}</div>");
								})
								->inlineLabel()
								->extraAttributes(['class' => 'text-end font-bold'])
								->columnSpanFull(),
						]),
					Fieldset::make('Bill')
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						])
						->schema([
							TextInput::make('rates')
								->numeric()
								->label('Bank Rates')
								->inlineLabel()
								->columnSpanFull()
								->prefix('IDR')
								->live(onBlur: true)
								->extraInputAttributes(['class' => 'text-end'])
								->afterStateUpdated(function ($get, $set, $state) {
										$selRates = $state + 100;
										$set('sell_rates', $selRates);
								}),
							TextInput::make('sell_rates')
								->numeric()
								->label('Sell Rates')
								->inlineLabel()
								->columnSpanFull()
								->prefix('IDR')
								->live(onBlur: true)
								->extraInputAttributes(['class' => 'text-end']),
							TextInput::make('charges')
								->numeric()
								->inlineLabel()
								->columnSpanFull()
								->prefix('IDR')
								->live(onBlur: true)
								->extraInputAttributes(['class' => 'text-end']),

							Placeholder::make('totalBill')
								->label('Total Bill')
								->reactive()
								->content(function ($get) {
									$orderAfterRate = (($get('total') ?? 0) * ($get('sell_rates') ?? 0));

									$totalIdr = number_format($orderAfterRate + ($get('charges') ?? 0), 2, ',', '.');
									return new HtmlString("<div>IDR {$totalIdr}</div>");
								})
								->inlineLabel()
								->extraAttributes(['class' => 'text-end font-bold'])
								->columnSpanFull(),
						]),

				])->columns(8)
		];
	}
}
