<x-admin-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('User Details') }}: {{ $user->name }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex flex-col md:flex-row">
                        <div class="md:w-1/3 flex justify-center mb-6 md:mb-0">
                            <div class="flex flex-col items-center">
                                <img class="h-32 w-32 rounded-full object-cover" src="{{ $user->profile_photo_url }}" alt="{{ $user->name }}">
                                <h3 class="mt-4 text-lg font-medium text-gray-900">{{ $user->name }}</h3>
                                <div class="mt-1 flex space-x-1">
                                    @foreach ($user->roles as $role)
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            {{ $role->name }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="md:w-2/3">
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">{{ __('User Information') }}</h4>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500">{{ __('Name') }}</p>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->name }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm font-medium text-gray-500">{{ __('Email') }}</p>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->email }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm font-medium text-gray-500">{{ __('Email Verified') }}</p>
                                        <p class="mt-1 text-sm text-gray-900">
                                            @if ($user->email_verified_at)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    {{ __('Verified') }} ({{ $user->email_verified_at->format('M d, Y') }})
                                                </span>
                                            @else
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    {{ __('Not Verified') }}
                                                </span>
                                            @endif
                                        </p>
                                    </div>

                                    <div>
                                        <p class="text-sm font-medium text-gray-500">{{ __('Created At') }}</p>
                                        <p class="mt-1 text-sm text-gray-900">{{ $user->created_at->format('M d, Y H:i') }}</p>
                                    </div>
                                </div>

                                <h4 class="text-lg font-medium text-gray-900 mt-6 mb-4">{{ __('Roles & Permissions') }}</h4>

                                <div class="mb-4">
                                    <p class="text-sm font-medium text-gray-500">{{ __('Roles') }}</p>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        @forelse ($user->roles as $role)
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                {{ $role->name }}
                                            </span>
                                        @empty
                                            <span class="text-sm text-gray-500">{{ __('No roles assigned') }}</span>
                                        @endforelse
                                    </div>
                                </div>

                                <div>
                                    <p class="text-sm font-medium text-gray-500">{{ __('Permissions') }}</p>
                                    <div class="mt-2 flex flex-wrap gap-2">
                                        @forelse ($user->getAllPermissions() as $permission)
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                                {{ $permission->name }}
                                            </span>
                                        @empty
                                            <span class="text-sm text-gray-500">{{ __('No direct permissions assigned') }}</span>
                                        @endforelse
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6 flex justify-end space-x-3">
                                <a href="{{ route('management.users.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 active:bg-gray-500 focus:outline-none focus:border-gray-500 focus:shadow-outline-gray disabled:opacity-25 transition ease-in-out duration-150">
                                    {{ __('Back to List') }}
                                </a>
                                <a href="{{ route('management.users.edit', $user) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    {{ __('Edit User') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
