<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetClientId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:set-client-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        DB::transaction(function () {
            // Update semua invoice yang customer_id tidak null
            $updated = DB::table('invoices')
                ->whereNotNull('customer_id')
                ->update([
                    'client_id' => DB::raw('customer_id')
                ]);

            $this->info("Berhasil mengupdate $updated baris invoice.");
        });

        return Command::SUCCESS;
    }
}
