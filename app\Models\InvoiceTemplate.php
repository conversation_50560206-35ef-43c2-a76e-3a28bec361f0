<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceTemplate extends Model
{
	use HasFactory, SoftDeletes;

	protected $casts = [
		'layout_config' => 'array',
	];

	protected $fillable = [
		'company_id',
		'font_id',
		'layout_config',
		'header_style',
		'line_style',
		'billto_style',
		'body_style',
		'table_style',
		'inword_style',
		'footer_style',
		'bankinfo_style',
		'remarks_style',

	];

	public function company()
	{
		return $this->belongsTo(Company::class, 'company_id', 'id');
	}
	public function font()
	{
		return $this->hasOne(Font::class, 'id', 'font_id');
	}
}
