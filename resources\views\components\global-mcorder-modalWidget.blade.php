<div class='space-y-3'>
    <div
        class='flex justify-between items-center gap-2 px-4 py-2 bg-gray-50 dark:bg-transparent border dark:border-gray-600 rounded'>
        <div class='flex flex-col'>
            <span class='text-xs'>Customer</span>
            <span class='font-bold text-lg'>{{ $record->customer?->customer_code }} | {{ $record->customer?->name }}</span>
        </div>
        <div class='flex flex-col text-center'>
            <span class='text-xs'>Order Date</span>
            <span class='font-bold text-lg'>{{ $orderDate }}</span>
        </div>
        <div class='flex flex-col text-end space-y-1'>
            <span class='text-xs'>Product</span>
            <x-filament::badge color='warning'>
                {{ $record->currency?->name }}
            </x-filament::badge>
        </div>
    </div>
    <table class='w-full mb-6 text-sm'>
		<thead class="border">
			<th class="p-1">Product</th>
			<th class="border-s border-e">Order Amount</th>
			<th>Rate</th>
			<th class="border-s border-e">Charges</th>
			<th>Sub Total</th>
		</thead>
		<tbody>
			<tr class="border">
				<td class="text-center">{{ $record->currency?->symbol }}</td>
				<td class="border-s border-e text-end pe-2">{{ $amount }}</td>
				<td class="text-end pe-2">IDR {{ $rates }}</td>
				<td class="border-s border-e text-end pe-2">IDR {{ $charges }}</td>
				<td class="text-end pe-2">IDR {{ $totalOrder }}</td>
			</tr>
		</tbody>
    </table>
</div>
