<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	/**
	 * Run the migrations.
	 */
	public function up(): void
	{
		Schema::table('invoices', function (Blueprint $table) {
			$table->unsignedBigInteger('order_id')->nullable()->after('id');
			$table->unsignedBigInteger('parent_invoice_id')->nullable()->after('order_id');
			$table->unsignedBigInteger('client_id')->nullable()->after('company_id');
			$table->unsignedBigInteger('invoice_create_by')->nullable()->after('client_id');

			$table->decimal('order_amount', 20, 2)->nullable()->after('invoice_amount');
			$table->decimal('booking_fee', 20, 2)->nullable()->after('order_amount');

			$table->json('bank_custom_columns')->nullable()->after('IFSC');

			$table->unsignedBigInteger('verified_by')->nullable()->after('replicated_by');
			$table->unsignedBigInteger('supervised_by')->nullable()->after('verified_by');
			$table->unsignedBigInteger('approved_by')->nullable()->after('supervised_by');

			$table->string('verification_status')->nullable()->after('deleted_at');
			$table->string('supervision_status')->nullable()->after('verification_status');
			$table->string('approval_status')->nullable()->after('supervision_status');
		});
	}


	/**
	 * Reverse the migrations.
	 */
	public function down(): void
	{
		Schema::table('invoices', function (Blueprint $table) {
			//
		});
	}
};
