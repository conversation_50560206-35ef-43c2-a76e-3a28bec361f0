<?php

namespace App\Filament\Admin\Widgets;

use App\Models\CompanyBank;
use App\Models\CompanyDepositSummary;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class BankUtilizationWidget extends BaseWidget
{
    protected static bool $isDiscovered = false; // Hide widget
    protected static ?string $heading = 'Bank Utilization';
    protected static ?string $pollingInterval = '60s';
    protected int | string | array $columnSpan = 1;
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
    protected function getTableQuery(): Builder
    {
        // Get current month transaction count for each bank
        $currentMonth = Carbon::now()->format('Y-m');

        return CompanyBank::query()
            ->join('companies', 'company_banks.company_id', '=', 'companies.id')
            ->leftJoin('view_company_deposit_summary', 'company_banks.id', '=', 'view_company_deposit_summary.bank_id')
            ->leftJoin(
                DB::raw("(
                    SELECT bank_id, COUNT(*) as monthly_transactions
                    FROM company_depositos
                    WHERE deleted_at IS NULL
                    AND DATE_FORMAT(trx_date, '%Y-%m') = '{$currentMonth}'
                    GROUP BY bank_id
                ) as monthly_stats"),
                'company_banks.id', '=', 'monthly_stats.bank_id'
            )
            ->where('companies.type', 2) // Only internal companies
            ->select([
                'company_banks.id',
                'company_banks.bank_name',
                'company_banks.bank_acc_no',
                'companies.name as company_name',
                'view_company_deposit_summary.balance',
                'monthly_stats.monthly_transactions'
            ])
            ->orderByDesc('view_company_deposit_summary.balance');
    }

    public function getTableRecordKey($record): string
    {
        return (string) $record->id;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('bank_name')
                    ->label('Bank Name')
                    ->weight('bold')
                    ->searchable(false),
                TextColumn::make('company_name')
                    ->label('Company')
                    ->searchable(false),
                TextColumn::make('balance')
                    ->label('Balance')
                    ->money('IDR')
                    ->color(function ($state) {
						$balance = $state ?? 0;
						if ($balance >= 5000000) return 'success';     // >= 5M
						if ($balance >= 1000000) return 'warning';     // >= 1M
						return 'danger';                               // < 1M
					})
                    ->weight('bold')
                    ->default(0),
                TextColumn::make('monthly_transactions')
                    ->label('Transactions')
                    ->suffix(' this month')
                    ->default(0)
                    ->color('info'),
            ])
            ->defaultSort('balance', 'desc')
            ->paginated(false)
            ->striped()
            ->emptyStateHeading('No Bank Data')
            ->emptyStateDescription('No banks found or no transaction data available.');
    }
}
