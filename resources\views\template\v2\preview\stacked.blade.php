@php
    $sizeClasses = [
        'h1' => 'text-4xl',     // besar
        'h2' => 'text-3xl',
        'h3' => 'text-2xl',
        'h4' => 'text-xl',
        'h5' => 'text-lg',
        'h6' => 'text-base',    // kecil
    ];
@endphp

<div class="bg-white shadow p-4 rounded-2xl border rounded border-gray-300 mb-2">
    <div class="flex flex-col items-center text-center space-y-2">
		@if($show_logo)
        	<img src="{{ asset('images/logolight.png') }}" alt="Logo" class="w-16 h-auto">
		@endif
        <span class="{{ $sizeClasses[$heading_size] ?? 'text-base' }} mt-2"  style="color: {{ $heading_color }}; font-weight: {{ $heading_weight }};">
            Company Name
		</span>

		@if($show_address)
			<ul class="text-xs text-gray-700 space-y-0 leading-snug">
				<li>Company Address. Company Phone - Company Email</li>
			</ul>
		@endif
    </div>
</div>
<div class="text-center text-xs">
	<span></span>this layout suitable for company with long logo image
</div>
