<?php

namespace App\Filament\Admin\Resources\CompanyTransactionResource\Widgets;

use App\Models\Company;
use App\Models\CompanyDeposito;
use App\Models\CompanyTransaction;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Widget extends BaseWidget
{
	protected int | string | array $columnSpan = 'full';

	protected static string $view = 'filament.admin.resources.deposit-resource.pages.company-transaction-details';


	public CompanyTransaction $record;

	public function mount(CompanyTransaction $record): void
	{
		$this->record = $record;
	}

	public function getViewData(): array
	{
		$companyName = $this->record->name;
		$companyId = $this->record->id;
		$now       = now('Asia/Jakarta');
		$today     = $now->toDateString();
		$yesterday = $now->copy()->subDay()->toDateString();
		$base = CompanyDeposito::query()->where('company_id', $companyId);
		$previous = (clone $base)
			->whereDate('trx_date', '<=', $yesterday)
			->selectRaw("
            COALESCE(SUM(CASE WHEN trx_type = 'in'    THEN amount END), 0)   AS in_sum,
            COALESCE(SUM(CASE WHEN trx_type = 'out'   THEN amount END), 0)   AS out_sum,
            COALESCE(SUM(CASE WHEN trx_type = 'order' THEN amount END), 0)   AS order_sum
        ")
			->first();

		$todayAgg = (clone $base)
			->whereDate('trx_date', '=', $today)
			->selectRaw("
            COALESCE(SUM(CASE WHEN trx_type = 'in'    THEN amount END), 0)   AS in_sum,
            COALESCE(SUM(CASE WHEN trx_type = 'out'   THEN amount END), 0)   AS out_sum,
            COALESCE(SUM(CASE WHEN trx_type = 'order' THEN amount END), 0)   AS order_sum,

            COALESCE(SUM(CASE WHEN trx_type = 'in'    THEN 1 ELSE 0 END), 0) AS in_count,
            COALESCE(SUM(CASE WHEN trx_type = 'out'   THEN 1 ELSE 0 END), 0) AS out_count,
            COALESCE(SUM(CASE WHEN trx_type = 'order' THEN 1 ELSE 0 END), 0) AS order_count
        ")
			->first();

		$totalPreviousIn    = (float) $previous->in_sum;
		$totalPreviousOut   = (float) $previous->out_sum;
		$totalPreviousOrder = (float) $previous->order_sum;

		$previousBalance = $totalPreviousIn - ($totalPreviousOut + $totalPreviousOrder);

		$todayIncoming        = (float) $todayAgg->in_sum;
		$todayOutgoing        = (float) $todayAgg->out_sum;
		$todayOrder           = (float) $todayAgg->order_sum;
		$todayIncomingCount   = (int) $todayAgg->in_count;
		$todayOutgoingCount   = (int) $todayAgg->out_count;
		$todayOrderCount      = (int) $todayAgg->order_count;

		$availableBalance = $previousBalance + $todayIncoming - ($todayOutgoing + $todayOrder);
		$bankAgg = CompanyDeposito::query()
			->selectRaw("
        cb.bank_name,
        cb.bank_acc_no,
        cb.bank_acc_name,
        company_depositos.bank_id,

        -- saldo sebelum hari ini
        COALESCE(SUM(CASE WHEN company_depositos.trx_date <= ? AND company_depositos.trx_type = 'in'
            THEN company_depositos.amount END), 0)
            - COALESCE(SUM(CASE WHEN company_depositos.trx_date <= ? AND company_depositos.trx_type = 'out'
            THEN company_depositos.amount END), 0)
            - COALESCE(SUM(CASE WHEN company_depositos.trx_date <= ? AND company_depositos.trx_type = 'order'
            THEN company_depositos.amount END), 0)
        AS previous_balance,

        -- hari ini
        COALESCE(SUM(CASE WHEN company_depositos.trx_date = ? AND company_depositos.trx_type = 'in'
            THEN company_depositos.amount END), 0) AS today_in,

        COALESCE(SUM(CASE WHEN company_depositos.trx_date = ? AND company_depositos.trx_type = 'out'
            THEN company_depositos.amount END), 0) AS today_out,

        COALESCE(SUM(CASE WHEN company_depositos.trx_date = ? AND company_depositos.trx_type = 'order'
            THEN company_depositos.amount END), 0) AS today_order
    ", [
				$yesterday,
				$yesterday,
				$yesterday,
				$today,
				$today,
				$today
			])
			->join('company_banks as cb', 'company_depositos.bank_id', '=', 'cb.id')
			->where('company_depositos.company_id', $companyId)
			->whereNull('company_depositos.deleted_at')
			->groupBy(
				'company_depositos.bank_id',
				'cb.bank_name',
				'cb.bank_acc_no',
				'cb.bank_acc_name'
			)
			->get()
			->map(function ($row) {
				$row->available_balance =
					$row->previous_balance
					+ $row->today_in
					- $row->today_out
					- $row->today_order;
				return $row;
			});

		return [
			'companyName'         => $companyName,
			'todayIncoming'       => $todayIncoming,
			'todayOutgoing'       => $todayOutgoing,
			'todayOrder'          => $todayOrder,
			'todayIncomingCount'  => $todayIncomingCount,
			'todayOutgoingCount'  => $todayOutgoingCount,
			'todayOrderCount'     => $todayOrderCount,
			'previousBalance'     => $previousBalance,
			'availableBalance'    => $availableBalance,
			'bankAggregates'      => $bankAgg,
		];
	}
}
