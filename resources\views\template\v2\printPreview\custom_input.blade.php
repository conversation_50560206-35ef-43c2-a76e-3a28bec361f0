@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$invoice = $payload['invoice'];
	$company = $payload['invoice']->company;

	// Style configuration (similar to remark)
	$customInputAlign = $layoutConfig['customInputAlignment'] ?? 'justify-start';
	$customInputDecor = $layoutConfig['customInputDecor'] ?? 'not-italic';
	$customInputWeight = $layoutConfig['customInputWeight'] ?? 'font-normal';
	$customInputContent = $layoutConfig['customInputContent'] ?? '';

@endphp

{{-- Custom Input Section --}}
<div class="flex {{ $customInputAlign }}">
	<div class="p-2">
			<div class="custom-input-item">
				<div class="{{ $customInputDecor }} {{ $customInputWeight }}">
					{{ $customInputContent }}
				</div>
			</div>
	</div>
</div>
