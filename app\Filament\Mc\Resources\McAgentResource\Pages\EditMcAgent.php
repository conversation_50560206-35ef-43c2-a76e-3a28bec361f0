<?php

namespace App\Filament\Mc\Resources\McAgentResource\Pages;

use App\Filament\Mc\Resources\McAgentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Support\Enums\Alignment;

class EditMcAgent extends EditRecord
{
    protected static string $resource = McAgentResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
