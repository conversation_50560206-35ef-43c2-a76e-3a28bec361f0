<?php

namespace App\Filament\Admin\Resources\MasterInvoiceDetailResource\Pages;

use App\Filament\Admin\Resources\MasterInvoiceDetailResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMasterInvoiceDetails extends ListRecords
{
    protected static string $resource = MasterInvoiceDetailResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
