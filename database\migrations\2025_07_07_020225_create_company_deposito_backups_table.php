<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_deposito_backups', function (Blueprint $table) {
            $table->id();
			$table->unsignedBigInteger('original_id')->nullable()->comment('ID dari record asli yang dibackup');
            $table->unsignedBigInteger('company_id')->nullable();
            $table->unsignedBigInteger('order_id')->nullable()->comment('if trx_type is order');
            $table->unsignedBigInteger('bank_id')->nullable()->comment('for bank transaction tracking');
            $table->string('description')->nullable();
            $table->enum('trx_type', ['in', 'out', 'order'])->nullable();
            $table->date('trx_date')->nullable();
            $table->decimal('amount', 18, 2)->nullable();
            $table->unsignedBigInteger('backup_by')->nullable();
            $table->timestamp('backed_up_at')->nullable(); // waktu backup
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
