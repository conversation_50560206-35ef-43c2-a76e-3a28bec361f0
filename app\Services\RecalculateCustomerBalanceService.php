<?php

namespace App\Services;

use App\Models\McCustomer;
use App\Models\McDeposit;
use Illuminate\Support\Facades\DB;

class RecalculateCustomerBalanceService
{
	public static function run(array|int $customerIds): void
	{
		$customerIds = is_array($customerIds) ? $customerIds : [$customerIds];

		DB::beginTransaction();

		try {
			foreach ($customerIds as $customerId) {
				$totalOut = McDeposit::where('mc_customer_id', $customerId)
					->where(function ($query) {
						$query->where('trx_type', 'like', '%out%')
							->orWhere('trx_type', 'like', '%order%');
					})
					->sum('amount');

				$totalIn = McDeposit::where('mc_customer_id', $customerId)
					->where('trx_type', 'like', '%incoming%')
					->sum('amount');

				McCustomer::where('id', $customerId)->update([
					'total_orders'   => $totalOut,
					'total_deposits' => $totalIn,
					'balance'        => $totalIn - $totalOut,
				]);
			}

			DB::commit();
		} catch (\Throwable $e) {
			DB::rollBack();
			throw $e;
		}
	}
}
