<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class CompanyDeposito extends Model
{
	use SoftDeletes;

	protected $table = 'company_depositos';

	protected $fillable = [
		'trx_no',
		'company_id',
		'order_id',
		'bank_id',
		'description',
		'trx_date',
		'trx_type',
		'amount',
	];
	protected $casts = [
		'trx_date' => 'date', // atau 'datetime' jika ada waktu juga
	];


	protected static function boot()
	{
		parent::boot();

		static::creating(function ($deposit) {
			$monthMapping = [
				'01' => 'A',
				'02' => 'B',
				'03' => 'C',
				'04' => 'D',
				'05' => 'E',
				'06' => 'F',
				'07' => 'G',
				'08' => 'H',
				'09' => 'I',
				'10' => 'J',
				'11' => 'K',
				'12' => 'L',
			];
			$tc = time();
			$currentMonth = now()->format('m');
			$mappedMonth = $monthMapping[$currentMonth];
			$yearNumber = now()->format('y');
			$userId = Auth::user()->id;

			$prefix = "TRX{$yearNumber}{$mappedMonth}-{$tc}U{$userId}-";
			$lastCode = CompanyDeposito::where('trx_no', 'like', "{$prefix}%")
				->orderByDesc('trx_no')
				->value('trx_no');

			// Ambil 5 digit terakhir dari kode sebelumnya
			$lastNumber = 0;
			if ($lastCode && preg_match('/(\d{5})$/', $lastCode, $matches)) {
				$lastNumber = (int) $matches[1];
			}

			// Increment dan format jadi 5 digit
			$nextNumber = str_pad($lastNumber + 1, 5, '0', STR_PAD_LEFT);

			// Gabungkan jadi order_code baru
			$deposit->trx_no = "{$prefix}{$nextNumber}";
		});
	}

	public function company()
	{
		return $this->belongsTo(Company::class, 'company_id', 'id')->where('type', 2);
	}

	public function order()
	{
		return $this->belongsTo(Order::class, 'order_id', 'id');
	}

	public function bank()
	{
		return $this->belongsTo(CompanyBank::class, 'bank_id', 'id');
	}

	/**
	 * Check if this deposito can be deleted manually
	 */
	public function canBeDeleted(): bool
	{
		return is_null($this->order_id);
	}

	/**
	 * Get deletion prevention message
	 */
	public function getDeletionPreventionMessage(): string
	{
		if ($this->canBeDeleted()) {
			return '';
		}

		$orderNo = $this->order ? $this->order->order_no : $this->order_id;
		return "Deposito ini terkait dengan Order #{$orderNo}. Untuk menghapus deposito, hapus atau edit Order terkait.";
	}
}
