<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('company_banks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->nullable();
            $table->string('bank_acc_name')->nullable();
            $table->string('bank_code')->nullable();
            $table->string('bank_acc_no')->nullable();
            $table->string('bank_acc_address')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('bank_address')->nullable();
            $table->string('bank_correspondent')->nullable();
            $table->string('swift')->nullable();
            $table->string('swift_correspondent')->nullable();
            $table->string('routing_no')->nullable();
            $table->string('transit')->nullable();
            $table->string('tt_charge')->nullable();
            $table->string('institution')->nullable();
            $table->string('iban')->nullable();
            $table->string('bsb')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('company_banks');
    }
};
