<?php

namespace App\Filament\Admin\Resources\CompanyDepositoResource\Pages;

use App\Filament\Admin\Resources\CompanyDepositoResource;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class InternalTransactionManagement extends Page
{
    protected static string $resource = CompanyDepositoResource::class;
    protected static string $view = 'filament.admin.resources.deposit-resource.pages.deposit-management';
    protected static ?string $title = 'Internal Transaction Management';

    // public function mount(): mixed
    // {
    //     if (Auth::check() && Auth::user()->hasRole('Staff Invoice')) {
    //         return redirect(CompanyDepositoResource::getUrl('index'));
    //     }
    //     return null;
    // }
}
