<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class CompanyDepositSummaryWidget extends BaseWidget
{
	protected static ?string $heading = 'Bank Balance Summary';
	protected static ?string $pollingInterval = '60s';
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	protected int | string | array $columnSpan = 'full';

	public array $selectedCompanyIds = [];
    public function getColumns(): int | string | array
    {
        return 3;
    }

	protected function getTableQuery(): Builder
	{
		// Use CompanyBank as base model to avoid primary key issues
		$query = \App\Models\CompanyBank::query()
			->join('companies', 'company_banks.company_id', '=', 'companies.id')
			->leftJoin('view_company_deposit_summary', 'company_banks.id', '=', 'view_company_deposit_summary.bank_id')
			->where('companies.type', 2)
			->whereRaw("TRIM(companies.name) LIKE ?", ['PT%'])
			->select([
				'company_banks.id', // Primary key from CompanyBank
				'company_banks.bank_name',
				'company_banks.bank_acc_no',
				'company_banks.bank_acc_name',
				'companies.name as company_name',
				'companies.id as company_id',
				'view_company_deposit_summary.total_in',
				'view_company_deposit_summary.total_out',
				'view_company_deposit_summary.total_order',
				'view_company_deposit_summary.balance'
			]);

		// Only show data if companies are selected
		if (!empty($this->selectedCompanyIds)) {
			$query->whereIn('companies.id', $this->selectedCompanyIds);
		} else {
			// Return empty result if no companies selected
			$query->whereRaw('1 = 0');
		}

		return $query;
	}

	public function getTableRecordKey($record): string
	{
		return (string) $record->id; // CompanyBank ID
	}

	public function table(Table $table): Table
	{
		return $table
			->query($this->getTableQuery())
			->columns([
				TextColumn::make('company_name')
					->label('Company')
					->sortable(),
				TextColumn::make('bank_name')
					->label('Bank Name')
					->sortable(),
				TextColumn::make('bank_acc_no')
					->label('Account No'),
				TextColumn::make('balance')
					->label('Balance')
					->money('IDR')
					->sortable()
					->color(fn ($state) => ($state ?? 0) >= 0 ? 'success' : 'danger')
					->weight('bold')
					->default(0),
			])
			->filters([
				//
			])
			->actions([
				\Filament\Tables\Actions\Action::make('view_details')
					->label('Details')
					->icon('heroicon-o-eye')
					->iconButton()
					->tooltip('View Transaction Details')
					->modalHeading(fn ($record) => "Summary")
					->modalContent(function ($record) {
						$viewPath = "components.company-deposits.deposits-modal";
						$record = $record;
						$bank = $record->bank_name;
						$company = $record->company->name;
						$companyLogo = $record->company->logo;
						$accountNo = $record->bank_acc_no;
						$accountName = $record->bank_acc_name;
						$totalIn = number_format($record->total_in ?? 0, 2, ',', '.');
						$totalOut = number_format($record->total_out ?? 0, 2, ',', '.');
						$totalOrder = number_format($record->total_order ?? 0, 2, ',', '.');
						$balance = number_format($record->balance ?? 0, 2, ',', '.');
						$balanceColor = ($record->balance ?? 0) >= 0 ? 'bg-green-50' : 'bg-red-50';
						$numberSpell = $record->balance ? Number::spell($record->balance, 'id') . ' rupiah' : null;
						// Kirim data warna ke view
						return new HtmlString(view($viewPath, [
							'record' => $record,
							'company' => $company,
							'companyLogo' => $companyLogo,
							'bank' => $bank,
							'accountNo' => $accountNo,
							'accountName' => $accountName,
							'totalIn' => $totalIn,
							'totalOut' => $totalOut,
							'totalOrder' => $totalOrder,
							'balance' => $balance,
							'numberSpell' => $numberSpell,
							'balanceColor' => $balanceColor
						])->render());
					})
					->modalWidth('lg')
					->modalSubmitAction(false)
					->modalCancelActionLabel('Close'),
			])
			->headerActions([
				\Filament\Tables\Actions\Action::make('filter_company')
					->label('Filter by Company')
					->icon('heroicon-o-funnel')
					->form([
						Select::make('company_ids')
							->label('Select Companies')
							->options(Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id'))
							->placeholder('Select companies to view...')
							->multiple()
							->searchable()
							->required()
					])
					->action(function (array $data) {
						$this->selectedCompanyIds = $data['company_ids'] ?? [];
					})
					->modalHeading('Filter by Companies')
					->modalSubmitActionLabel('Apply Filter'),
				\Filament\Tables\Actions\Action::make('clear_filter')
					->label('Clear Filter')
					->icon('heroicon-o-x-mark')
					->color('gray')
					->action(function () {
						$this->selectedCompanyIds = [];
					})
					->visible(fn () => !empty($this->selectedCompanyIds)),
			])
			->defaultSort('balance', 'desc')
			->striped()
			->paginated(false) // Show all records without pagination for summary
			->emptyStateHeading('No Companies Selected')
			->emptyStateDescription('Please select companies from the filter to view bank balance summary.');
	}

	protected function getTableHeading(): string
	{
		return "Bank Balance Summary";
	}
}
