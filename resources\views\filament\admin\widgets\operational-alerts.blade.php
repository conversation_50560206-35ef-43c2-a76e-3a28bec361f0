<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            Operational Alerts
        </x-slot>

        <div class="space-y-3">
            {{-- Low Balance Banks Alert --}}
            @if($lowBalanceBanks->count() > 0)
                <div class="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 flex-1">
                        <h3 class="text-sm font-medium text-red-800">
                            Low Balance Warning
                        </h3>
                        <div class="mt-1 text-sm text-red-700">
                            {{ $lowBalanceBanks->count() }} bank(s) below IDR {{ number_format($lowBalanceThreshold, 0, ',', '.') }}
                        </div>
                        <div class="mt-2">
                            @foreach($lowBalanceBanks->take(3) as $bank)
                                <div class="text-xs text-red-600">
                                    {{ $bank->bank_name }}: IDR {{ number_format($bank->balance, 0, ',', '.') }}
                                </div>
                            @endforeach
                            @if($lowBalanceBanks->count() > 3)
                                <div class="text-xs text-red-500 mt-1">
                                    and {{ $lowBalanceBanks->count() - 3 }} more...
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            {{-- Zero Balance Banks Alert --}}
            @if($zeroBalanceBanks > 0)
                <div class="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Zero Balance Alert
                        </h3>
                        <div class="mt-1 text-sm text-red-700">
                            {{ $zeroBalanceBanks }} bank(s) with zero or negative balance
                        </div>
                    </div>
                </div>
            @endif

            {{-- Stale Orders Alert --}}
            @if($staleOrders > 0)
                <div class="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Stale Orders
                        </h3>
                        <div class="mt-1 text-sm text-yellow-700">
                            {{ $staleOrders }} draft order(s) older than 7 days
                        </div>
                    </div>
                </div>
            @endif

            {{-- High Activity Alert --}}
            @if($todayTransactions > 10)
                <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            High Activity
                        </h3>
                        <div class="mt-1 text-sm text-blue-700">
                            {{ $todayTransactions }} transactions today (above normal)
                        </div>
                    </div>
                </div>
            @endif

            {{-- No Alerts --}}
            @if($lowBalanceBanks->count() == 0 && $staleOrders == 0 && $todayTransactions <= 10 && $zeroBalanceBanks == 0)
                <div class="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800">
                            All Systems Normal
                        </h3>
                        <div class="mt-1 text-sm text-green-700">
                            No operational alerts at this time
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
