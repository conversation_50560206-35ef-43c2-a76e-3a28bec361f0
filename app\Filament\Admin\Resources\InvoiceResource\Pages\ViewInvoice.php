<?php

namespace App\Filament\Admin\Resources\InvoiceResource\Pages;

use App\Filament\Admin\Resources\InvoiceResource;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\MasterInvoiceDetail;
use Filament\Actions;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Select;
use Filament\Infolists\Components\Fieldset;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Grid;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class ViewInvoice extends ViewRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
			//Action back to listrecord (filament.admin.resources.invoices.index atau route('filament.admin.resources.invoices.index'))
            Actions\Action::make('back')
                ->label('Back')
				->icon('heroicon-o-arrow-left')
				->color('gray')
                ->url(route('filament.admin.resources.invoices.index')),
            Actions\EditAction::make(),
			ActionGroup::make([
				Actions\Action::make('Issue')
					->label('Forward to Next Company')
					->icon('heroicon-o-forward')
					->tooltip('Create invoice for next company in the chain')
					->color('primary')
					->requiresConfirmation()
					->modalHeading('Forward to Next Company')
					->modalDescription(function ($record) {
						// Calculate current chain position
						$depth = 0;
						$current = $record;
						while ($current->parent_invoice_id) {
							$depth++;
							$current = $current->parentInvoice;
						}
						$currentLevel = $depth + 1;
						$nextLevel = $currentLevel + 1;

						return "Current Position: Chain Level {$currentLevel}\n" .
							   "This will create an invoice for the next company (Chain Level {$nextLevel}) and mark the current invoice as issued.";
					})
					->modalSubmitActionLabel('Yes, Forward')
					->visible(function ($record) {
						return $record->status === 'Draft' && !$record->parent_invoice_id;
					})
					->disabled(function ($record) {
						return is_null($record->invoice_no);
					})
					->form([
						// memilih perusahaan yang akan menjadi client di invoice berikutnya
						Select::make('client_id')
							->label('Next Company in Chain')
							->helperText('Select the company that will receive funds from the current company')
							->options(function ($record) {
								return Company::whereNotIn('id', [
									$record->company_id, // Hindari company asal
									$record->client_id,  // Hindari client yang sama (kalau sudah ada)
								])->pluck('name', 'id');
							})
							->searchable()
							->required(),
					])
					->action(function (Invoice $record, array $data) {
						try {
							$invoice = DB::transaction(function () use ($record, $data) {
								$record->update(['status' => 'Issued']);

								//di invoice baru, client yang dipilih akan menjadi client di invoice baru
								$client = Company::find($data['client_id']);

								//di invoice baru, client lama akan menjadi company di invoice baru
								$company = Company::find($record->client_id);

								// jika ingin menggunakan dari MasterDetail
								// $detail = MasterInvoiceDetail::where('business_type', $company->business_type)->get();

								// jika ingin menggunakan dari InvoiceDetail
								$detail = InvoiceDetail::where('invoice_id', $record->id)->get();

								$bank = CompanyBank::where('company_id', $company->id)->where('is_default', true)->first();
								if(!$bank)
								{
									$bank = CompanyBank::where('company_id', $company->id)->first();
								}

								$invoice = Invoice::create([
									'order_id' => $record->order_id,
									'client_id' => $client->id,
									'company_id' => $company->id,
									'booking_fee' => $record->order?->booking_fee,
									'rates' => $record->rates,
									'order_amount' => $record->order_amount,
									'invoice_amount' => $record->total,
									'status' => 'Draft',
									'invoice_date'=> $record->invoice_date,
									'due_date' => $record->due_date,
									'invoice_create_by' => Auth::user()->id,
									'currency_id' => $record->currency_id,
									//include client address (sesuai client address)
									'client_address' => $client->address,
									'parent_invoice_id' => $record->id,
									'prev_invoice' => $record->invoice_no,

									//dan bank information (sesuai company bank)
									'bank_acc_name' => $bank?->bank_acc_name,
									'bank_code' => $bank?->bank_code,
									'bank_acc_no' => $bank?->bank_acc_no,
									'bank_acc_address' => $bank?->bank_acc_address,
									'bank_name' => $bank?->bank_name,
									'bank_address' => $bank?->bank_address,
									'bank_correspondent' => $bank?->bank_correspondent,
									'swift' => $bank?->swift,
									'swift_correspondent' => $bank?->swift_correspondent,
									'routing_no' => $bank?->routing_no,
									'transit' => $bank?->transit,
									'tt_charge' => $bank?->tt_charge,
									'iban' => $bank?->iban,
									'institution' => $bank?->institution,
									'bsb' => $bank?->bsb,
									'branch_code' => $bank?->branch_code,
									'sort_code' => $bank?->sort_code,
									'branch_bank' => $bank?->branch_bank,
									'back2back' => $bank?->back2back,
									'ABA' => $bank?->ABA,
									'IFSC' => $bank?->IFSC,
									'bank_custom_columns' => $bank?->custom_columns,
								]);

								// Tambahkan detail invoice dari master invoice detail sesuai business type company yang mengeluarkan invoice
								if ($detail->isNotEmpty()) {
									foreach ($detail as $item) {
										$invoice->invoiceDetails()->create([
											'company_id' => $data['client_id'],
											'client_id' => $record->company_id,
											'description' => $item->description,
											'quantity' => $item->quantity,
											'unit' => $item->unit,
											'price' => $item->price,
											'sub_total' => $item->quantity * $item->price,
											'status' => 'Active',
										]);
									}

									// Hitung ulang total invoice
									$total = $invoice->invoiceDetails()->sum('sub_total');
									$invoice->invoice_amount = $total;
									$invoice->inv_sub_total = $total;
									$invoice->save();
								}

								return $invoice;
							});

							Notification::make()
								->title('Invoice forwarded successfully.')
								->body('Invoice has been forwarded to the next company in the chain.')
								->success()
								->send();

							// Redirect ke halaman edit invoice yang baru dibuat
							return redirect()->route('filament.admin.resources.invoices.edit', ['record' => $invoice->id]);
						} catch (\Throwable $e) {
							Notification::make()
								->title('Failed to forward invoice.')
								->body('Error: ' . $e->getMessage())
								->danger()
								->send();
							// Tidak redirect, tetap di halaman ini
						}
					}),

				Actions\Action::make('continue_chain')
					->label('Continue Chain')
					->icon('heroicon-o-forward')
					->tooltip('Continue to next company in the chain')
					->color(fn ($record) => $record->invoice_no ? 'success' : '')
					->requiresConfirmation()
					->modalHeading('Continue Transaction Chain')
					->modalDescription(function ($record) {
						// Calculate current chain position
						$depth = 1;
						$current = $record;
						while ($current->parent_invoice_id) {
							$depth++;
							$current = $current->parentInvoice;
						}
						$currentLevel = $depth;
						$nextLevel = $currentLevel + 1;

						return "Current Position: Chain Level {$currentLevel}\n" .
								"This will create an invoice for the next company (Chain Level {$nextLevel}) and mark the current invoice as issued.";
					})
					->modalSubmitActionLabel('Yes, Continue Chain')
					->visible(function ($record) {
						// Only show for Draft child invoices
						return $record->status === 'Draft' && $record->parent_invoice_id;
					})
					->disabled(function ($record) {
						return is_null($record->invoice_no); // true jika invoice_no masih kosong
					})
					->form([
						Select::make('client_id')
							->label('Next Company in Chain')
							->helperText('Select the company that will receive funds from the current company')
							->options(function ($record) {
								return \App\Models\Company::whereNotIn('id', [
									$record->company_id, // Hindari company asal
									$record->client_id,  // Hindari client yang sama (kalau sudah ada)
								])->pluck('name', 'id');
							})
							->searchable()
							->required(),
					])
					->action(function (Invoice $record, array $data) {
						try {
							$invoice = DB::transaction(function () use ($record, $data) {
								$record->update(['status' => 'Issued']);

								//di invoice baru, client yang dipilih akan menjadi client di invoice baru
								$client = Company::find($data['client_id']);

								//di invoice baru, client lama akan menjadi company di invoice baru
								$company = Company::find($record->client_id);

								//jika ingin menggunakan dari Master
								// $detail = MasterInvoiceDetail::where('business_type', $company->business_type)->get();

								//jika ingin menggunakan dari detail
								$detail = InvoiceDetail::where('invoice_id', $record->id)->get();

								$bank = CompanyBank::where('company_id', $company->id)->where('is_default', true)->first();
								if(!$bank)
								{
									$bank = CompanyBank::where('company_id', $company->id)->first();
								}
								$invoice = \App\Models\Invoice::create([
									'order_id' => $record->order_id,
									'client_id' => $client->id,
									'company_id' => $company->id,
									'booking_fee' => $record->booking_fee,
									'rates' => $record->rates,
									'order_amount' => $record->order_amount,
									'invoice_amount' => $record->total,
									'status' => 'Draft',
									'invoice_date'=> $record->invoice_date,
									'due_date' => $record->due_date,
									'invoice_create_by' => Auth::user()->id,
									'currency_id' => $record->currency_id,
									//include client address (sesuai client address)
									'client_address' => $client->address,
									'parent_invoice_id' => $record->id,
									'prev_invoice' => $record->invoice_no,

									//dan bank information (sesuai company bank)
									'bank_acc_name' => $bank->bank_acc_name,
									'bank_code' => $bank->bank_code,
									'bank_acc_no' => $bank->bank_acc_no,
									'bank_acc_address' => $bank->bank_acc_address,
									'bank_name' => $bank->bank_name,
									'bank_address' => $bank->bank_address,
									'bank_correspondent' => $bank->bank_correspondent,
									'swift' => $bank->swift,
									'swift_correspondent' => $bank->swift_correspondent,
									'routing_no' => $bank->routing_no,
									'transit' => $bank->transit,
									'tt_charge' => $bank->tt_charge,
									'iban' => $bank->iban,
									'institution' => $bank->institution,
									'bsb' => $bank->bsb,
									'branch_code' => $bank->branch_code,
									'sort_code' => $bank->sort_code,
									'branch_bank' => $bank->branch_bank,
									'back2back' => $bank->back2back,
									'ABA' => $bank->ABA,
									'IFSC' => $bank->IFSC,
									'bank_custom_columns' => $bank->custom_columns,
								]);

								// Tambahkan detail invoice dari master invoice detail sesuai business type company yang mengeluarkan invoice
								if ($detail->isNotEmpty()) {
									foreach ($detail as $item) {
										$invoice->invoiceDetails()->create([
											'company_id' => $data['client_id'],
											'client_id' => $record->company_id,
											'description' => $item->description,
											'quantity' => $item->quantity,
											'unit' => $item->unit,
											'price' => $item->price,
											'sub_total' => $item->quantity * $item->price,
											'status' => 'Active',
										]);
									}

									// Hitung ulang total invoice
									$total = $invoice->invoiceDetails()->sum('sub_total');
									$invoice->invoice_amount = $total;
									$invoice->inv_sub_total = $total;
									$invoice->save();
								}

								return $invoice;
							});

							Notification::make()
								->title('Invoice forwarded successfully.')
								->body('Invoice has been forwarded to the next company in the chain.')
								->success()
								->send();

							// Redirect ke halaman edit invoice yang baru dibuat
							return redirect()->route('filament.admin.resources.invoices.edit', ['record' => $invoice->id]);
						} catch (\Throwable $e) {
							Notification::make()
								->title('Failed to continue chain.')
								->body('Error: ' . $e->getMessage())
								->danger()
								->send();

							// Tidak redirect, tetap di halaman ini
						}
					}),

				Actions\Action::make('Close')
					->label('End Chain Here')
					->icon('heroicon-o-lock-closed')
					->tooltip('End the transaction chain at this company')
					->color(fn ($record) => $record->invoice_no ? 'success' : '')
					->requiresConfirmation()
					->modalHeading('End Transaction Chain')
					->modalDescription(function ($record) {
						// Calculate current chain position
						$depth = 1;
						$current = $record;
						while ($current->parent_invoice_id) {
							$depth++;
							$current = $current->parentInvoice;
						}

						return "Current Position: Chain Level {$depth}\n" .
								"This will end the transaction chain at this company and mark the invoice as closed. No further invoices will be created after this chain.";
					})
					->modalSubmitActionLabel('Yes, End Chain')
					->visible(function (Invoice $record) {
						return ($record->status === 'Draft');
					})
					->disabled(function ($record) {
						return is_null($record->invoice_no); // true jika invoice_no masih kosong
					})
					->action(function (Invoice $record) {
						try {

							// Refresh record dari database untuk memastikan data terbaru
							$record->refresh();

							// Update status
							$updated = $record->update(['status' => 'Closed']);

							if ($updated) {
								Notification::make()
									->title('Transaction chain ended successfully.')
									->body("Invoice #{$record->id} - Transaction chain has been ended at this company.")
									->success()
									->send();
							} else {
								throw new \Exception('Failed to update invoice status');
							}

						} catch (\Throwable $e) {
							Log::error('Failed to close invoice', [
								'invoice_id' => $record->id,
								'error' => $e->getMessage(),
								'trace' => $e->getTraceAsString()
							]);

							Notification::make()
								->title('Failed to end transaction chain.')
								->body('Error: ' . $e->getMessage())
								->danger()
								->send();
						}
					}),
				Actions\Action::make('printPreview')
					->label('Print Preview')
					->icon('heroicon-o-printer')
					// ->iconButton()
					->color('success')
					->tooltip('Print Preview')
					->url(fn (Invoice $record) => route('printInvoice', $record))
					->openUrlInNewTab(),
				Actions\Action::make('pdfPreview')
					->label('PDF Preview')
					->icon('icon-filetype-pdf')
					// ->iconButton()
					->color('warning')
					->tooltip('PDF Preview')
					->url(fn (Invoice $record) => route('printInvoicePdf', $record))
					->openUrlInNewTab(),
			])
			->label('More ...')
			->icon('heroicon-o-bars-3')
			->button(),
        ];
    }

	public function getHeading(): string
	{
        return 'Invoice Summary';
	}

    public function getSubheading(): string|Htmlable
    {
        return new HtmlString(view('components.invoice-quick-navigation', [
            'invoice' => $this->record,
            'mode' => 'view'
        ])->render());
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Transaction Chain Overview - Moved to top for context
                Section::make('Transaction Chain Overview')
                    ->schema([
                        ViewEntry::make('chain_visualization')
                            ->hiddenLabel()
                            ->view('components.invoice-chain-visualization', [
                                'invoice' => $this->record
                            ])
                    ])
                    ->collapsible()
                    ->collapsed(false),

                // Basic Invoice Information
                Section::make('Invoice Information')
                    ->schema([
                        Grid::make(5)
                            ->schema([
                                TextEntry::make('invoice_no')
                                    ->label('Invoice Number')
                                    ->weight('bold')
                                    ->color('primary'),
                                TextEntry::make('invoice_date')
                                    ->label('Invoice Date')
                                    ->date(),
                                TextEntry::make('due_date')
                                    ->label('Due Date')
                                    ->date(),
                                TextEntry::make('status')
									->label('Status')
									->badge()
									->default('no status')
									->formatStateUsing(fn ($state) => $state ?? 'no status')
									->color(fn (string $state): string => match ($state) {
										'Draft' => 'warning',
										'Issued' => 'info',
										'Closed' => 'success',
										'no status' => 'danger'
									}),
                                TextEntry::make('currency.symbol')
                                    ->label('Currency')
                                    ->badge()
									->formatStateUsing(fn ($record, $state) => $state . ' - ' . $record->currency->name)
                                    ->color('info'),
                            ]),
                    ])
                    ->columns(1),

                // Company & Client Information
                Section::make('Company & Client Information')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('company.name')
                                    ->label('Company (Issuer)')
                                    ->weight('bold')
                                    ->color('primary'),
                                TextEntry::make('client.name')
                                    ->label('Client (Recipient)')
                                    ->weight('bold')
                                    ->color('success'),
                            ]),
                        Grid::make(2)
                            ->schema([
                                TextEntry::make('company.address')
                                    ->label('Company Address')
                                    ->html()
                                    ->placeholder('No address provided'),
                                TextEntry::make('client_address')
                                    ->label('Client Address')
                                    ->html()
                                    ->placeholder('No address provided'),
                            ]),
                    ])
                    ->columns(1),

                // Invoice Details
                Section::make('Invoice Details')
					->collapsible()
                    ->schema([
                        ViewEntry::make('invoice_details_table')
                            ->hiddenLabel()
                            ->view('components.invoice-details-table', [
                                'invoice' => $this->record
                            ])
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(false),

                // Bank Information (Multiple Banks)
                Section::make('Bank Information')
                    ->schema([
                        ViewEntry::make('banks_table')
                            ->hiddenLabel()
                            ->view('components.invoice-banks-table', [
                                'invoice' => $this->record
                            ])
                            ->columnSpanFull()
                    ])
                    ->collapsible()
                    ->collapsed(true)
                    ->visible(fn ($record) => $record->companyBanks()->count() > 0),
            ]);
    }
}
