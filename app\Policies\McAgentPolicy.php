<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\McAgent;
use App\Models\User;

class McAgentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any McAgent');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, McAgent $mcagent): bool
    {
        return $user->checkPermissionTo('view McAgent');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create McAgent');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, McAgent $mcagent): bool
    {
        return $user->checkPermissionTo('update McAgent');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, McAgent $mcagent): bool
    {
        return $user->checkPermissionTo('delete McAgent');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any McAgent');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, McAgent $mcagent): bool
    {
        return $user->checkPermissionTo('restore McAgent');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any McAgent');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, McAgent $mcagent): bool
    {
        return $user->checkPermissionTo('replicate McAgent');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder McAgent');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, McAgent $mcagent): bool
    {
        return $user->checkPermissionTo('force-delete McAgent');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any McAgent');
    }
}
