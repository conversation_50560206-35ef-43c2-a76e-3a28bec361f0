<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Role permissions
            'view roles',
            'create roles',
            'edit roles',
            'delete roles',
            
            // Team permissions
            'view teams',
            'create teams',
            'edit teams',
            'delete teams',
            
            // Media permissions
            'view media',
            'upload media',
            'delete media',
            
            // Settings permissions
            'view settings',
            'edit settings',
            
            // Backup permissions
            'view backups',
            'create backups',
            'download backups',
            'delete backups',
            
            // Health permissions
            'view health',
            
            // Pulse permissions
            'view pulse',
            
            // Schedule permissions
            'view schedule',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        $managerRole = Role::create(['name' => 'manager']);
        $managerRole->givePermissionTo([
            'view users',
            'view roles',
            'view teams',
            'create teams',
            'edit teams',
            'view media',
            'upload media',
            'view settings',
            'view backups',
            'view health',
            'view pulse',
            'view schedule',
        ]);

        $userRole = Role::create(['name' => 'user']);
        $userRole->givePermissionTo([
            'view teams',
            'view media',
            'upload media',
        ]);

        // Create admin user
        $admin = User::where('email', '<EMAIL>')->first();
        
        if (!$admin) {
            $admin = User::factory()->create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }
        
        $admin->assignRole('admin');

        // Create manager user
        $manager = User::where('email', '<EMAIL>')->first();
        
        if (!$manager) {
            $manager = User::factory()->create([
                'name' => 'Manager',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }
        
        $manager->assignRole('manager');

        // Create regular user
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            $user = User::factory()->create([
                'name' => 'User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }
        
        $user->assignRole('user');
    }
}
