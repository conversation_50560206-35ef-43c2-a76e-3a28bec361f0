	public function getViewData(): array
	{
		$now = Carbon::now('Asia/Jakarta');
		$calcOrderRevenue = fn($q) =>
			$q->get()->sum(
				fn($ord) => ((($ord?->sell_rates ?? 0) - ($ord?->rates ?? 0)) * ($ord?->total ?? 0)) + ($ord?->charges ?? 0)
			);

		// Helper untuk membuat baseQuery baru setiap kali
		$base = fn() => Order::with(['agent', 'bank', 'company', 'currency', 'user']);

		// THIS YEAR & LAST YEAR
		$revenueThisYear = $calcOrderRevenue(($base())->whereYear('order_date', now()->year));
		$revenueLastYear = $calcOrderRevenue(($base())->whereYear('order_date', now()->subYear()->year));
		$yearDiff = $revenueThisYear !== 0 ? (($revenueThisYear - $revenueLastYear) / $revenueThisYear) * 100 : 0;

		// THIS MONTH & LAST MONTH
		$revenueThisMonth = $calcOrderRevenue(($base())
			->whereYear('order_date', now()->year)
			->whereMonth('order_date', now()->month));

		$revenueLastMonth = $calcOrderRevenue(($base())
			->whereYear('order_date', now()->subMonth()->year)
			->whereMonth('order_date', now()->subMonth()->month));

		$monthDiff = $revenueThisMonth !== 0 ? (($revenueThisMonth - $revenueLastMonth) / $revenueThisMonth) * 100 : 0;

		// THIS WEEK & LAST WEEK
		$revenueThisWeek = $calcOrderRevenue(($base())
			->whereBetween('order_date', [now()->startOfWeek(), now()->endOfWeek()]));

		$revenueLastWeek = $calcOrderRevenue(($base())
			->whereBetween('order_date', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()]));

		$weekDiff = $revenueThisWeek !== 0 ? (($revenueThisWeek - $revenueLastWeek) / $revenueThisWeek) * 100 : 0;

		// TODAY
		$revenueToday = $calcOrderRevenue(($base())
			->whereDate('order_date', now()->toDateString()));

		// YESTERDAY
		$revenueYesterday = $calcOrderRevenue(($base())
			->whereDate('order_date', now()->subDay()->toDateString()));

		// Day Difference
		$dayDiff = $revenueToday !== 0
			? (($revenueToday - $revenueYesterday) / $revenueToday) * 100
			: 0;

		return [
			'revenue' => [
				'year'  => $revenueThisYear,
				'month' => $revenueThisMonth,
				'week'  => $revenueThisWeek,
				'day'   => $revenueToday,
			],
			'difference' => [
				'year'  => round($yearDiff, 2),
				'month' => round($monthDiff, 2),
				'week'  => round($weekDiff, 2),
				'day'   => round($dayDiff, 2),
			],
			'datenumbering' => [
				'year'  => substr($now->year, -2), // 2 digit terakhir tahun, misal: 2025 → 25
				'month' => str_pad($now->month, 2, '0', STR_PAD_LEFT), // bulan 1 → 01, dst
				'week'  => str_pad($now->weekOfYear, 2, '0', STR_PAD_LEFT), // minggu ke-n
				'day'   => str_pad($now->day, 2, '0', STR_PAD_LEFT), // tanggal hari ini
			]
		];
	}
