<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\HealthCheckSetting;
use App\Models\User;

class HealthCheckSettingPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any HealthCheckSetting');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, HealthCheckSetting $healthchecksetting): bool
    {
        return $user->checkPermissionTo('view HealthCheckSetting');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create HealthCheckSetting');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, HealthCheckSetting $healthchecksetting): bool
    {
        return $user->checkPermissionTo('update HealthCheckSetting');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, HealthCheckSetting $healthchecksetting): bool
    {
        return $user->checkPermissionTo('delete HealthCheckSetting');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any HealthCheckSetting');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, HealthCheckSetting $healthchecksetting): bool
    {
        return $user->checkPermissionTo('restore HealthCheckSetting');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any HealthCheckSetting');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, HealthCheckSetting $healthchecksetting): bool
    {
        return $user->checkPermissionTo('replicate HealthCheckSetting');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder HealthCheckSetting');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, HealthCheckSetting $healthchecksetting): bool
    {
        return $user->checkPermissionTo('force-delete HealthCheckSetting');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any HealthCheckSetting');
    }
}
