<?php

namespace App\Filament\Mc\Pages;

use App\Filament\Mc\Widgets\McOrderWidget;
use App\Filament\Mc\Widgets\McDebtWidget;
use App\Filament\Mc\Widgets\McStatWidget;
use Filament\Actions\Action;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class McPanel extends BaseDashboard
{
	use HasFiltersForm;
	protected static ?string $navigationLabel = 'Home';
    protected static ?string $title = 'Dashboard';
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
    public function getWidgets(): array
    {
        return [
			/*
			1. Orders
				a. This week orders stat
				b. today orders stat
				c. today orders table
				d. order form
			*/
			McStatWidget::class,
			McOrderWidget::class,
			McDebtWidget::class,

			/*
			2. Deposits
				a. Balance stat
				b. today deposits stat
				c. all customer in debt table
				d. deposit form
			*/
        ];
    }

	// public function getHeading(): string|Htmlable
    // {
    //     return new HtmlString('');
    // }
	public function getSubheading(): string|Htmlable
    {
        return new HtmlString('<span class="text-sm italic">Quick Insight</span>');
    }

    public function getColumns(): int | string | array
    {
        return [
			'sm' => 1,
			'lg' => 4,
		];
    }
	protected function getHeaderActions(): array
	{
		return [
			Action::make('bank')
				->tooltip('Go to Bank list page')
				->url(fn () => route('filament.mc.resources.mc-bank-groups.index'))
				->icon('heroicon-o-building-office-2')
				->iconButton(),
			Action::make('Agent')
				->tooltip('Go to Agent list page')
				->url(fn () => route('filament.mc.resources.mc-agents.index'))
				->icon('heroicon-o-user-group')
				->iconButton(),
			Action::make('customer')
				->tooltip('Go to Customer list page')
				->url(fn () => route('filament.mc.resources.mc-customers.index'))
				->icon('heroicon-o-users')
				->iconButton(),
		];
	}
}
