<?php

namespace App\Policies;

use Illuminate\Auth\Access\Response;
use App\Models\CompanyDeposito;
use App\Models\User;

class CompanyDepositoPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any CompanyDeposito');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CompanyDeposito $companydeposito): bool
    {
        return $user->checkPermissionTo('view CompanyDeposito');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create CompanyDeposito');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CompanyDeposito $companydeposito): bool
    {
        return $user->checkPermissionTo('update CompanyDeposito');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CompanyDeposito $companydeposito): bool
    {
        return $user->checkPermissionTo('delete CompanyDeposito');
    }

    /**
     * Determine whether the user can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any CompanyDeposito');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CompanyDeposito $companydeposito): bool
    {
        return $user->checkPermissionTo('restore CompanyDeposito');
    }

    /**
     * Determine whether the user can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any CompanyDeposito');
    }

    /**
     * Determine whether the user can replicate the model.
     */
    public function replicate(User $user, CompanyDeposito $companydeposito): bool
    {
        return $user->checkPermissionTo('replicate CompanyDeposito');
    }

    /**
     * Determine whether the user can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder CompanyDeposito');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CompanyDeposito $companydeposito): bool
    {
        return $user->checkPermissionTo('force-delete CompanyDeposito');
    }

    /**
     * Determine whether the user can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any CompanyDeposito');
    }
}
