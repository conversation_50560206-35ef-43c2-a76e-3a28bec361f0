<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;
use Spatie\Health\Facades\Health;
use Spatie\Health\Checks\Checks\CacheCheck;
use Spatie\Health\Checks\Checks\DatabaseCheck;
use Spatie\Health\Checks\Checks\DebugModeCheck;
use Spatie\Health\Checks\Checks\EnvironmentCheck;
// use Spatie\Health\Checks\Checks\HorizonCheck;
use Spatie\Health\Checks\Checks\OptimizedAppCheck;
// use Spatie\Health\Checks\Checks\QueueCheck; // Disabled as queue is not used
// use Spatie\Health\Checks\Checks\RedisCheck;
// use Spatie\Health\Checks\Checks\ScheduleCheck; // Disabled as schedule is not used
use Spatie\Health\Checks\Checks\UsedDiskSpaceCheck;

class HealthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->registerChecksFromConfig();
    }

    /**
     * Register health checks based on configuration settings.
     */
    private function registerChecksFromConfig(): void
    {
        $enabledChecks = config('health-check-settings.enabled_checks', []);

        $checks = array_filter([
            // Check if the application is in debug mode
            ($enabledChecks['Debug Mode'] ?? true) ? $this->registerDebugModeCheck() : [],

            // Check if the environment is as expected
            ($enabledChecks['Environment'] ?? true) ? $this->registerEnvironmentCheck() : [],

            // Check if the application is optimized - important for production
            ($enabledChecks['Optimized App'] ?? true) ? $this->registerOptimizedAppCheck() : [],

            // Check if the database connection is working
            ($enabledChecks['Database'] ?? true) ? $this->registerDatabaseCheck() : [],

            // Check if the cache is working
            ($enabledChecks['Cache'] ?? true) ? $this->registerCacheCheck() : [],

            // Check if the queue is working
            ($enabledChecks['Queue'] ?? false) ? $this->registerQueueCheck() : [],

            // Check if the schedule is running
            ($enabledChecks['Schedule'] ?? false) ? $this->registerScheduleCheck() : [],

            // Check disk space - only enabled on non-Windows systems
            ($enabledChecks['Disk Space'] ?? true) ? $this->registerDiskSpaceCheck() : [],
        ]);

        $this->registerFlattenedChecks($checks);
    }

    /**
     * Flatten and register health checks.
     */
    private function registerFlattenedChecks(array $checks): void
    {
        // Flatten the array to handle cases where methods return arrays
        $flattenedChecks = [];
        foreach ($checks as $check) {
            if (is_array($check)) {
                foreach ($check as $subCheck) {
                    if ($subCheck) {
                        $flattenedChecks[] = $subCheck;
                    }
                }
            } elseif ($check) {
                $flattenedChecks[] = $check;
            }
        }

        Health::checks($flattenedChecks);
    }

    /**
     * Register debug mode check with fallback.
     */
    private function registerDebugModeCheck()
    {
        try {
            return DebugModeCheck::new()
                ->expectedToBe(env('APP_DEBUG', false))
                ->label('Debug Mode');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register debug mode check: " . $e->getMessage());
        }

        return [];
    }

    /**
     * Register environment check with fallback.
     */
    private function registerEnvironmentCheck()
    {
        try {
            return EnvironmentCheck::new()
                ->expectEnvironment(env('APP_ENV', 'production'))
                ->label('Environment');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register environment check: " . $e->getMessage());
        }

        return [];
    }

    /**
     * Register optimized app check with fallback.
     */
    private function registerOptimizedAppCheck()
    {
        try {
            return OptimizedAppCheck::new()
                ->label('Optimized App');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register optimized app check: " . $e->getMessage());
        }

        return [];
    }

    /**
     * Register database check with fallback.
     */
    private function registerDatabaseCheck()
    {
        try {
            return DatabaseCheck::new()
                ->label('Database');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register database check: " . $e->getMessage());
        }

        return [];
    }

    /**
     * Register cache check with fallback.
     */
    private function registerCacheCheck()
    {
        try {
            return CacheCheck::new()
                ->label('Cache');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register cache check: " . $e->getMessage());
        }

        return [];
    }

    /**
     * Register queue check with fallback.
     * Currently disabled as queue is not used.
     */
    private function registerQueueCheck()
    {
        // Queue check is disabled as queue is not used
        return [];

        // Uncomment below to enable queue check when needed
        /*
        try {
            return QueueCheck::new()
                ->label('Queue');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register queue check: " . $e->getMessage());
        }

        return [];
        */
    }

    /**
     * Register schedule check with fallback.
     * Currently disabled as schedule is not used.
     */
    private function registerScheduleCheck()
    {
        // Schedule check is disabled as schedule is not used
        return [];

        // Uncomment below to enable schedule check when needed
        /*
        try {
            return ScheduleCheck::new()
                ->label('Schedule');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register schedule check: " . $e->getMessage());
        }

        return [];
        */
    }

    /**
     * Register disk space check with fallback.
     */
    private function registerDiskSpaceCheck()
    {
        // Skip disk space check on Windows
        if (PHP_OS_FAMILY === 'Windows') {
            return [];
        }

        try {
            return UsedDiskSpaceCheck::new()
                ->failWhenUsedSpaceIsAbovePercentage(90)
                ->label('Disk Space');
        } catch (\Exception $e) {
            // Log the error but don't crash the application
            Log::warning("Failed to register disk space check: " . $e->getMessage());
            return [];
        }
    }
}
