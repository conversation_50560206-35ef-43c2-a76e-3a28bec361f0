<?php

namespace App\Filament\Admin\Resources\MasterInvoiceDetailResource\Pages;

use App\Filament\Admin\Resources\MasterInvoiceDetailResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateMasterInvoiceDetail extends CreateRecord
{
    protected static string $resource = MasterInvoiceDetailResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
}
