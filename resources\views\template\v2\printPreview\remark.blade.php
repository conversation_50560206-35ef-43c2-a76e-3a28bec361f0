@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$invoice = $payload['invoice'];
	$company = $payload['invoice']->company;

	// Remark configuration
	$remarkStyle = $layoutConfig['remarkStyle'] ?? 'default';
	$remarkAlign = $layoutConfig['remarkAlign'] ?? 'text-left';
	$remarkWeight = $layoutConfig['remarkWeight'] ?? 'font-normal';
	$remarkBorder = $layoutConfig['remarkBorder'] ?? false;
	$remarkBg = $layoutConfig['remarkBg'] ?? 'transparent';

	// Use isDarkColor function from parent (printPreview.blade.php)
	$remarkTextColorClass = isDarkColor($remarkBg) ? 'text-white' : 'text-black';
@endphp

{{-- Remarks Section --}}
@if($invoice->remarks)
	<div class="remarks-section {{ $remarkAlign }}">
		<div class="{{ $remarkBorder ? 'border border-gray-300 p-2 rounded' : 'p-2' }} {{ $remarkTextColorClass }}" style="background-color: {{ $remarkBg }}">
			<div class="text-compact font-medium mb-1">
				{{ $company->type == 1 ? 'Remarks' : 'Catatan' }}:
			</div>
			<div class="text-xs-compact {{ $remarkWeight }}">
				{!! $invoice->remarks !!}
			</div>
		</div>
	</div>
@endif
