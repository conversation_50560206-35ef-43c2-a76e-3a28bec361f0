<div x-cloak x-data="globalCompanyModal()" x-init="init()" @open-company-modal.window="openModal()"
	@close-modal.window="close()"
    class="global-company-modal">
    <div x-show="open" x-transition class="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-y-auto" style="display: none;">
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" @click="close()"></div>

        <div x-show="open"
            x-transition
            @keydown.escape.window="close()"
            class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 w-full max-w-3/4 mx-auto z-60 overflow-y-auto max-h-[90vh]"
        >
            {{-- Livewire company Create Form --}}
            @livewire(\App\Filament\Admin\Resources\CompanyResource\Pages\CreateCompany::class)
			<span class="text-xs italic text-indigo-500">Warning! Redirecting to the dashboard upon successful submission</span>
        </div>
    </div>
</div>
<script>
    function globalCompanyModal() {
        return {
            open: false,
            init() {
                // Shortcut: Ctrl+Shift+O
				let step = 1
                document.addEventListener('keydown', (e) => {
                    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
                    const metaKey = isMac ? e.metaKey : e.ctrlKey;

					// if (metaKey && e.altKey && e.code === 'Digit1') {
					if (metaKey && e.altKey && e.key.toLowerCase() === 'c') {
                        e.preventDefault();
                        this.openModal();
                    }
                });

                // From menu click
                document.addEventListener('click', (e) => {
                    if (e.target.closest('a[href="#company-modal"]')) {
                        e.preventDefault();
                        this.openModal();
                    }
                });
            },
            openModal() {
                this.open = true;
            },
            close() {
                this.open = false;
            }
        }
    }
</script>
