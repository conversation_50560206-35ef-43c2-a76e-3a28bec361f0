@php
    $sizeCompanyClasses = [
        'h1' => 'text-4xl',     // besar
        'h2' => 'text-3xl',
        'h3' => 'text-2xl',
        'h4' => 'text-xl',
        'h5' => 'text-lg',
        'h6' => 'text-base',    // kecil
    ];

	$sizeInvoiceClasses = [
        'h3' => 'text-2xl',
        'h4' => 'text-xl',
        'h5' => 'text-lg',
        'h6' => 'text-base',    // kecil
    ];

    $horizontalAlign = [
        'text-start' => 'text-start',
        'text-center' => 'text-center',
        'text-end' => 'text-end',
    ];

    $verticalAlign = [
        'items-start' => 'items-start',
        'items-center' => 'items-center',
        'items-end' => 'items-end',
    ];

    // Menentukan urutan flex children berdasarkan posisi logo
    $flexOrder = $headingLogoPosition === 'logo-right' ? ['company-info', 'logo'] : ['logo', 'company-info'];

	$columns = collect([
        ['key' => 'item', 'label' => 'Item'],
        ['key' => 'description', 'label' => 'Description'],
        ['key' => 'qty', 'label' => 'Qty'],
        ['key' => 'price', 'label' => 'Price'],
        ['key' => 'total', 'label' => 'Total'], // Jangan sembunyikan
    ])->map(function ($col) use ($tableVisibleColumns) {
        $col['show'] = in_array($col['key'], $tableVisibleColumns) || $col['key'] === 'total';
        return $col;
    });

    $visibleColumnsFiltered = $columns->filter(fn($c) => $c['show'])->values();
    $visibleCount = $visibleColumnsFiltered->count();
    $lastVisibleIndex = $visibleCount - 1;
    $colspan = $visibleCount - 1;

    $rows = [
        ['item' => 'A1', 'description' => 'Product A1', 'qty' => 2, 'price' => 50000, 'total' => 100000],
        ['item' => 'B1', 'description' => 'Product B1', 'qty' => 1, 'price' => 70000, 'total' => 70000],
    ];

    $summaries = [
		'Total Items' => '8',
		'Total Quantity' => '76',
        'Booking Fee' => 'Rp 100.000',
        'Subtotal' => 'Rp 170.000',
        'Rate' => 'Rp 17.000',
        'Total' => 'Rp 287.000',
    ];

	$summaryDetails = collect($summaries)
    	->filter(fn($_, $label) => in_array($label, ['Total Items', 'Total Quantity']));

	$summaryMain = collect($summaries)
    	->reject(fn($_, $label) => in_array($label, ['Total Items', 'Total Quantity']));

	if (!function_exists('getColumnWidthStyle')) {
		function getColumnWidthStyle($key) {
			return match ($key) {
				'description' => 'width: 40%; text-align: left;',
				'total' => 'width: 20%; text-align: right;',
				'price' => 'width: 15%; text-align: right;',
				'qty' => 'width: 10%; text-align: right;',
				default => 'width: 15%; text-align: left;',
			};
		}
	}

	if (!function_exists('isDarkColor')) {
		function isDarkColor($hexColor) {
			$hexColor = ltrim($hexColor, '#');
			$r = hexdec(substr($hexColor, 0, 2));
			$g = hexdec(substr($hexColor, 2, 2));
			$b = hexdec(substr($hexColor, 4, 2));
			$brightness = ($r * 299 + $g * 587 + $b * 114) / 1000;
			return $brightness < 128;
		}
	}

	if (!function_exists('bgDarkenColor')) {
		function bgDarkenColor($hex, $percent = 1) {
			$hex = ltrim($hex, '#');

			$r = hexdec(substr($hex, 0, 2));
			$g = hexdec(substr($hex, 2, 2));
			$b = hexdec(substr($hex, 4, 2));

			$r = max(0, $r - intval($r * $percent / 100));
			$g = max(0, $g - intval($g * $percent / 100));
			$b = max(0, $b - intval($b * $percent / 100));

			return sprintf("#%02x%02x%02x", $r, $g, $b);
		}
	}

	if (!function_exists('getBorderClasses')) {
		function getBorderClasses($style, $isTh = false) {
			return match ($style) {
				'none' => '',
				'row' => $isTh ? 'border-b border-gray-300' : 'border-b border-gray-300',
				'column' => 'border-s border-gray-300',
				'full' => 'border border-gray-300',
				default => '',
			};
		}
    }

    $headerTextColorClass = isDarkColor($tableHeaderShading) ? 'text-white' : 'text-black';
    $inwordTextColorClass = isDarkColor($inwordBg) ? 'text-white' : 'text-black';
    $bankInfoTextColorClass = isDarkColor($bankInfoBg) ? 'text-white' : 'text-black';
    $remarkTextColorClass = isDarkColor($remarkBg) ? 'text-white' : 'text-black';
    $tableTextColorClass = isDarkColor($tableSummaryBg) ? 'text-white' : 'text-black';
@endphp

<div class="bg-white shadow p-4 rounded-2xl border border-gray-300 mb-2 mx-auto w-full max-w-none">
	@include('template.v2.preview.headingLayout')

	<div class="mt-6">
		{{-- 'before table' --}}
		@include('template.v2.preview.beforeTable')

		<div class="mt-6">
			@include('template.v2.preview.tableStyle')
		</div>

		{{-- 'after table' --}}
		@include('template.v2.preview.afterTable')
	</div>
</div>
