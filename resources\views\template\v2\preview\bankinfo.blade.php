@php
    $bankIsSingleColumn = false;
    $bankOnColumn1 = false;
    $bankColumnSize = 2; // Default column size

    foreach ($repeaterLayout as $r) {
        $col1 = (array) ($r['column_1_content'] ?? []);
        $col2 = (array) ($r['column_2_content'] ?? []);

        if (in_array('bank_info', $col1)) {
            $bankColumnSize = $r['column_size'] ?? 2;
            $bankOnColumn1 = true;
        }

        if (
            ($r['column_size'] == 1) &&
            (in_array('bank_info', $col1))
        ) {
            $bankIsSingleColumn = true;
        }

        if ($bankIsSingleColumn && $bankOnColumn1) {
            break;
        }
    }
	$isGrid = $bankIsSingleColumn && $bankOnColumn1;
@endphp

<h3 class="mb-1 text-sm">Bank Information</h3>
@if($bankInfoLayout === 'grid')
	@if($bankColumnSize == 1)
		{{-- Column Size 1: 2 Grid Layout --}}
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			@foreach ($companyBanks as $bank)
				<div class="{{ $bankInfoTextColorClass }} {{ $bankInfoBorder ? 'border border-gray-300 rounded-lg' : '' }} p-4" style="background-color: {{ $bankInfoBg }}">
					<div class="space-y-3 text-xs {{ $bankInfoDecor }}">
						<div class="grid grid-cols-5 items-start">
							<span class="col-span-1">Bank Name:</span>
							<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">Registered Bank Name</span>
						</div>
						<div class="grid grid-cols-5 items-start">
							<span class="col-span-1">Account Name:</span>
							<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">Registered Bank Account Name</span>
						</div>
						<div class="grid grid-cols-5 items-start">
							<span class="col-span-1">Account Number:</span>
							<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">Registered Bank Account Number</span>
						</div>
					</div>
				</div>
			@endforeach
		</div>
	@else
		{{-- Column Size 2: 1 Grid Layout (Stacked) --}}
		<div class="grid grid-cols-1 gap-6">
			@foreach ($companyBanks as $bank)
				<div class="{{ $bankInfoTextColorClass }} {{ $bankInfoBorder ? 'border border-gray-300 rounded-lg' : '' }} p-4" style="background-color: {{ $bankInfoBg }}">
					<div class="space-y-3 text-xs {{ $bankInfoDecor }}">
						<div class="grid grid-cols-5 items-start">
							<span class="col-span-1">Bank Name:</span>
							<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">Registered Bank Name</span>
						</div>
						<div class="grid grid-cols-5 items-start">
							<span class="col-span-1">Account Name:</span>
							<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">Registered Bank Account Name</span>
						</div>
						<div class="grid grid-cols-5 items-start">
							<span class="col-span-1">Account Number:</span>
							<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">Registered Bank Account Number</span>
						</div>
					</div>
				</div>
			@endforeach
		</div>
	@endif
@endif

@if($bankInfoLayout === 'stacked')
	@if($bankColumnSize == 1)
		<div class="space-y-3">
			<div class="grid grid-cols-2 gap-4">
				@foreach ($companyBanks as $bank)
					<div class="{{ $bankInfoTextColorClass }} {{ $bankInfoBorder ? 'border border-gray-300 p-3 rounded' : 'p-3' }}" style="background-color: {{ $bankInfoBg }}">
						<div class="{{ $bankInfoDecor }}">
							<h6 class="h6 mb-2 border-b border-gray-300 py-2 font-bold uppercase">BANK NAME</h6>
							<div class="text-xs space-y-2 flex flex-col">
								<div class="flex flex-col">
									<span>Account Name:</span>
									<span class="{{ $bankInfoWeight }}">Registered Bank Account Name</span>
								</div>
								<div class="flex flex-col">
									<span>Account Number:</span>
									<span class="{{ $bankInfoWeight }}">Registered Bank Account Number</span>
								</div>
							</div>
						</div>
					</div>
				@endforeach
			</div>
		</div>
	@else
		<div class="space-y-3">
			@foreach ($companyBanks as $bank)
				<div class="{{ $bankInfoTextColorClass }} {{ $bankInfoBorder ? 'border border-gray-300 p-3 rounded' : 'p-3' }}" style="background-color: {{ $bankInfoBg }}">
					<div class="{{ $bankInfoDecor }}">
							<h6 class="h6 mb-2 border-b border-gray-300 py-2 font-bold uppercase">BANK NAME</h6>
							<div class="text-xs space-y-2 flex flex-col">
								<div class="flex flex-col">
									<span>Account Name:</span>
									<span class="{{ $bankInfoWeight }}">Registered Bank Account Name</span>
								</div>
								<div class="flex flex-col">
									<span>Account Number:</span>
									<span class="{{ $bankInfoWeight }}">Registered Bank Account Number</span>
								</div>
							</div>
						</div>
				</div>
			@endforeach
		</div>
	@endif
@endif

@if($bankInfoLayout === 'tabular')
	@if($bankColumnSize == 1)
		{{-- Column Size 1: Grid/Side by Side Layout --}}
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			@foreach ($companyBanks as $bank)
				@php
					// Handle both array (dummy data) and object (real data) formats
					if (is_array($bank)) {
						$attributes = collect($bank)
							->except([
								'id', 'company_id', 'custom_columns', 'is_default',
								'created_at', 'updated_at', 'deleted_at',
							])
							->filter(fn($value) => !is_null($value) && $value !== '');
					} else {
						$attributes = collect($bank->getAttributes())
							->except([
								'id', 'company_id', 'custom_columns', 'is_default',
								'created_at', 'updated_at', 'deleted_at',
							])
							->filter(fn($value) => !is_null($value));
					}

					$swift = $attributes->pull('swift');
					$swiftCorrespondent = $attributes->pull('swift_correspondent');
					if (!is_null($swift)) {
						$attributes->put('swift', $swift);
					}
					if (!is_null($swiftCorrespondent)) {
						$attributes->put('swift_correspondent', $swiftCorrespondent);
					}

					// Handle custom columns for both array and object formats
					$customColumns = is_array($bank) ? ($bank['custom_columns'] ?? []) : $bank->custom_columns;
					if (is_array($customColumns) && count($customColumns)) {
						foreach ($customColumns as $label => $field) {
							if (is_array($field) && isset($field['type'], $field['value'])) {
								$attributes->put('custom__' . $label, $field);
							}
						}
					}
				@endphp

				<div class="{{ $bankInfoTextColorClass }}" style="background-color: {{ $bankInfoBg }}">
					<table class="w-full text-xs {{ $bankInfoWeight }} {{ $bankInfoDecor }} border-collapse">
						@foreach ($attributes as $key => $value)
							@php
								$isCustom = str_starts_with($key, 'custom__');
								$label = $isCustom ? Str::of(Str::after($key, 'custom__'))->headline() : Str::of($key)->headline();
							@endphp
							<tr>
								<td class="border border-gray-300 p-2 bg-gray-50 font-medium" width="150px">{{ $label }}</td>
								<td class="border border-gray-300 p-2">
									@if ($isCustom)
										@switch($value['type'])
											@case('richtext')
												{!! $value['value'] !!}
												@break

											@case('textarea')
											@case('text')
												{{ $value['value'] }}
												@break
										@endswitch
									@elseif(Str::contains($key, 'address') && Str::contains($value, '<'))
										{!! $value !!}
									@else
										{{ $value }}
									@endif
								</td>
							</tr>
						@endforeach
					</table>
				</div>
			@endforeach
		</div>
	@else
		{{-- Column Size 2: Vertical/Stacked Layout (Default) --}}
		<div class="space-y-4">
			@foreach ($companyBanks as $bank)
				@php
					// Handle both array (dummy data) and object (real data) formats
					if (is_array($bank)) {
						$attributes = collect($bank)
							->except([
								'id', 'company_id', 'custom_columns', 'is_default',
								'created_at', 'updated_at', 'deleted_at',
							])
							->filter(fn($value) => !is_null($value) && $value !== '');
					} else {
						$attributes = collect($bank->getAttributes())
							->except([
								'id', 'company_id', 'custom_columns', 'is_default',
								'created_at', 'updated_at', 'deleted_at',
							])
							->filter(fn($value) => !is_null($value));
					}

					$swift = $attributes->pull('swift');
					$swiftCorrespondent = $attributes->pull('swift_correspondent');
					if (!is_null($swift)) {
						$attributes->put('swift', $swift);
					}
					if (!is_null($swiftCorrespondent)) {
						$attributes->put('swift_correspondent', $swiftCorrespondent);
					}

					// Handle custom columns for both array and object formats
					$customColumns = is_array($bank) ? ($bank['custom_columns'] ?? []) : $bank->custom_columns;
					if (is_array($customColumns) && count($customColumns)) {
						foreach ($customColumns as $label => $field) {
							if (is_array($field) && isset($field['type'], $field['value'])) {
								$attributes->put('custom__' . $label, $field);
							}
						}
					}
				@endphp

				<div class="{{ $bankInfoTextColorClass }}" style="background-color: {{ $bankInfoBg }}">
					<table class="w-full text-xs {{ $bankInfoWeight }} {{ $bankInfoDecor }} border-collapse">
						@foreach ($attributes as $key => $value)
							@php
								$isCustom = str_starts_with($key, 'custom__');
								$label = $isCustom ? Str::of(Str::after($key, 'custom__'))->headline() : Str::of($key)->headline();
							@endphp
							<tr>
								<td class="border border-gray-300 p-2 bg-gray-50 font-medium" width="150px">{{ $label }}</td>
								<td class="border border-gray-300 p-2">
									@if ($isCustom)
										@switch($value['type'])
											@case('richtext')
												{!! $value['value'] !!}
												@break

											@case('textarea')
											@case('text')
												{{ $value['value'] }}
												@break
										@endswitch
									@elseif(Str::contains($key, 'address') && Str::contains($value, '<'))
										{!! $value !!}
									@else
										{{ $value }}
									@endif
								</td>
							</tr>
						@endforeach
					</table>
				</div>
			@endforeach
		</div>
	@endif
@endif
