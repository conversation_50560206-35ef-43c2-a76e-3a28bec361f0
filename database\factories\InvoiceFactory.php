<?php

namespace Database\Factories;

use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceFactory extends Factory
{
    protected $model = Invoice::class;

    public function definition(): array
    {
        return [
            'order_id' => 1, // Will be overridden in tests
            'company_id' => 1, // Will be overridden in tests
            'client_id' => 1,
            'invoice_create_by' => 1,
            'invoice_no' => 'INV-' . $this->faker->unique()->numerify('####'),
            'invoice_date' => $this->faker->date(),
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days')->format('Y-m-d'),
            'currency_id' => 1,
            'order_amount' => $this->faker->randomFloat(2, 100, 10000),
            'booking_fee' => $this->faker->randomFloat(2, 10, 100),
            'invoice_amount' => $this->faker->randomFloat(2, 100, 10000),
            'status' => 'Draft',
            'inv_sub_total' => $this->faker->randomFloat(2, 100, 10000),
            'rates' => $this->faker->randomFloat(2, 14000, 16000),
            'verification_status' => '0',
            'supervision_status' => '0',
            'approval_status' => '0',
        ];
    }
}
