<?php

namespace App\Filament\Admin\Resources\CompanyDepositoResource\Pages;

use App\Filament\Admin\Resources\CompanyDepositoResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateCompanyDeposito extends CreateRecord
{
    protected static string $resource = CompanyDepositoResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	public function getHeading(): string
	{
        return 'Internal Transactions';
	}

	protected static bool $canCreateAnother = false;
	protected function getCancelFormAction(): Actions\Action
    {
        return Actions\Action::make('cancel')
            ->label('Cancel')
            ->color('gray')
            ->action(fn () => $this->dispatch('close-modal'));
    }
	public function getRedirectUrl(): string
	{
		return '/admin';
	}
}
