<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\CompanyDeposito;
use App\Models\CompanyDepositSummary;
use App\Models\Currency;
use App\Models\MasterInvoiceDetail;
use App\Models\McAgent;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Forms\Components\{DatePicker, Fieldset, Group, Hidden, Placeholder, Select, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Notifications\Notification;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\{SelectColumn, TextColumn, TextInputColumn};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

class DepositHistoryWidget extends BaseWidget
{
	protected static ?string $heading = 'Transaction History';
	protected int | string | array $columnSpan = 'full';

	protected $listeners = ['deposit-created' => '$refresh'];
	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['Staff Order', 'Staff Invoice', 'Admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['Staff Order', 'Staff Invoice', 'Admin', 'Super Admin']);
	}

	public function table(Table $table): Table
	{
		return $table
			->recordClasses(function (Model $record) {
				$classes = [];

				// Ambil daftar ID bank milik company
				$companyBanks = $record->company?->banks->pluck('id');

				// Validasi apakah bank_id dari order ada di daftar bank company
				$isBankValid = $companyBanks?->contains($record->bank_id);

				if (!$isBankValid) {
					// Tandai merah kalau bank tidak valid
					$classes[] = 'bg-red-50 dark:bg-red-600';
				}

				return implode(' ', $classes);
			})
			->query(function () {
				$start = Carbon::now()->startOfMonth()->subDays(7); // 7 hari sebelum awal bulan ini
				$end = Carbon::now()->endOfMonth(); // akhir bulan ini

				$query = CompanyDeposito::query()
					->with(['company'])
					->whereBetween('trx_date', [$start, $end]);

				return $query;
			})
			->columns([
				TextColumn::make('trx_no')
					->label('#Trx')
					->searchable()
					->sortable()
					->limit(30),
				TextColumn::make('bank.bank_name')
					->label('Bank')
					->searchable()
					->sortable()
					->limit(30)
					->description(function ($record) {
						// remark: warning jika order tapi belum pilih bank
						if ($record->trx_type === 'order' && empty($record->bank_id)) {
							return new HtmlString('<span class="text-red-600 font-semibold">⚠ WARNING! No Bank data</span>');
						}

						// remark: validasi bank_id milik company terkait (opsional jika keduanya ada)
						$invalidBankBadge = '';
						if (! empty($record->company_id) && ! empty($record->bank_id)) {
							$companyBankIds = $record->company?->banks?->pluck('id') ?? collect();

							if ($companyBankIds->isNotEmpty() && ! $companyBankIds->contains($record->bank_id)) {
								// remark: badge invalid bank
								$invalidBankBadge = '<span class="ml-2 inline-flex items-center rounded px-1.5 py-0.5 text-xs font-semibold text-red-700 bg-red-50 dark:text-red-100 dark:bg-red-600/30">⚠ Invalid Bank</span>';
							}
						}

						// remark: limit deskripsi & escape agar aman
						$desc = e(Str::limit($record->description ?? '', 30));

						// remark: gabungkan output
						return new HtmlString("{$invalidBankBadge}<span>{$desc}</span>");
					}),
				TextColumn::make('company.name')
					->label('Company')
					->searchable()
					->sortable()
					->limit(30),
				TextColumn::make('trx_date')
					->label('Trx Date')
					->date()
					->sortable(),
				TextColumn::make('trx_type')
					->badge()
					->color(fn($state) => match ($state) {
						'in' => 'success',
						'out' => 'danger',
						'order' => 'warning',
					})
					->formatStateUsing(fn($state) => match ($state) {
						'in' => 'Incoming',
						'out' => 'Outgoing',
						'order' => 'Order (Outgoing)',
					}),
				TextColumn::make('amount')
					->label('Amount')
					->formatStateUsing(fn($record, $state) => ($record->currency?->symbol ?? '') . ' ' . number_format($state, 2, ',', '.'))
					->alignEnd()
					->sortable(),
			])
			->filters([
				Filter::make('trx_date')
					->form([
						DatePicker::make('from')->default(today()),
						DatePicker::make('until')->default(today()),
					])
					->query(function (Builder $query, array $data): Builder {
						return $query
							->when(
								$data['from'],
								fn(Builder $query, $date): Builder => $query->whereDate('trx_date', '>=', $date),
							)
							->when(
								$data['until'],
								fn(Builder $query, $date): Builder => $query->whereDate('trx_date', '<=', $date),
							);
					}),
				SelectFilter::make('company_id')
					->label('Company')
					->options(Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id'))
					->searchable(),
				SelectFilter::make('trx_type')
					->options([
						'in' => 'Incoming',
						'out' => 'Outgoing',
						'order' => 'Order (Outgoing)',
					]),
			])
			->actions([
				ActionGroup::make([
					ViewAction::make()
						->tooltip('View Transaction Details')
						->modalWidth('3xl')
						->modalFooterActionsAlignment(Alignment::Right)
						->modalHeading(fn($record) => 'Detail for: ' . $record->trx_no)
						->form($this->getDepositView()),
					EditAction::make()
						->tooltip('Edit Data')
						->modalWidth('3xl')
						->modalFooterActionsAlignment(Alignment::Right)
						->modalHeading(fn($record) => 'Edit Order: ' . $record->trx_no)
						->visible(fn() => Auth::user()->hasAnyRole(['Admin', 'Super Admin']))
						->form($this->getDepositForm())
						->fillForm(function (CompanyDeposito $record): array {
							return [
								'trx_no' => $record->trx_no,
								'company_id' => $record->company_id,
								'bank_id' => $record->bank_id,
								'trx_date' => $record->trx_date,
								'amount' => $record->amount,
								'description' => $record->description,
								'trx_type' => $record->trx_type,
							];
						})
						->action(function (CompanyDeposito $record, array $data): void {
							try {
								$record->update([
									'trx_no' => $data['trx_no'],
									'company_id' => $data['company_id'],
									'bank_id' => $data['bank_id'],
									'trx_date' => $data['trx_date'],
									'amount' => $data['amount'],
									'description' => $data['description'],
									'trx_type' => $data['trx_type'],
								]);

								Notification::make()
									->title('Transaction Updated Successfully')
									->success()
									->send();

								// Dispatch event to refresh other widgets
								$this->dispatch('transaction-created');
							} catch (\Exception $e) {
								Notification::make()
									->title('Error Updating Transaction')
									->body($e->getMessage())
									->danger()
									->send();
							}
						}),

					DeleteAction::make()
						->tooltip('Delete')
						->visible(fn() => Auth::user()->hasAnyRole(['Admin', 'Super Admin']))
						->requiresConfirmation()
						->modalHeading('Delete Transaction')
						->modalDescription('Are you sure you want to delete this transaction?')
						->modalSubmitActionLabel('Yes, delete it'),
				]),
			])
			->defaultSort('created_at', 'desc')
			->striped()
			->paginated([10, 25, 50])
			->defaultPaginationPageOption(10)
			->emptyStateHeading('No internal transaction yet.')
			->emptyStateDescription(' Use filter to display transactions or create new transaction.');
	}

	protected function getDepositForm(): array
	{
		return [
			Group::make()
				->schema([
					Hidden::make('trx_no'),
					Select::make('company_id')
						->label('Select Client')
						->helperText('Note: This will become the client in the invoice')
						->searchable()
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						])
						->reactive()
						->options(fn() => Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id')),

					Select::make('bank_id')
						->label('Select Bank')
						->searchable()
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						])
						->helperText('The order amount will be debited from the selected bank.')
						->reactive()
						->options(fn($get) => CompanyBank::where('company_id', $get('company_id'))
							->pluck('bank_name', 'id'))
						->afterStateUpdated(function (callable $set, $state, callable $get) {
							$balance = CompanyDepositSummary::where('bank_id', $state)->first()->balance ?? 0;
							$set('balance', $balance);
						}),

					DatePicker::make('trx_date')
						->columnSpan([
							'default' => 8,
							'lg' => 2,
						])
						->default(today())
						->timezone('Asia/Jakarta')
						->required()
						->native(false),

					Select::make('trx_type')
						->searchable()
						->columnSpan([
							'default' => 8,
							'lg' => 2,
						])
						->options([
							'in' => 'Incoming',
							'out' => 'Outgoing',
						])
						->required()
						->reactive(),
					TextInput::make('amount')
						->live(onBlur: true)
						->numeric()
						->required()
						->columnSpan([
							'default' => 8,
							'lg' => 4,
						]),
					Textarea::make('description')
						->reactive()
						->maxLength(191)
						->columnSpanFull(),
				])->columns(8)
		];
	}

	protected function getDepositView(): array
	{
		return [
			Group::make()
				->schema([
					Placeholder::make('company')
						->columnSpanFull()
						->inlineLabel()
						->content(fn($record) => $record->company?->name),
					Placeholder::make('bank')
						->columnSpanFull()
						->inlineLabel()
						->content(fn($record) => $record->bank?->bank_name),

					Placeholder::make('date')
						->columnSpanFull()
						->inlineLabel()
						->content(fn($record) => $record->trx_date->format('d F Y')),

					Placeholder::make('desc')
						->columnSpanFull()
						->inlineLabel()
						->content(fn($record) => $record->description),

					Placeholder::make('trxAmount')
						->columnSpanFull()
						->inlineLabel()
						->content(fn($record) => number_format($record->amount, 2, ',', '.')),
				])->columns(8)
		];
	}
}
