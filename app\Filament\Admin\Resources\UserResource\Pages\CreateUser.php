<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use Filament\Resources\Pages\CreateRecord;
use TomatoPHP\FilamentApi\Traits\InteractWithAPI;

class <PERSON>reateUser extends CreateRecord
{
    // use InteractWithAPI;

    public static function getFilamentAPIMiddleware(): array
    {
        return [];
    }
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if (empty($data['username']) && !empty($data['name'])) {
            $username = strtolower(str_replace(' ', '_', $data['name']));
            $originalUsername = $username;
            $counter = 1;

            while (\App\Models\User::where('username', $username)->exists()) {
                $username = $originalUsername . '_' . $counter;
                $counter++;
            }

            $data['username'] = $username;
        }

        return $data;
    }
}
