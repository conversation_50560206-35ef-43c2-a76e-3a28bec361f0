<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => ['class' => 'mb-6']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mb-6']); ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3 w-full">
            <div class="col-span-1 lg:col-span-3 space-y-2">
                <span class="text-lg font-semibold px-4 mb-4">Transaction Insight</span>
                <div class="grid grid:cols-1 lg:grid-cols-3 lg:border-dashed lg:border-t lg:border-b">
                    <div class="col-span-1 p-4 text-center flex flex-col">
                        <span class="font-semibold">IDR <?php echo e(number_format($transactionInsight['lastWeekPurchase'], 2, ',', '.')); ?></span>
                        <span class="text-sm text-gray-400 dark:text-gray-200">Previous Week</span>
                    </div>
                    <div class="col-span-1 lg:border-dashed lg:border-s lg:border-e p-4 text-center flex flex-col">
                        <span class="font-semibold">IDR <?php echo e(number_format($transactionInsight['thisWeekPurchase'], 2, ',', '.')); ?></span>
                        <span class="text-sm text-gray-400 dark:text-gray-200">This Week</span>
                    </div>
                    <div class="col-span-1 p-4 text-center flex flex-col">
                        <span class="font-semibold">IDR <?php echo e(number_format($transactionInsight['thisMonthPurchase'], 2, ',', '.')); ?></span>
                        <span class="text-sm text-gray-400 dark:text-gray-200">This Month</span>
                    </div>
                </div>
				
				<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split(\App\Filament\Admin\Widgets\OrderTrendsWidget::class);

$__html = app('livewire')->mount($__name, $__params, 'lw-456276864-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
				<span class="text-xs italic text-gray-500 dark:text-gray-400">Weekly Purchase Transaction Trends</span>
            </div>
            <div class="col-span-1 lg:col-span-2 lg:border-s px-4 space-y-6 w-full">
                <div class="flex justify-between align-center">
                    <div class="flex align-top">
                        <?php if (isset($component)) { $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon','data' => ['icon' => 'heroicon-s-currency-dollar','class' => 'h-8 w-8 text-gray-500 dark:text-gray-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-s-currency-dollar','class' => 'h-8 w-8 text-gray-500 dark:text-gray-400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $attributes = $__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__attributesOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950)): ?>
<?php $component = $__componentOriginalbfc641e0710ce04e5fe02876ffc6f950; ?>
<?php unset($__componentOriginalbfc641e0710ce04e5fe02876ffc6f950); ?>
<?php endif; ?>
                        <div class="flex flex-col">
                            <span class="text-lg font-semibold">Orders</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">Today Orders Performance</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="text-end">
                            <?php if (isset($component)) { $__componentOriginalf0029cce6d19fd6d472097ff06a800a1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf0029cce6d19fd6d472097ff06a800a1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.icon-button','data' => ['icon' => 'heroicon-s-share','size' => 'lg','label' => 'Share','color' => 'info']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::icon-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['icon' => 'heroicon-s-share','size' => 'lg','label' => 'Share','color' => 'info']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf0029cce6d19fd6d472097ff06a800a1)): ?>
<?php $attributes = $__attributesOriginalf0029cce6d19fd6d472097ff06a800a1; ?>
<?php unset($__attributesOriginalf0029cce6d19fd6d472097ff06a800a1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf0029cce6d19fd6d472097ff06a800a1)): ?>
<?php $component = $__componentOriginalf0029cce6d19fd6d472097ff06a800a1; ?>
<?php unset($__componentOriginalf0029cce6d19fd6d472097ff06a800a1); ?>
<?php endif; ?>
                        </span>
                    </div>
                </div>

                
				<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split(\App\Filament\Admin\Widgets\OrderSummaryWidget::class);

$__html = app('livewire')->mount($__name, $__params, 'lw-456276864-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
				<span class="text-xs italic text-gray-500 dark:text-gray-400">Today orders by currency.</span>
                <div class="flex items-center gap-2 mb-2">
                    <span
                        class="w-7 h-7 flex items-center justify-center rounded-full border dark:border-none dark:bg-gray-700 text-gray-50 font-medium">
                        🏆
                    </span>
                    <span class="font-bold">Most Traded</span>
                </div>
                <div class="mb-4 text-gray-500 dark:text-gray-400">
                    <p>
                        With a total valuation of IDR <?php echo e(number_format($leadingTradedCurrency['totalSell'], 0, ',', '.')); ?> from <?php echo e($leadingTradedCurrency['orderCount']); ?> sales transactions, the <strong class="dark:text-teal-500"><?php echo e($leadingTradedCurrency['name']); ?> (<?php echo e($leadingTradedCurrency['symbol']); ?>)</strong> has become the <strong class="dark:text-teal-500">MOST TRADED</strong> currency this month.
                    </p>
                </div>

                
                
				
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>


<?php /**PATH D:\sites\starterkit\web_starter\resources\views/filament/admin/widgets/stacked-cashflow-widget.blade.php ENDPATH**/ ?>