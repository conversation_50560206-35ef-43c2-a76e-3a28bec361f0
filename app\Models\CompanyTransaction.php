<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'companies';

	public function banks()
    {
        return $this->hasMany(CompanyBank::class, 'company_id', 'id');
    }
	public function deposits()
    {
        return $this->hasMany(CompanyDeposito::class, 'company_id', 'id');
    }
}
