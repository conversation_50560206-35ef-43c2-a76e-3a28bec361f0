<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyDepositSummary;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\Summarizers\Count;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\View\TablesRenderHook;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class DepositSummaryWidget extends BaseWidget
{
	protected static ?string $heading = 'Balance Summary by Company';
	protected int | string | array $columnSpan = 1;

	public array $selectedCompanyIds = [];

	protected $listeners = ['order-created' => '$refresh'];

	public function mount(): void
	{
		// Initialize with empty selection to show default data
		if (empty($this->selectedCompanyIds)) {
			$this->selectedCompanyIds = [];
		}
	}


	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['Admin', 'Super Admin']);
	}

	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['Admin', 'Super Admin']);
	}

	public function table(Table $table): Table
	{
		return $table
			->query(Company::query())
			->modifyQueryUsing(function (Builder $query) {
				$query->where('type', 2)
					->whereRaw("TRIM(name) LIKE ?", ['PT%'])
					->leftJoin('company_banks', 'companies.id', '=', 'company_banks.company_id')
					->leftJoin('company_depositos', function ($join) {
						$join->on('company_banks.id', '=', 'company_depositos.bank_id')
							->whereNull('company_depositos.deleted_at'); // pastikan data aktif
					})
					->select([
						'companies.id',
						'companies.name',
						'companies.logo',
						'companies.address',

						// Total balance = in - (out + order)
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_type = 'in' THEN company_depositos.amount
							WHEN company_depositos.trx_type IN ('out', 'order') THEN -company_depositos.amount
							ELSE 0 END), 0) as total_balance"),

						// Total in
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_type = 'in' THEN company_depositos.amount
							ELSE 0 END), 0) as total_in"),

						// Total out
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_type = 'out' THEN company_depositos.amount
							ELSE 0 END), 0) as total_out"),

						// Total order
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_type = 'order' THEN company_depositos.amount
							ELSE 0 END), 0) as total_order"),

						// Jumlah bank aktif
						DB::raw("COUNT(DISTINCT company_banks.id) as bank_count"),

						// Balance bulan lalu (sampai akhir bulan lalu)
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_date < DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN
								CASE
									WHEN company_depositos.trx_type = 'in' THEN company_depositos.amount
									WHEN company_depositos.trx_type IN ('out', 'order') THEN -company_depositos.amount
									ELSE 0
								END
							ELSE 0 END), 0) as balance_last_month"),

						// Cash in bulan berjalan
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
							AND company_depositos.trx_type = 'in' THEN company_depositos.amount
							ELSE 0 END), 0) as cash_in_current_month"),

						// Cash out bulan berjalan (hanya 'out')
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
							AND company_depositos.trx_type = 'out' THEN company_depositos.amount
							ELSE 0 END), 0) as cash_out_current_month"),

						// Order bulan berjalan (hanya 'order')
						DB::raw("COALESCE(SUM(CASE
							WHEN company_depositos.trx_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
							AND company_depositos.trx_type = 'order' THEN company_depositos.amount
							ELSE 0 END), 0) as order_current_month"),
					])
					->groupBy(
						'companies.id',
						'companies.name',
						'companies.logo',
						'companies.address'
					);

				if (!empty($this->selectedCompanyIds)) {
					$query->whereIn('companies.id', $this->selectedCompanyIds);
				}
			})
			->columns([
				TextColumn::make('name')
					->label('Company')
					->sortable()
					->searchable()
					->limit(15),
				TextColumn::make('total_balance')
					->label('Total Balance')
					->alignEnd()
					->money('IDR')
					->sortable()
					->color(fn($state) => ($state ?? 0) >= 0 ? 'success' : 'danger')
					->weight('bold')
					->default(0),
			])
			->actions([
				ActionGroup::make([
					Action::make('view_modal')
						->label('Summary')
						->modalFooterActionsAlignment('right')
						->tooltip('View Bank Details')
						->modalHeading(fn($record) => "Bank Details: {$record->name}")
						->modalContent(function ($record) {
							// Get all banks for this company with their deposit summary
							$banks = \App\Models\CompanyBank::withSum([
									'deposit as total_in' => fn ($q) => $q->where('trx_type', 'in'),
									'deposit as total_out' => fn ($q) => $q->where('trx_type', 'out'),
									'deposit as total_order' => fn ($q) => $q->where('trx_type', 'order'),
									// Balance bulan lalu - perlu dihitung manual karena withSum tidak support conditional sum
									'deposit as balance_last_month_in' => fn ($q) => $q->where('trx_type', 'in')
										->whereRaw("trx_date < DATE_FORMAT(CURDATE(), '%Y-%m-01')"),
									'deposit as balance_last_month_out' => fn ($q) => $q->where('trx_type', 'out')
										->whereRaw("trx_date < DATE_FORMAT(CURDATE(), '%Y-%m-01')"),
									'deposit as balance_last_month_order' => fn ($q) => $q->where('trx_type', 'order')
										->whereRaw("trx_date < DATE_FORMAT(CURDATE(), '%Y-%m-01')"),
									// Cash in bulan berjalan
									'deposit as cash_in_current_month' => fn ($q) => $q->where('trx_type', 'in')
										->whereRaw("trx_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')"),
									// Cash out bulan berjalan (hanya 'out')
									'deposit as cash_out_current_month' => fn ($q) => $q->where('trx_type', 'out')
										->whereRaw("trx_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')"),
									// Order bulan berjalan (hanya 'order')
									'deposit as order_current_month' => fn ($q) => $q->where('trx_type', 'order')
										->whereRaw("trx_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')"),
								], 'amount')
								->where('company_id', $record->id)
								->get()
								->map(function ($bank) {
									// Current balance (total sampai sekarang)
									$bank->balance = ($bank->total_in ?? 0) - (($bank->total_out ?? 0) + ($bank->total_order ?? 0));

									// Balance last month (saldo sampai akhir bulan lalu)
									$bank->balance_last_month = ($bank->balance_last_month_in ?? 0) - (($bank->balance_last_month_out ?? 0) + ($bank->balance_last_month_order ?? 0));

									return $bank;
								});

							$totalBalance = number_format($record->total_balance ?? 0, 2, ',', '.');
							$balanceLastMonth = number_format($record->balance_last_month ?? 0, 2, ',', '.');
							$cashInCurrentMonth = number_format($record->cash_in_current_month ?? 0, 2, ',', '.');
							$cashOutCurrentMonth = number_format($record->cash_out_current_month ?? 0, 2, ',', '.');
							$orderCurrentMonth = number_format($record->order_current_month ?? 0, 2, ',', '.');
							$balanceColor = ($record->total_balance ?? 0) >= 0 ? 'text-green-600' : 'text-red-600';
							$balanceLastMonthColor = ($record->balance_last_month ?? 0) >= 0 ? 'text-green-600' : 'text-red-600';

							$bankDetails = '';
							foreach ($banks as $bank) {
								$bankBalance = number_format($bank->balance ?? 0, 2, ',', '.');
								$bankBalanceColor = ($bank->balance ?? 0) >= 0 ? 'text-green-600' : 'text-red-600';

								$bankBalanceLastMonth = number_format($bank->balance_last_month ?? 0, 2, ',', '.');
								$bankCashInCurrentMonth = number_format($bank->cash_in_current_month ?? 0, 2, ',', '.');
								$bankCashOutCurrentMonth = number_format($bank->cash_out_current_month ?? 0, 2, ',', '.');
								$bankOrderCurrentMonth = number_format($bank->order_current_month ?? 0, 2, ',', '.');
								$bankBalanceLastMonthColor = ($bank->balance_last_month ?? 0) >= 0 ? 'text-green-600' : 'text-red-600';

								$bankDetails .= "
									<div class='border rounded-lg p-2 bg-white dark:bg-transparent'>
										<div class='flex justify-between items-center mb-3'>
											<h5 class='font-semibold text-gray-800 dark:text-gray-300'>{$bank->bank_name}</h5>
											<span class='text-lg font-bold {$bankBalanceColor}'>IDR {$bankBalance}</span>
										</div>
										<div class='grid grid-cols-2 gap-2 text-xs'>
											<div class='col-span-1 w-full'>
												<table class='w-full'>
													<tr>
														<td class='align-top' width='25%'>Account:</td>
														<td width='75%' class='font-bold'>{$bank->bank_acc_no}</td>
													</tr>
													<tr>
														<td class='align-top'>Acc Name:</td>
														<td class='font-bold'>{$bank->bank_acc_name}</td>
													</tr>
												</table>
											</div>
											<div class='col-span-1 w-full'>
												<table class='w-full'>
													<tr>
														<td width='35%'>Balance Last Month:</td>
														<td width='60%' class='text-end {$bankBalanceLastMonthColor}'>IDR {$bankBalanceLastMonth}</td>
													</tr>
													<tr>
														<td>Cash In:</td>
														<td class='text-end text-green-600'>IDR {$bankCashInCurrentMonth}</td>
													</tr>
													<tr>
														<td>Cash Out:</td>
														<td class='text-end text-red-600'>IDR {$bankCashOutCurrentMonth}</td>
													</tr>
													<tr>
														<td>Order (Cash Out):</td>
														<td class='text-end text-orange-600'>IDR {$bankOrderCurrentMonth}</td>
													</tr>
													<tr>
														<td class='font-bold border-t-2'>Current Balance:</td>
														<td class='{$bankBalanceColor} text-end font-bold border-t-2'>IDR {$bankBalance}</td>
													</tr>
												</table>
											</div>
										</div>
									</div>
								";
							}

							// return new HtmlString('hello');

							return new HtmlString("
								<div class='space-y-6'>
									<!-- Company Summary -->
									<div class='bg-gray-50 dark:bg-transparent p-4 rounded-lg'>
										<h4 class='font-semibold text-gray-800 dark:text-gray-300 mb-3'>Monthly Summary</h4>
										<div class='grid grid-cols-2 gap-4 mb-4'>
											<div class='bg-gray-100 dark:bg-transparent border border-gray-50 dark:border-green-500 p-3 rounded'>
												<h5 class='font-medium text-gray-800 dark:text-gray-300'>Balance Last Month</h5>
												<p class='text-lg font-bold {$balanceLastMonthColor} dark:text-gray-300'>IDR {$balanceLastMonth}</p>
												<span class='text-xs text-gray-500'>Balance until previous month</span>
											</div>
											<div class='bg-blue-50 dark:bg-transparent border border-gray-50 dark:border-primary-500 p-3 rounded'>
												<h5 class='font-medium text-blue-800 dark:text-gray-300'>Cash In</h5>
												<p class='text-lg font-bold text-blue-600 dark:text-gray-300'>IDR {$cashInCurrentMonth}</p>
												<span class='text-xs text-gray-500'>Total inbound current month</span>
											</div>
											<div class='bg-red-50 dark:bg-transparent border border-gray-50 dark:border-red-500 p-3 rounded'>
												<h5 class='font-medium text-red-800 dark:text-gray-300'>Cash Out</h5>
												<p class='text-lg font-bold text-red-600 dark:text-gray-300'>IDR {$cashOutCurrentMonth}</p>
												<span class='text-xs text-gray-500'>Total outbound current month</span>
											</div>
											<div class='bg-orange-50 dark:bg-transparent border border-gray-50 dark:border-orange-500 p-3 rounded'>
												<h5 class='font-medium text-orange-800 dark:text-gray-300'>Order (Cash Out)</h5>
												<p class='text-lg font-bold text-orange-600 dark:text-gray-300'>IDR {$orderCurrentMonth}</p>
												<span class='text-xs text-gray-500'>Total order (outbound) current month</span>
											</div>
										</div>
										<div class='bg-gray-100 dark:bg-transparent border border-gray-50 dark:border-green-500 p-3 rounded'>
											<div class=' flex justify-between items-center'>
												<h5 class='font-medium text-gray-800 dark:text-gray-300'>Current Balance</h5>
												<p class='text-lg font-bold {$balanceColor}  dark:text-gray-300'>IDR {$totalBalance}</p>
											</div>
											<span class='text-xs text-gray-500'>Balance until today</span>
										</div>
									</div>

									<!-- Bank Details -->
									<div>
										<h4 class='font-semibold text-gray-800 dark:text-gray-300 mb-3'>Bank Details ({$record->bank_count})</h4>
										<div class='space-y-3'>
											{$bankDetails}
										</div>
									</div>
								</div>
							");
						})
						->modalWidth('2xl')
						->modalSubmitAction(false)
						->modalCancelActionLabel('Close'),
					Action::make('view_details')
						->label('Todays Summary')
						->url(fn ($record) => '/admin/company-transactions/'.$record->id),
					Action::make('details')
						->label('Details')
						->url(fn ($record) => '/admin/company-transactions/'.$record->id)
				])

			])
			// ->headerActions([
			//     Action::make('filter_company')
			//         ->label('Filter')
			//         ->icon('heroicon-o-funnel')
			//         ->form([
			//             Select::make('company_ids')
			//                 ->label('Select Companies')
			//                 ->options(Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id'))
			//                 ->placeholder('Select companies...')
			//                 ->multiple()
			//                 ->searchable()
			//                 ->required()
			//         ])
			//         ->action(function (array $data) {
			//             $this->selectedCompanyIds = $data['company_ids'] ?? [];
			//         })
			//         ->modalHeading('Filter Companies')
			//         ->modalSubmitActionLabel('Apply'),
			//     Action::make('clear_filter')
			//         ->label('Clear')
			//         ->icon('heroicon-o-x-mark')
			//         ->color('gray')
			//         ->action(function () {
			//             $this->selectedCompanyIds = [];
			//         })
			//         ->visible(fn () => !empty($this->selectedCompanyIds)),
			// ])
			->defaultSort('total_balance', 'desc')
			->striped()
			->paginated([6])
			->defaultPaginationPageOption(3)
			->emptyStateHeading('No Data Available')
			->emptyStateDescription('Select companies from filter or check if data exists.');
	}


	public function getTableRenderHooks(): array
	{
		return [
			TablesRenderHook::HEADER_BEFORE => fn() => null,
			TablesRenderHook::HEADER_AFTER => fn() => null,
		];
	}
}
