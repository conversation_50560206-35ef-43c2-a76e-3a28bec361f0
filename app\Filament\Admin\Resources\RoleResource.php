<?php

namespace App\Filament\Admin\Resources;

use Althinect\FilamentSpatieRolesPermissions\Resources\RoleResource as BaseRoleResource;
use App\Filament\Admin\Resources\RoleResource\Pages;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;
use Filament\Facades\Filament;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Illuminate\Database\Eloquent\Model;

class RoleResource extends BaseRoleResource
{
    // Override metode yang ingin Anda ubah
    // Contoh:

    // Definisikan relasi tenant untuk model Role
    protected static ?string $tenantOwnershipRelationshipName = null;

    public static function form(Form $form): Form
    {
        $webPermissions = \Spatie\Permission\Models\Permission::query()
            ->orderBy('name')
            ->where('guard_name', 'web')
            ->get()
            ->groupBy(fn($permission) => explode(' ', $permission->name)[1] ?? 'other'); // Kelompok berdasarkan prefix, dengan fallback 'other'

        $apiPermissions = \Spatie\Permission\Models\Permission::query()
            ->orderBy('name')
            ->where('guard_name', 'api')
            ->get()
            ->groupBy(fn($permission) => explode(' ', $permission->name)[1] ?? 'other'); // Kelompok berdasarkan prefix, dengan fallback 'other'

        $webSchema = [
            Placeholder::make('web_description')
                ->hiddenLabel()
                ->helperText('Izin untuk mengakses web aplikasi. Digunakan untuk autentikasi pengguna melalui interface web.')
                ->columnSpanFull(),
        ];

        foreach ($webPermissions as $group => $permissions) {
            $webSchema[] = Section::make('Model '.ucfirst($group) .' (Web)')
                ->collapsed()
                ->columnSpan(1)
                ->schema([
                    CheckboxList::make("permissions_web_{$group}")
                        ->hiddenLabel()
                        ->options($permissions->pluck('name', 'id')->toArray())
                        ->relationship(
                            name: 'permissions',
                            titleAttribute: 'name',
                            modifyQueryUsing: fn(Builder $query) => $query->whereIn('id', $permissions->pluck('id'))
                        )
                        ->bulkToggleable()
                        ->columns(2)
                        ->getOptionLabelFromRecordUsing(fn($record) => "{$record->name}")
                ]);
        }

        $apiSchema = [
            Placeholder::make('api_description')
                ->hiddenLabel()
                ->helperText('Izin untuk mengakses API. Digunakan untuk autentikasi pengguna melalui API endpoints.')
                ->columnSpanFull(),
        ];

        foreach ($apiPermissions as $group => $permissions) {
            $apiSchema[] = Section::make('Model '.ucfirst($group) .' (API)')
                ->collapsed()
                ->columnSpan(1)
                ->schema([
                    CheckboxList::make("permissions_api_{$group}")
                        ->hiddenLabel()
                        ->options($permissions->pluck('name', 'id')->toArray())
                        ->relationship(
                            name: 'permissions',
                            titleAttribute: 'name',
                            modifyQueryUsing: fn(Builder $query) => $query->whereIn('id', $permissions->pluck('id'))
                        )
                        ->bulkToggleable()
                        ->columns(2)
                        ->getOptionLabelFromRecordUsing(fn($record) => "{$record->name}")
                ]);
        }

        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label(__('filament-spatie-roles-permissions::filament-spatie.field.name'))
                                    ->required()
                                    ->unique(ignoreRecord: true, modifyRuleUsing: function (Unique $rule) {
                                        // If using teams and Tenancy, ensure uniqueness against current tenant
                                        if (config('permission.teams', false) && Filament::hasTenancy()) {
                                            // Check uniqueness against current user/team
                                            $rule->where(config('permission.column_names.team_foreign_key', 'team_id'), Filament::getTenant()->id);
                                        }
                                        return $rule;
                                    }),

                                Select::make('guard_name')
                                    ->label(__('filament-spatie-roles-permissions::filament-spatie.field.guard_name'))
                                    ->options(config('filament-spatie-roles-permissions.guard_names'))
                                    ->default(config('filament-spatie-roles-permissions.default_guard_name'))
                                    ->visible(fn() => config('filament-spatie-roles-permissions.should_show_guard', true))
                                    ->required(),

                                Select::make(config('permission.column_names.team_foreign_key', 'team_id'))
                                    ->label(__('filament-spatie-roles-permissions::filament-spatie.field.team'))
                                    ->hidden(fn() => ! config('permission.teams', false) || Filament::hasTenancy())
                                    ->options(
                                        fn() => config('filament-spatie-roles-permissions.team_model', \App\Models\Team::class)::pluck('name', 'id')
                                    )
                                    ->dehydrated(fn($state) => (int) $state > 0)
                                    ->placeholder(__('filament-spatie-roles-permissions::filament-spatie.select-team'))
                                    ->hint(__('filament-spatie-roles-permissions::filament-spatie.select-team-hint')),
                            ]),
                    ]),
                    Tabs::make('Permissions')
                        ->label(__('filament-spatie-roles-permissions::filament-spatie.field.permissions'))
                        ->tabs([
                            Tab::make('Web')
                                ->schema($webSchema)
                                ->columns(2),
                            Tab::make('API')
                                ->schema($apiSchema)
                                ->columns(2),
                        ])->contained(false)->activeTab(1),
            ])->columns(1);
    }

    public static function getPages(): array
    {
        if (config('filament-spatie-roles-permissions.should_use_simple_modal_resource.roles')) {
            return [
                'index' => Pages\ListRoles::route('/'),
            ];
        }

        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
            'view' => Pages\ViewRole::route('/{record}'),
        ];
    }

    public static function getRelations(): array
    {
        $relationManagers = [];

        // if (config('filament-spatie-roles-permissions.should_display_relation_managers.permissions', true)) {
        //     $relationManagers[] = RelationManager\PermissionRelationManager::class;
        // }

        // if (config('filament-spatie-roles-permissions.should_display_relation_managers.users', true)) {
        //     $relationManagers[] = RelationManager\UserRelationManager::class;
        // }

        return $relationManagers;
    }

    // Anda juga dapat mengubah metode lain seperti table(), dll.
}
