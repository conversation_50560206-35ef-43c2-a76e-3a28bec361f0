@props(['invoice'])

@php
    $banks = $invoice->companyBanks ?? collect();
@endphp

<div class="space-y-6">
    @if ($banks->count() > 0)
        @foreach ($banks as $index => $bank)
            @php
                // Handle JSON custom_columns properly
                $customColumns = null;
                if ($bank->custom_columns) {
                    if (is_string($bank->custom_columns)) {
                        $customColumns = json_decode($bank->custom_columns, true);
                    } elseif (is_array($bank->custom_columns)) {
                        $customColumns = $bank->custom_columns;
                    }
                }

                // Collect all non-null bank fields
                $bankFields = [
                    'Bank Information' => [
                        'Bank Name' => $bank->bank_name,
                        'Account Name' => $bank->bank_acc_name,
                        'Account Number' => $bank->bank_acc_no,
                        'Bank Code' => $bank->bank_code,
                        'Bank Correspondent' => $bank->bank_correspondent,
                    ],
                    'Addresses' => [
                        'Bank Address' => $bank->bank_address,
                        'Account Address' => $bank->bank_acc_address,
                    ],
                    'International Codes' => [
                        'SWIFT Code' => $bank->swift,
                        'SWIFT Correspondent' => $bank->swift_correspondent,
                        'IBAN' => $bank->iban,
                        'Routing Number' => $bank->routing_no,
                        'ABA' => $bank->ABA,
                        'IFSC' => $bank->IFSC,
                    ],
                    'Additional Information' => [
                        'Transit Code' => $bank->transit,
                        'TT Charge' => $bank->tt_charge,
                        'Institution Code' => $bank->institution,
                        'BSB' => $bank->bsb,
                        'Branch Code' => $bank->branch_code,
                        'Sort Code' => $bank->sort_code,
                        'Branch Bank' => $bank->branch_bank,
                    ],
                ];

                // Filter out null/empty values
                foreach ($bankFields as $sectionKey => $section) {
                    $bankFields[$sectionKey] = array_filter($section, function ($value) {
                        return !is_null($value) && $value !== '' && $value !== '<p></p>';
                    });

                    // Remove empty sections
                    if (empty($bankFields[$sectionKey])) {
                        unset($bankFields[$sectionKey]);
                    }
                }
            @endphp

            {{-- Bank Card --}}
            <div class="bg-white dark:bg-transparent border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm">
                {{-- Bank Header --}}
                <div class="bg-gray-50 dark:bg-gray-800 px-6 py-4 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold">
                                {{ $bank->bank_name ?: 'Bank ' . ($index + 1) }}
                            </h3>
                            <p class="text-sm">
                                {{ $bank->bank_acc_name ?: 'Account Name Not Specified' }}
                            </p>
                        </div>
                        <div class="text-right">
                            @if ($bank->bank_acc_no)
                                <p class="text-sm font-mono">{{ $bank->bank_acc_no }}</p>
                            @endif
                            @if ($bank->swift)
                                <p class="text-xs">SWIFT: {{ $bank->swift }}</p>
                            @endif
                        </div>
                    </div>
                </div>

                {{-- Bank Details Grid --}}
                <div class="p-6">
                    @if (!empty($bankFields))
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            @foreach ($bankFields as $sectionTitle => $fields)
                                <div class="space-y-3">
                                    <h4 class="text-sm font-medium border-b dark:border-gray-600 pb-2">
                                        {{ $sectionTitle }}
                                    </h4>
                                    <div class="space-y-2">
                                        @foreach ($fields as $label => $value)
                                            <div class="flex flex-col sm:flex-row sm:justify-between">
                                                <span
                                                    class="text-smfont-medium">{{ $label }}:</span>
                                                <span
                                                    class="text-sm mt-1 sm:mt-0 sm:text-right {{ in_array($label, ['Account Number', 'SWIFT Code', 'SWIFT Correspondent', 'IBAN', 'Routing Number', 'ABA', 'IFSC']) ? 'font-mono' : '' }}">
                                                    @if (in_array($label, ['Bank Address', 'Account Address']))
                                                        <div class="text-right">{!! $value !!}</div>
                                                    @else
                                                        {{ $value }}
                                                    @endif
                                                </span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <p class="text-sm">No additional bank information available</p>
                        </div>
                    @endif

                    {{-- Custom Columns Section --}}
                    @if ($customColumns && is_array($customColumns) && count($customColumns) > 0)
                        <div class="mt-6 pt-6 border-t dark:border-gray-600">
                            <h4 class="text-sm font-medium border-b dark:border-gray-600 pb-2 mb-4">
                                Custom Fields
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach ($customColumns as $key => $customField)
                                    @php
                                        $fieldValue = null;
                                        $fieldType = 'text';

                                        if (is_array($customField)) {
                                            $fieldValue = $customField['value'] ?? null;
                                            $fieldType = $customField['type'] ?? 'text';
                                        } else {
                                            $fieldValue = $customField;
                                        }

                                        // Skip empty values
                                        if (is_null($fieldValue) || $fieldValue === '' || $fieldValue === '<p></p>') {
                                            continue;
                                        }
                                    @endphp

                                    <div class="bg-gray-50 rounded-lg p-3">
                                        <div class="flex flex-col">
                                            <span class="text-xs font-medium uppercase tracking-wider">
                                                {{ str_replace('_', ' ', $key) }}
                                            </span>
                                            <span class="text-sm mt-1">
                                                @if ($fieldType === 'richtext')
                                                    <div class="prose prose-sm max-w-none">{!! $fieldValue !!}</div>
                                                @elseif($fieldType === 'textarea')
                                                    <div class="whitespace-pre-wrap">{{ $fieldValue }}</div>
                                                @else
                                                    {{ $fieldValue }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @endforeach

        {{-- Summary --}}
        <div class="mt-6 bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div class="flex justify-between items-center text-sm">
                <span class="">
                    <strong>Total Banks:</strong> {{ $banks->count() }}
                    {{ $banks->count() > 1 ? 'accounts' : 'account' }}
                </span>
                <span class="">
                    <strong>Template in use:</strong> {{ $invoice->company->name ?? 'Unknown Company' }}
                </span>
            </div>
        </div>
    @else
            {{-- Empty State --}}
            <div class="text-center py-12">
                <div class="mx-auto h-16 w-16 text-gray-400">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                        </path>
                    </svg>
                </div>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No Bank Accounts</h3>
                <p class="mt-2 text-sm text-gray-500">
                    This invoice doesn't have any bank accounts configured in the template.
                </p>
                <p class="mt-1 text-xs text-gray-400">
                    Bank accounts are configured at the company template level.
                </p>
            </div>
        @endif
</div>
