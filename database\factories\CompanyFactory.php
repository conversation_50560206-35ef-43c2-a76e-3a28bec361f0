<?php

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyFactory extends Factory
{
    protected $model = Company::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'address' => $this->faker->address(),
            'phone' => $this->faker->phoneNumber(),
            'email' => $this->faker->companyEmail(),
            'type' => 2, // Default company type
            'business_type' => 1, // Default business type
            'bg_color' => '#ffffff',
            'text_color' => '#000000',
            'footer_color' => '#f8f9fa',
            'heading_size' => 'medium',
            'can_replicate' => false,
        ];
    }
}
