<?php

namespace App\Filament\Admin\Widgets;

use App\Models\CompanyDepositSummary;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FinancialOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';
	protected static ?int $sort = 0;
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
    protected function getStats(): array
    {
        // 1. Total Balance - Sum all positive balances
        $totalBalance = CompanyDepositSummary::sum('balance') ?? 0;

        // 2. Cash Flow Total - Total in minus total out minus total order
        $cashFlowData = DB::table('company_depositos')
            ->whereNull('deleted_at')
            ->selectRaw('
                SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as total_in,
                SUM(CASE WHEN trx_type = "out" THEN amount ELSE 0 END) as total_out,
                SUM(CASE WHEN trx_type = "order" THEN amount ELSE 0 END) as total_order
            ')
            ->first();

        $cashFlowTotal = ($cashFlowData->total_in ?? 0) - ($cashFlowData->total_out ?? 0) - ($cashFlowData->total_order ?? 0);

        // 3. Low Balance Banks - Count banks with balance < 1,000,000
        $lowBalanceThreshold = 1000000; // 1 million IDR
        $lowBalanceBanks = CompanyDepositSummary::where('balance', '<', $lowBalanceThreshold)->count();

        return [
            Stat::make('Total Balance', 'IDR ' . number_format($totalBalance, 0, ',', '.'))
                ->description('Current balance across all banks')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color($totalBalance >= 0 ? 'success' : 'danger'),

            Stat::make('Cash Flow Total', 'IDR ' . number_format($cashFlowTotal, 0, ',', '.'))
                ->description('Total in - out - orders (all time)')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color($cashFlowTotal >= 0 ? 'success' : 'danger'),

            Stat::make('Low Balance Banks', $lowBalanceBanks)
                ->description('Banks below IDR 1M threshold')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($lowBalanceBanks > 0 ? 'warning' : 'success'),
        ];
    }
}
