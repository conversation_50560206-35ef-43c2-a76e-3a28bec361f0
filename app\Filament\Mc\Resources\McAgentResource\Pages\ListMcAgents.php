<?php

namespace App\Filament\Mc\Resources\McAgentResource\Pages;

use App\Filament\Mc\Resources\McAgentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListMcAgents extends ListRecords
{
    protected static string $resource = McAgentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('New Agent')->modalHeading('New Agent')->modalWidth('3xl'),
        ];
    }

	public function getHeading(): string
	{
        return 'MC Agents';
	}
	public function getSubheading(): string|Htmlable|null
	{
		return new HtmlString('<span class="italic text-sm text-gray-500">Agents of all mc customers.</span>');
	}
}
