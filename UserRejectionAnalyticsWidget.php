<?php

namespace App\Filament\Admin\Widgets;

use App\Models\InvoiceApprovalLog;
use Filament\Widgets\ChartWidget;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UserRejectionAnalyticsWidget extends ChartWidget
{
    protected static ?string $heading = 'Invoice Rejections by User';
    protected static ?string $pollingInterval = '60s';
    protected int | string | array $columnSpan = 2;
	protected static ?int $sort = 21;
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
    protected function getFilters(): ?array
    {
        return [
            1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
            5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
            9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
        ];
    }

    protected function getData(): array
    {
        $month = $this->filter ?? now()->month;
        $year = now()->year;

        // Get rejection data per user for selected month
        $rejectionData = InvoiceApprovalLog::rejections()
            ->whereYear('invoice_approval_logs.created_at', $year)
            ->whereMonth('invoice_approval_logs.created_at', $month)
            ->join('users', 'invoice_approval_logs.creator_id', '=', 'users.id')
            ->select('users.name', 'users.id', DB::raw('COUNT(*) as rejection_count'))
            ->groupBy('users.id', 'users.name')
            ->orderBy('rejection_count', 'desc')
            ->limit(10) // Top 10 users with most rejections
            ->get();

        // Prepare data for chart
        $labels = $rejectionData->pluck('name')->toArray();
        $data = $rejectionData->pluck('rejection_count')->toArray();

        // If no data, show empty state
        if (empty($labels)) {
            return [
                'datasets' => [
                    [
                        'label' => 'Rejections',
                        'data' => [0],
                        'backgroundColor' => ['rgba(239, 68, 68, 0.6)'],
                        'borderColor' => ['rgb(239, 68, 68)'],
                        'borderWidth' => 2,
                    ],
                ],
                'labels' => ['No Data'],
            ];
        }

        return [
            'datasets' => [
                [
                    'label' => 'Total Rejections',
                    'data' => $data,
                    'borderColor' => 'rgb(239, 68, 68)',
                    'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                    'tension' => 0.3,
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        $month = $this->filter ?? now()->month;
        $year = now()->year;
        $monthName = Carbon::create($year, $month)->format('F Y');

        return [
            'responsive' => true,
            'plugins' => [
                'title' => [
                    'display' => true,
                    'text' => "Invoice Rejections - {$monthName}",
                    'font' => [
                        'size' => 16,
                        'weight' => 'bold',
                    ],
                ],
                'legend' => [
                    'display' => false,
                ],
                'tooltip' => [
                    'callbacks' => [
                        'title' => 'function(context) {
                            return context[0].label + " - Total Rejections";
                        }',
                        'label' => 'function(context) {
                            return "Rejections: " + context.parsed.y;
                        }',
                    ],
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Number of Rejections',
                    ],
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => 'Users',
                    ],
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
            'interaction' => [
                'intersect' => false,
                'mode' => 'index',
            ],
        ];
    }

    public function getHeading(): string
    {
        $month = $this->filter ?? now()->month;
        $year = now()->year;
        $monthName = Carbon::create($year, $month)->format('F Y');
        return "Invoice Rejections by User - {$monthName}";
    }
}
