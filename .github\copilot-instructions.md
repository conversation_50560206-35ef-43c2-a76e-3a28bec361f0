# AI Agent Instructions for Laravel Invoice Management System

## Project Overview
This is a Laravel-based invoice management system designed to digitize and automate the complete invoice lifecycle - from order receipt to bank submission. The system emphasizes:
- Parent-child invoice relationship (internal vs client invoices)
- Multi-level approval workflows
- Real-time status tracking
- Automated data validation
- Bank slip generation

## Core Architecture

### Key Components
- **Frontend**: Livewire + Alpine.js + Tailwind CSS + Flowbite
- **Backend**: <PERSON><PERSON> with extensive use of Spatie packages
- **Authentication**: Laravel Fortify + Jetstream
- **Admin Panel**: Filament 3
- **Database**: Default SQLite (configurable for MySQL)
- **Monitoring**: Laravel Pulse + Health Checks

### Critical Workflows
1. **Invoice Processing**:
   ```
   Order → Internal Invoice → Client Invoice → Payment → Archive
   ```
   - Internal invoices are parents, client invoices are children
   - Each stage has specific validation rules and approval requirements

2. **Approval Flow**:
   - Transactions < $50,000: Staff → Verification → Supervisor (2hr SLA)
   - Transactions ≥ $50,000: Staff → Verification → Supervisor → CEO (24hr SLA)

## Project Conventions

### Status Management
- Draft: Initial state
- Issued: Sent to client
- Closed: Process completed
- Status updates trigger real-time notifications

### File Organization
- Models: `app/Models/` - Follow Laravel conventions
- Actions: `app/Actions/` - Complex business logic
- Services: `app/Services/` - Reusable services
- Views: `resources/views/` - Blade templates

### Key Integration Points
- **Bank APIs**: For slip generation and submission
- **PDF Generation**: Using API2PDF/PDFShift
- **Payment Processing**: Laravel Cashier + PayPal
- **Health Monitoring**: Spatie Health package

## Development Best Practices
1. **Error Handling**:
   - Use standard Laravel exceptions
   - Log errors with Spatie Activity Log
   - Return user-friendly messages through Flash

2. **Testing**:
   - PHPUnit for unit/feature tests
   - Health checks for system monitoring
   - Test coverage focused on critical flows

3. **Permission System**:
   - Use Spatie Permission package
   - Default roles: Admin, Manager, User
   - Custom permissions for specific actions

4. **Media Handling**:
   - Use Spatie Media Library
   - Store uploads in configured filesystem
   - Follow naming conventions in `config/media-library.php`

## Common Development Tasks
1. **Setup Environment**:
   ```bash
   composer install
   npm install
   php artisan migrate:fresh --seed
   ```

2. **Run Development Server**:
   ```bash
   php artisan serve
   npm run dev
   ```

3. **Add New Invoice Feature**:
   - Create migration
   - Add model with relationships
   - Implement Filament resource
   - Add to approval workflow
   - Update status tracking

## System Architecture Notes
- Uses repository pattern for data access
- Event-driven architecture for status updates
- Queue-based processing for heavy tasks
- Real-time updates via Laravel Echo
