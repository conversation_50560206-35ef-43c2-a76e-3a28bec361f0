@props(['invoice'])

@php
    $invoiceDetails = $invoice->invoiceDetails ?? collect();
    $currency = $invoice->currency;
    $currencySymbol = $currency->symbol ?? '$';
    $currencyCode = $currency->code ?? 'USD';

    // Check if company type is 2 (Indonesian company)
    $companyType = $invoice->company->type ?? 1;
    $isIndonesianCompany = $companyType == 2;

    // Override currency for Indonesian companies
    if ($isIndonesianCompany) {
        $currencySymbol = 'Rp.';
        $currencyCode = 'IDR';
    }
@endphp

<div class="overflow-hidden">
    @if($invoiceDetails->count() > 0)
        <div class="overflow-x-auto">
            <table class="w-full border-collapse border border-gray-200 rounded-lg">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-800">
                        <th class="border border-gray-200 px-4 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">
                            Description
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-200 uppercase  w-24">
                            Qty
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-200 uppercase  w-32">
                            Unit
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-200 uppercase  w-32">
                            Price
                        </th>
                        <th class="border border-gray-200 px-4 py-3 text-start text-xs font-medium text-gray-500 dark:text-gray-200 uppercase tracking-wider">
                            Subtotal
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    @foreach($invoiceDetails as $detail)
                        <tr class="">
                            <td class="border border-gray-200 px-4 py-3 text-sm">
                                <div class="max-w-xs">
                                    {!! $detail->description !!}
                                </div>
                            </td>
                            <td class="border border-gray-200 pe-2 py-3 text-sm text-end">
                                {{ number_format($detail->quantity, 2) }}
                            </td>
                            <td class="border border-gray-200 px-4 py-3 text-sm text-center">
                                {{ $detail->unit ?? '-' }}
                            </td>
                            <td class="border border-gray-200 pe-2 py-3 text-sm text-end">
                                {{ $currency->symbol }} {{ number_format($detail->price, 2) }}
                            </td>
                            <td class="border border-gray-200 pe-2 py-3 text-sm font-medium text-end">
                                {{ $currency->symbol }} {{ number_format($detail->sub_total, 2) }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr class="bg-gray-50 dark:bg-gray-800">
                        <td colspan="4" class="border border-gray-200 px-4 py-3 text-sm font-medium text-end">
                            <strong>Sub Total:</strong>
                        </td>
                        <td class="border border-gray-200 pe-2 py-3 text-sm font-bold text-end bg-blue-50  dark:bg-gray-800">
                            {{ $currency->symbol }} {{ number_format($invoiceDetails->sum('sub_total'), 2) }}
                        </td>
                    </tr>
                    @if($invoice->booking_fee > 0)
                        <tr class="bg-gray-50 dark:bg-gray-800">
                            <td colspan="4" class="border border-gray-200 px-4 py-3 text-sm font-medium text-end">
                                <strong>Booking Fee:</strong>
                            </td>
                            <td class="border border-gray-200 pe-2 py-3 text-sm font-medium text-end">
                                {{ $currency->symbol }} {{ number_format($invoice->booking_fee, 2) }}
                            </td>
                        </tr>
                    @endif
                    @if($isIndonesianCompany && $invoice->rates > 0)
                        <tr class="bg-gray-50 dark:bg-gray-800">
                            <td colspan="4" class="border border-gray-200 px-4 py-3 text-sm font-medium text-end">
                                <strong>Rates:</strong>
                            </td>
                            <td class="border border-gray-200 pe-2 py-3 text-sm font-medium text-end">
                                Rp. {{ number_format($invoice->rates, 2) }}
                            </td>
                        </tr>
                    @endif
                    <tr class="bg-blue-50 dark:bg-gray-800">
                        <td colspan="4" class="border border-gray-200 px-4 py-3 text-sm font-bold text-end">
                            <strong>Invoice Total:</strong>
                        </td>
                        <td class="border border-gray-200 pe-2 py-3 text-sm font-bold text-end">
                            {{ $currencySymbol }} {{ number_format($invoice->invoice_amount, 2) }}
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>

        {{-- Summary Information --}}
        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <h4 class="text-sm font-medium mb-2">Summary</h4>
                <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <span class="">Total Items:</span>
                        <span class="font-medium">{{ $invoiceDetails->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="">Total Quantity:</span>
                        <span class="font-medium">{{ number_format($invoiceDetails->sum('quantity'), 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="">Subtotal:</span>
                        <span class="font-medium">{{ $currencySymbol }} {{ number_format($invoiceDetails->sum('sub_total'), 2) }}</span>
                    </div>
                    @if($invoice->booking_fee > 0)
                        <div class="flex justify-between">
                            <span class="">Booking Fee:</span>
                            <span class="font-medium">{{ $currencySymbol }} {{ number_format($invoice->booking_fee, 2) }}</span>
                        </div>
                    @endif
                    @if($isIndonesianCompany && $invoice->rates > 0)
                        <div class="flex justify-between">
                            <span class="">Rates:</span>
                            <span class="font-medium">Rp. {{ number_format($invoice->rates, 2) }}</span>
                        </div>
                    @endif
                    <div class="flex justify-between pt-2 border-t border-gray-200">
                        <span class="font-semibold">Invoice Total:</span>
                        <span class="font-bold text-blue-600 dark:text-teal-300">{{ $currencySymbol }} {{ number_format($invoice->invoice_amount, 2) }}</span>
                    </div>
                </div>
            </div>

			<div>
				{{-- @if($invoice->amount_inword) --}}
					<x-filament::fieldset>
						<x-slot name="label">
							Amount in Words
						</x-slot>
						<p class="text-sm italic">
							{{ $invoice->amount_inword }}
						</p>
					</x-filament::fieldset>
				{{-- @endif --}}
					<x-filament::fieldset>
						<x-slot name="label">
							Remarks
						</x-slot>
						<p class="text-sm italic">
							{{ trim($invoice->remarks) !== '' ? $invoice->remarks : 'no remarks' }}
						</p>
					</x-filament::fieldset>
			</div>
        </div>
    @else
        <div class="text-center py-8">
            <div class="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No invoice details</h3>
            <p class="mt-1 text-sm text-gray-500">This invoice doesn't have any line items yet.</p>
        </div>
    @endif
</div>
