<?php

namespace App\Filament\Admin\Resources\BusinessTypeResource\Pages;

use App\Filament\Admin\Resources\BusinessTypeResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateBusinessType extends CreateRecord
{
    protected static string $resource = BusinessTypeResource::class;
	protected static ?string $title = 'New Business Type';
	public static string | Alignment $formActionsAlignment = Alignment::Right;

	protected static bool $canCreateAnother = false;
	protected function getCancelFormAction(): Actions\Action
    {
        return Actions\Action::make('cancel')
            ->label('Cancel')
            ->color('gray')
            ->action(fn () => $this->dispatch('close-modal'));
    }

	public function getRedirectUrl(): string
	{
		return '/admin';
	}
}
