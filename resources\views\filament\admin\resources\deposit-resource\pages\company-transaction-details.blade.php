<x-filament-widgets::widget>
    <div class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
			<x-filament::section>
				<div class="col-span-5 flex flex-col">
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Previous Balance</span>
					<span class="font-semibold">IDR {{ number_format($previousBalance,2,'.',',') }}</span>
				</div>
			</x-filament::section>
			<x-filament::section>
				<div class="col-span-5 flex flex-col">
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Today Trx In</span>
					<span class="font-semibold">IDR {{ number_format($todayIncoming,2,'.',',') }}</span>
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Trx Count: {{ $todayIncomingCount }}</span>
				</div>
			</x-filament::section>
			<x-filament::section>
				<div class="col-span-5 flex flex-col">
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Today Trx Out</span>
					<span class="font-semibold">IDR {{ number_format($todayOutgoing,2,'.',',') }}</span>
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Trx Count: {{ $todayOutgoingCount }}</span>
				</div>
			</x-filament::section>
			<x-filament::section>
				<div class="col-span-5 flex flex-col">
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Today Orders</span>
					<span class="font-semibold">IDR {{ number_format($todayOrder,2,'.',',') }}</span>
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Trx Count: {{ $todayOrderCount }}</span>
				</div>
			</x-filament::section>
			<x-filament::section class="">
				<div class="col-span-5 flex flex-col">
					<span class="fi-wi-stats-overview-stat-label text-sm font-medium text-gray-400 dark:text-gray-200 text-xs">Available Balance</span>
					<span class="font-bold {{ $availableBalance <= 0 ? 'text-red-500' : 'text-green-500' }}">IDR {{ number_format($availableBalance,2,'.',',') }}</span>
				</div>
			</x-filament::section>
		</div>
		@if ($bankAggregates->count() > 0)
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
				@foreach ($bankAggregates as $bank)
					<x-filament::section>
						<div class='flex justify-between items-center mb-3 font-bold'>
							<h5 class='font-semibold text-gray-800 dark:text-gray-300'>{{ $companyName ?? 'Unknown Company' }}</h5>
							<span class='{{ $bank->available_balance <= 0 ? 'text-red-500' : 'text-green-500' }}'>IDR {{ number_format($bank->available_balance,2,'.',',') }}</span>
						</div>
						<div class='grid grid-cols-2 gap-2 text-xs'>
							<div class='col-span-1 w-full'>
								<table class='w-full'>
									<tr>
										<td class='align-top' width='25%'>Bank:</td>
										<td width='75%' class='font-bold'>{{ $bank->bank_name }}</td>
									</tr>
									<tr>
										<td class='align-top' width='25%'>Account:</td>
										<td width='75%' class='font-bold'>{{ $bank->bank_acc_no }}</td>
									</tr>
									<tr>
										<td class='align-top'>Acc Name:</td>
										<td class='font-bold'>{{ $bank->bank_acc_name }}</td>
									</tr>
								</table>
							</div>
							<div class='col-span-1 w-full'>
								<table class='w-full'>
									<tr>
										<td width='35%'>Previous Balance:</td>
										<td width='60%' class='text-end'>IDR {{ number_format($bank->previous_balance,2,'.',',') }}</td>
									</tr>
									<tr>
										<td>Cash In:</td>
										<td class='text-end text-green-600'>IDR {{ number_format($bank->today_in,2,'.',',') }}</td>
									</tr>
									<tr>
										<td>Cash Out:</td>
										<td class='text-end text-red-600'>IDR {{ number_format($bank->today_out,2,'.',',') }}</td>
									</tr>
									<tr>
										<td>Order (Cash Out):</td>
										<td class='text-end text-orange-600'>IDR {{ number_format($bank->today_order,2,'.',',') }}</td>
									</tr>
									<tr>
										<td class='font-bold border-t-2'>Current Balance:</td>
										<td class='text-end font-bold border-t-2 {{ $bank->available_balance <= 0 ? 'text-red-500' : 'text-green-500' }}'>IDR {{ number_format($bank->available_balance,2,'.',',') }}</td>
									</tr>
								</table>
							</div>
						</div>
					</x-filament::section>
				@endforeach
			</div>
		@endif
    </div>
</x-filament-widgets::widget>
