<?php

namespace Database\Factories;

use App\Models\CompanyBank;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyBankFactory extends Factory
{
    protected $model = CompanyBank::class;

    public function definition(): array
    {
        return [
            'company_id' => 1, // Will be overridden in tests
            'bank_acc_name' => $this->faker->name(),
            'bank_code' => $this->faker->numerify('###'),
            'bank_acc_no' => $this->faker->bankAccountNumber(),
            'bank_name' => $this->faker->company() . ' Bank',
            'bank_address' => $this->faker->address(),
            'swift' => $this->faker->swiftBicNumber(),
            'is_default' => false,
            'include_in_invoice' => true,
            'custom_columns' => [],
        ];
    }
}
