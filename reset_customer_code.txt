use Illuminate\Support\Facades\DB;
use App\Models\McCustomer;
use App\Models\McAgent;

$monthMapping = [
    '01' => 'A', '02' => 'B', '03' => 'C', '04' => 'D',
    '05' => 'E', '06' => 'F', '07' => 'G', '08' => 'H',
    '09' => 'I', '10' => 'J', '11' => 'K', '12' => 'L',
];

$month = now()->format('m');
$yearCode = now()->format('y');
$monthCode = $monthMapping[$month];

// Ambil semua customer yang customer_code-nya masih kosong
$customers = McCustomer::whereNull('customer_code')->get();

foreach ($customers as $customer) {
    $agent = McAgent::find($customer->agent_id);

    if (! $agent || ! filled($agent->agent_code)) {
        echo "Lewati ID {$customer->id} karena agent tidak valid.\n";
        continue;
    }

    $prefix = "{$agent->agent_code}{$yearCode}{$monthCode}";

    $lastCustomer = McCustomer::where('customer_code', 'like', "{$prefix}%")
        ->orderByDesc('customer_code')
        ->first();

    $nextSequence = 1;
    if ($lastCustomer && preg_match('/(\d{4})$/', $lastCustomer->customer_code, $match)) {
        $nextSequence = (int) $match[1] + 1;
    }

    $sequenceFormatted = str_pad($nextSequence, 4, '0', STR_PAD_LEFT);
    $finalCode = "{$prefix}{$sequenceFormatted}";

    // Update langsung via query builder untuk menghindari event model
    DB::table('mc_customers')
        ->where('id', $customer->id)
        ->update(['customer_code' => $finalCode]);

    echo "Updated ID {$customer->id} => $finalCode\n";
}
