<?php

namespace App\Filament\Mc\Resources\McAgentResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MembersRelationManager extends RelationManager
{
    protected static string $relationship = 'members';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
				TextInput::make('name')
					->required()
					->maxLength(191),
				TextInput::make('phone')
					->maxLength(191),
				TextInput::make('note')
					->maxLength(255),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('agent_code')
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('phone'),
                TextColumn::make('note'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
				->label('New Member')
				->modalHeading('Edit Member')
				->modalFooterActionsAlignment('right'),
            ])
            ->actions([
                EditAction::make()
					->iconButton()
					->modalHeading('Edit Member')
					->modalFooterActionsAlignment('right'),
                ViewAction::make()
					->iconButton()
					->modalHeading('Member Data')
					->modalFooterActionsAlignment('right'),
                // DeleteAction::make()->iconButton()->modalHeading('Delete Member'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    // DeleteBulkAction::make(),
                ]),
            ]);
    }
}
