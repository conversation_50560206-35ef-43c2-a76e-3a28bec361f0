<?php

namespace App\Models;

use App\Services\RecalculateCustomerBalanceService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class McDeposit extends Model
{
	use HasFactory, SoftDeletes;

    protected $table = 'mc_deposits';

	protected $fillable = [
		'trx_code',
		'trx_type',
		'mc_customer_id',
		'bank_id',
		'amount',
		'origin_bank',
		'origin_account_name',
		'origin_account_no',
		'slip_date',
		'ref_code',
		'attachment',
		'evidence',
		'status',
		'validation',
	];

	protected $casts = [
		'slip_date' => 'datetime',
	];


	protected static function booted()
    {
		static::creating(function ($deposit) {
			//skip if trx_code is already set to accomodate manual trx_code
			if (!empty($deposit->trx_code)) {
				return;
			}
			$monthMapping = [
				'01' => 'A', '02' => 'B', '03' => 'C', '04' => 'D', '05' => 'E', '06' => 'F', '07' => 'G', '08' => 'H', '09' => 'I', '10' => 'J', '11' => 'K', '12' => 'L',
			];
			$currentMonth = now()->format('m');
			$mappedMonth = $monthMapping[$currentMonth];
			$yearNumber = now()->format('y');
			$userId = Auth::user()->id;
			$prefix = "MCD{$yearNumber}{$mappedMonth}U{$userId}-";

			// Cari trx_code terakhir dengan prefix itu
			$lastCode = McDeposit::where('trx_code', 'like', "{$prefix}%")
				->orderByDesc('trx_code')
				->value('trx_code');

			// Ambil 5 digit terakhir dari kode sebelumnya
			$lastNumber = 0;
			if ($lastCode && preg_match('/(\d{5})$/', $lastCode, $matches)) {
				$lastNumber = (int) $matches[1];
			}

			// Increment dan format jadi 5 digit
			$nextNumber = str_pad($lastNumber + 1, 5, '0', STR_PAD_LEFT);

			// Gabungkan jadi trx_code baru
			$deposit->trx_code = "{$prefix}{$nextNumber}";
		});

		static::created(function ($deposit) {
			RecalculateCustomerBalanceService::run($deposit->mc_customer_id);
		});
		static::updated(function ($deposit) {
			RecalculateCustomerBalanceService::run($deposit->mc_customer_id);
		});
		static::deleted(function ($deposit) {
			RecalculateCustomerBalanceService::run($deposit->mc_customer_id);
		});
    }

	public function customer()
	{
		return $this->belongsTo(McCustomer::class, 'mc_customer_id');
	}

	public function bank()
	{
		return $this->belongsTo(McBank::class);
	}
	public function order()
	{
		return $this->belongsTo(McOrder::class, 'ref_code', 'order_code');
	}
}
