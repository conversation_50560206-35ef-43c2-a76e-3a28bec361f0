<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {


        Schema::create('mc_bank_groups', function (Blueprint $table) {
            $table->id();
			$table->bigInteger('company_id')->unsigned()->nullable();
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('mc_banks', function (Blueprint $table) {
            $table->id();
			$table->bigInteger('mc_bank_group_id')->unsigned()->nullable();
            $table->string('name');
            $table->string('account_no');
            $table->string('account_name');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mc_bank_groups');
        Schema::dropIfExists('mc_banks');
    }
};
