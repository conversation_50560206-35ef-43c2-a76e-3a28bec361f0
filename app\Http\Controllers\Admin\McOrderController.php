<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\McDeposit;
use App\Models\McOrder;
use Illuminate\Http\Request;
use Spatie\Browsershot\Browsershot;

class McOrderController extends Controller
{
	public function printOrder($record)
	{
		$payload = $this->orderPayload($record);
		return view('mc.mc-order-print-view', compact('payload'));
	}

	public function printOrderBillPdf($record)
	{

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->orderPayload($record);

		$html = view('mc.mc-order-print-view', [
			'payload'        => $payload,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'A4',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		])->render();
		$footerHtml = '<div style="font-family: Courier, monospace; font-size:10px; width:100%; text-align:center; margin:0 auto;">
			This is a digitally generated invoice, no authorization signature is required. &nbsp;&nbsp;|&nbsp;&nbsp; Page <span class="pageNumber"></span> of <span class="totalPages"></span>
		</div>';
		try {
			$pdf = Browsershot::html($html)
				// ->format('A4')
				->paperSize(210, 148)
				->margins(10, 10, 10, 10)
				->noSandbox()
				->scale(0.85)
				->showBackground()
				->waitUntilNetworkIdle()
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', true)
				->setOption('footerTemplate', $footerHtml)
				->setOption('headerTemplate', '<div></div>')
				->pdf();

			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="' . $payload['orderCode'] . ' - ' . $payload['customer'] . '.pdf"');
		} catch (\Exception $e) {
			return $html; // fallback debug
		}
	}

	public function orderPayload($record)
	{
		// Ambil data order dengan relasi menggunakan eager loading
		$order = McOrder::with([
			'customer',
			'currency',
			'bank',
		])->findOrFail($record);

		$customerId = $order->mc_customer_id;

		// Hitung total deposit yang valid
		$allDeposits = McDeposit::where('mc_customer_id', $customerId)
			->where('trx_type', 'incoming')
			->sum('amount');

		$allOrders = McDeposit::where('mc_customer_id', $customerId)
			->where('trx_type', 'order')
			->sum('amount');

		// Hitung total untuk order saat ini
		$orderTotal = ($order->amount ?? 0) * ($order->sell_rates ?? 1);
		if (str_contains($order->order_code, 'CLS')) {
			$orderCurrent = $order->total_order;
		} else {
			$orderCurrent = $orderTotal + ($order->charges ?? 0);
		}

		// Hitung deposit saat ini dan saldo
		$currentDeposits = ($allDeposits - $allOrders) + $orderCurrent;
		$balance = $allDeposits - $allOrders;
		// Buat payload
		return [
			'id'			=> $order->id,
			'orderCode'		=> $order->order_code ?? '',
			'customer'		=> $order->customer->name ?? '',
			'customerId'	=> $order->customer->customer_code ?? '',
			'fxSymbol'		=> $order->currency->symbol ?? '',
			'fxName'		=> $order->currency->name ?? '',
			'amount'		=> $order->amount ?? 0,
			'rate'			=> $order->sell_rates ?? 1,
			'charges'		=> $order->charges ?? 0,
			'totalCurrent'	=> $orderCurrent,
			'totalOrder'	=> $allOrders,
			'deposit'		=> $currentDeposits,
			'balance'		=> $balance,
			'orderDate'		=> $order->created_at,
			'bankName'		=> $order->bank->name ?? '',
			'bankAccNo'		=> $order->bank->account_no ?? '',
			'bankAccName'	=> $order->bank->account_name ?? '',
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		];
	}
}
