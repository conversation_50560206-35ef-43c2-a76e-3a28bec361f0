<?php

namespace App\Filament\Mc\Widgets;

use App\Models\McCustomer;
use App\Models\McOrder;
use Carbon\Carbon;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\View\TablesRenderHook;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\HtmlString;

class McOrderWidget extends BaseWidget
{
    protected static ?string $heading = 'Today Orders';
    protected int | string | array $columnSpan = [
		'sm' => 1,
		'lg' => 2,
	];

    public array $selectedMcOrderIds = [];

    protected $listeners = ['order-created' => '$refresh'];

    public function mount(): void
    {
        // Initialize with empty selection to show default data
        if (empty($this->selectedMcOrderIds)) {
            $this->selectedMcOrderIds = [];
        }
    }


	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(McOrder::query())
            ->modifyQueryUsing(function (Builder $query) {
 				$query->where('order_date', today())->where('order_code', 'NOT LIKE', '%CLS%');
            })

			->queryStringIdentifier('order-widget')
            ->columns([
                TextColumn::make('order_code')
                    ->label('Order #')
                    ->sortable()
                    ->badge()
                    ->weight('bold'),
                TextColumn::make('customer.name')
                    ->label('Customer')
                    ->sortable()
                    ->searchable()
                    ->limit(15),
				TextColumn::make('sell_rates')
                    ->label('Rates')
                    ->sortable()
					->alignEnd()
					->searchable()
                    ->color(fn ($state) => ($state ?? 0) >= 0 ? 'success' : 'danger')
                    ->weight('bold')
					->formatStateUsing(fn ($state) => 'IDR ' . number_format($state,2,',','.')),
                TextColumn::make('total_order')
                    ->label('Order Amount')
					->alignEnd()
                    ->money('IDR')
                    ->sortable()
                    ->color(fn ($state) => ($state ?? 0) >= 0 ? 'success' : 'danger')
                    ->weight('bold')
                    ->default(0),
            ])
            ->actions([
                Action::make('view_details')
                    ->label('Order Details')
                    ->icon('heroicon-o-eye')
					->modalFooterActionsAlignment('right')
                    ->iconButton()
                    ->tooltip('View Order Details')
                    ->modalWidth('4xl')
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close')
                    ->modalHeading(fn ($record) => "Order #: {$record->order_code}")
					->modalContent(fn ($record): View => view(
						'components.global-mcorder-modalWidget',
						[
							'record' => $record,
							'amount' => number_format($record->amount ?? 0, 2, ',', '.'),
							'charges' => number_format($record->charges ?? 0, 2, ',', '.'),
							'rates' => number_format($record->sell_rates ?? 0, 2, ',', '.'),
							'totalOrder' => number_format($record->total_order ?? 0, 2, ',', '.'),
							'orderDate' => Carbon::parse($record->order_date)->format('d F Y'),
						]
					))
					->extraModalFooterActions(fn (Action $action, $record): array => [
						Action::make('printOrderPdf')
							->label('Print')
							->icon('heroicon-o-printer')
							->url(fn ($record) => route('mcOrderBillPdf', ['record' => $record->id]))
							->openUrlInNewTab()
							->color('info'),
						// Action::make('previewOrder')
						// 	->label('Preview')
						// 	->icon('heroicon-o-window')
						// 	->url(fn ($record) => route('mcOrderPreview', ['record' => $record->id]))
						// 	->openUrlInNewTab()
						// 	->color('primary'),
					])

            ])
            ->headerActions([
                Action::make('view_list')
                    ->icon('heroicon-o-list-bullet')
					->hiddenLabel()
					->tooltip('View All Orders')
					->color('primary')
					->size('sm')
					->url(fn () => route('filament.mc.resources.mc-orders.index'))
                    ->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin'])),
                Action::make('new_order')
                    ->icon('heroicon-o-shopping-cart')
					->hiddenLabel()
					->tooltip('Add New Order')
					->color('warning')
					->size('sm')
					->url('#mcorder-modal')
                    ->visible(fn () => Auth::user()->hasAnyRole(['admin', 'Super Admin'])),
            ])
            ->striped()
            ->paginated([6])
            ->defaultPaginationPageOption(3)
            ->emptyStateHeading('No Order.')
			->defaultSort('created_at', 'asc')
            ->emptyStateDescription('No order has been made today.');
    }


	public function getTableRenderHooks(): array
    {
        return [
            TablesRenderHook::HEADER_BEFORE => fn () => null,
            TablesRenderHook::HEADER_AFTER => fn () => null,
        ];
    }
}
