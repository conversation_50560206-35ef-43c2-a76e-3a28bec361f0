<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class McCustomer extends Model
{
	use HasFactory, SoftDeletes;

	protected $table = 'mc_customers';

	const FILLABLE_FIELDS = [
		'agent_id',
		'customer_code',
		'name',
		'phone',
		'note',
		'total_orders',
		'total_deposits',
		'balance'
	];

	protected $fillable = self::FILLABLE_FIELDS;

	public function orders(): HasMany
	{
		return $this->hasMany(McOrder::class);
	}

	public function deposits()
	{
		return $this->hasMany(McDeposit::class);
	}

	public function agent(): BelongsTo
	{
		return $this->belongsTo(McAgent::class, 'agent_id');
	}


	protected static function booted()
	{
		static::creating(function (McCustomer $customer) {
			DB::transaction(function () use ($customer) {
				// 1. Tentukan prefix (4 karakter)
				$agentCode = 'UN' . strtoupper(substr(dechex(crc32('unassigned' . microtime())), 0, 2));

				if ($customer->agent_id) {
					$agent = McAgent::find($customer->agent_id);
					if ($agent && filled($agent->agent_code)) {
						$agentCode = $agent->agent_code;
					}
				}

				// 2. Mapping bulan ke huruf (A–L)
				$monthMapping = [
					'01' => 'A',
					'02' => 'B',
					'03' => 'C',
					'04' => 'D',
					'05' => 'E',
					'06' => 'F',
					'07' => 'G',
					'08' => 'H',
					'09' => 'I',
					'10' => 'J',
					'11' => 'K',
					'12' => 'L',
				];

				$month = now()->format('m');
				$monthCode = $monthMapping[$month];
				$yearCode = now()->format('y');

				// 3. Cek customer terakhir dari agent + bulan ini
				$prefix = "{$agentCode}{$yearCode}{$monthCode}";

				$lastCustomer = self::where('customer_code', 'like', "{$prefix}%")
					->orderByDesc('customer_code')
					->first();

				$nextSequence = 1;
				if ($lastCustomer && preg_match('/(\d{4})$/', $lastCustomer->customer_code, $match)) {
					$nextSequence = (int)$match[1] + 1;
				}

				$sequenceFormatted = str_pad($nextSequence, 4, '0', STR_PAD_LEFT);

				// 4. Final customer_code
				$customer->customer_code = "{$prefix}{$sequenceFormatted}";
			});
		});
	}
}
