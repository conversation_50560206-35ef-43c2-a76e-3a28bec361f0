<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceBank extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'invoice_id',
        'bank_acc_name',
        'bank_code',
        'bank_acc_no',
        'bank_acc_address',
        'bank_name',
        'bank_address',
        'bank_correspondent',
        'swift',
        'swift_correspondent',
        'routing_no',
        'transit',
        'tt_charge',
        'institution',
        'iban',
        'bsb',
        'branch_code',
        'sort_code',
        'branch_bank',
        'ABA',
        'IFSC',
        'is_default',
        'custom_columns',
    ];

    protected $casts = [
        'custom_columns' => 'array',
        'is_default' => 'boolean',
    ];

    /**
     * Get all delivered (existing) column names
     *
     * @return array
     */
    public static function getDeliveredColumns(): array
    {
        return [
            'invoice_id', 'bank_acc_name', 'bank_code', 'bank_acc_no', 'bank_acc_address',
            'bank_name', 'bank_address', 'bank_correspondent', 'swift', 'swift_correspondent',
            'routing_no', 'transit', 'tt_charge', 'institution', 'iban', 'bsb',
            'branch_code', 'sort_code', 'branch_bank', 'ABA', 'IFSC', 'is_default',
            'created_at', 'updated_at', 'deleted_at', 'custom_columns'
        ];
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id', 'id');
    }

    /**
     * Get existing custom column keys for this record
     *
     * @return array
     */
    public function getExistingCustomKeys(): array
    {
        return array_keys($this->custom_columns ?? []);
    }

    /**
     * Check if a key already exists (in delivered columns or custom columns)
     *
     * @param string $key
     * @return bool
     */
    public function keyExists(string $key): bool
    {
        return in_array($key, self::getDeliveredColumns()) ||
               in_array($key, $this->getExistingCustomKeys());
    }
}
