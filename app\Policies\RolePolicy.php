<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePolicy
{
    /**
     * Determine whether the role can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->checkPermissionTo('view-any Role');
    }

    /**
     * Determine whether the role can view the model.
     */
    public function view(User $user, Role $model): bool
    {
        return $user->checkPermissionTo('view Role');
    }

    /**
     * Determine whether the role can create models.
     */
    public function create(User $user): bool
    {
        return $user->checkPermissionTo('create Role');
    }

    /**
     * Determine whether the role can update the model.
     */
    public function update(User $user, Role $model): bool
    {
        return $user->checkPermissionTo('update Role');
    }

    /**
     * Determine whether the role can delete the model.
     */
    public function delete(User $user, Role $model): bool
    {
        return $user->checkPermissionTo('delete Role');
    }

    /**
     * Determine whether the role can delete any models.
     */
    public function deleteAny(User $user): bool
    {
        return $user->checkPermissionTo('delete-any Role');
    }

    /**
     * Determine whether the role can restore the model.
     */
    public function restore(User $user, Role $model): bool
    {
        return $user->checkPermissionTo('restore Role');
    }

    /**
     * Determine whether the role can restore any models.
     */
    public function restoreAny(User $user): bool
    {
        return $user->checkPermissionTo('restore-any Role');
    }

    /**
     * Determine whether the role can replicate the model.
     */
    public function replicate(User $user, Role $model): bool
    {
        return $user->checkPermissionTo('replicate Role');
    }

    /**
     * Determine whether the role can reorder the models.
     */
    public function reorder(User $user): bool
    {
        return $user->checkPermissionTo('reorder Role');
    }

    /**
     * Determine whether the role can permanently delete the model.
     */
    public function forceDelete(User $user, Role $model): bool
    {
        return $user->checkPermissionTo('force-delete Role');
    }

    /**
     * Determine whether the role can permanently delete any models.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->checkPermissionTo('force-delete-any Role');
    }
}
