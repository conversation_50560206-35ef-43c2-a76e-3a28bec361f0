<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\CompanyDeposito;
use App\Models\CompanyDepositSummary;
use App\Models\Currency;
use App\Models\Invoice;
use App\Models\McAgent;
use App\Models\Order;
use Filament\Forms\Components\{DatePicker, Fieldset, Group, Hidden, Placeholder, Select, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class DepositFormWidget extends Widget implements HasForms
{
	use InteractsWithForms;

	protected static string $view = 'filament.admin.widgets.deposit-form-widget';
	protected static ?string $heading = 'New Transaction';

	protected int | string | array $columnSpan = 3;
	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin', 'Staff Order']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin', 'Staff Order']);
    }

	public ?array $data = [];

	public function mount(): void
	{
		$this->form->fill();
	}

	public function form(Form $form): Form
	{
		return $form
            ->schema([
				Select::make('trx_type')
					->label('Transaction Type')
					->reactive()
					->searchable()
					->options([
						'in' => 'Incoming',
						'out' => 'Outgoing',
						'bt' => 'Book Transfer',
					])
					->required()
					->helperText(fn ($state) => match ($state) {
						'in' => 'Incoming Transaction for any purposes',
						'out' => 'Outgoing Transaction for any purposes',
						'bt' => 'Book Transfer Transaction between companies',
						default => 'select transaction type',
					}),
				DatePicker::make('trx_date')->reactive()->default(now()),

				Select::make('origin_company_id')
					->label('Select Origin Company')
					->options(fn() => Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id'))
					->searchable()
					->reactive()
					->required(),

				Select::make('origin_bank_id')
					->label('Select Origin Bank')
					->options(fn($get) => CompanyBank::where('company_id', $get('origin_company_id'))->pluck('bank_name', 'id'))
					->searchable()
					->required()
					->reactive(),

				Select::make('receiving_company_id')
					->label('Select Destination Company')
					->options(function (callable $get) {
						$originId = $get('origin_company_id');

						return Company::where('type', 2)
							->when($originId, fn($query) => $query->where('id', '!=', $originId))
							->where('name', 'like', 'PT%')
							->pluck('name', 'id');
					})
					->searchable()
					->reactive()
					->required()
					->visible(fn($get) => $get('trx_type') === 'bt'),
				Select::make('receiving_bank_id')
					->label('Select Destination Bank')
					->options(fn($get) => CompanyBank::where('company_id', $get('receiving_company_id'))->pluck('bank_name', 'id'))
					->options(function ($get) {
						return CompanyBank::where('company_id', $get('receiving_company_id'))
							->get()
							->mapWithKeys(function ($bank) {
								return [
									$bank->id => $bank->bank_name . ' (' . $bank->bank_acc_no . ')',
								];
							});
					})
					->searchable()
					->required()
					->reactive()
					->visible(fn($get) => $get('trx_type') === 'bt'),
				Textarea::make('description')
					->reactive()
					->columnSpanFull()
					->maxLength(191)
					->hidden(fn($get) => $get('trx_type') === 'bt'),
				TextInput::make('amount')
					->live(onBlur:true)
					->numeric()
					->required()
					->columnSpanFull()
					->afterStateUpdated(function ($set, $state) {
						$spellTotal = Number::spell($state, locale: 'id');
						// Log::info($spellTotal);
						$set('amountInWord', $spellTotal);
					})
					->helperText(fn ($get, $state) => number_format($state ?? 0,2, ',', '.'). ' | ' . $get('amountInWord')),
            ])
			->statePath('data')
			->columns(2);
	}

	public function create(): void
	{
		$data = $this->form->getState();
		// dd($data);
		try {
			if ($data['trx_type'] === 'bt') {
				// Ambil data perusahaan tujuan
				$originCompany = Company::find($data['origin_company_id']);
				$receivingCompany = Company::find($data['receiving_company_id']);

				// Simpan record IN (receiving)
				$receipent = CompanyDeposito::create([
					'company_id'   => $data['receiving_company_id'],
					'bank_id'      => $data['receiving_bank_id'],
					'trx_type'     => 'in',
					'description'  => 'Book Transfer from ' . ($receivingCompany->name ?? '-'),
					'trx_date'     => $data['trx_date'],
					'amount'       => $data['amount'],
				]);

				// Simpan record OUT (origin)
				$origin = CompanyDeposito::create([
					'company_id'   => $data['origin_company_id'],
					'bank_id'      => $data['origin_bank_id'],
					'trx_type'     => 'out',
					'trx_date'     => $data['trx_date'],
					'amount'       => $data['amount'],
				]);
				$receipent->update([
					'description' => 'Book Transfer from ' . $originCompany->name . ' (pair:#' . $origin->trx_no . ')',
				]);
				$origin->update([
					'description' => 'Book Transfer to ' . $receivingCompany->name . ' (pair:#' . $receipent->trx_no . ')',
				]);
			} else {
				// Hanya simpan satu record
				CompanyDeposito::create([
					'company_id'  => $data['origin_company_id'],
					'bank_id'     => $data['origin_bank_id'],
					'trx_type'    => $data['trx_type'],
					'description' => $data['description'],
					'trx_date'    => $data['trx_date'],
					'amount'      => $data['amount'],
				]);
			}

			Notification::make()
				->title('Transaction Created Successfully')
				->success()
				->send();

			// Reset form
			$this->form->fill();
			$this->dispatch('deposit-created');
		} catch (\Exception $e) {
			Notification::make()
				->title('Error Creating Transaction')
				->body($e->getMessage())
				->danger()
				->send();
		}
	}

}
