<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyDepositSummary;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class trendsWidget extends BaseWidget
{
	// protected static ?int $sort = 2;
	protected int | string | array $columnSpan = 3;

	protected static string $view = 'filament.admin.widgets.cash-flow-trends';

	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public function getViewData(): array
	{
		$now = Carbon::now('Asia/Jakarta');
		$startOfMonth = $now->copy()->startOfMonth()->toDateString(); // tanggal 1 bulan ini
		$endOfMonth = $now->copy()->endOfMonth()->toDateString();     // akhir bulan ini (untuk jaga-jaga)

		// All Time Balance (Net Cash)
		$cashFlowData = DB::table('company_depositos')
			->whereNull('deleted_at')
			->selectRaw('
				SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as total_in,
				SUM(CASE WHEN trx_type = "out" THEN amount ELSE 0 END) as total_out,
				SUM(CASE WHEN trx_type = "order" THEN amount ELSE 0 END) as total_order
			') ->first();

		$cashInAll = $cashFlowData->total_in ?? 0;
		$cashOutAll = ($cashFlowData->total_out ?? 0) + ($cashFlowData->total_order ?? 0);
		$netCash = $cashInAll - $cashOutAll;

		// Cash Flow This Month
		$monthlyCashFlow = DB::table('company_depositos')
			->whereNull('deleted_at')
			->whereBetween('trx_date', [$startOfMonth, $endOfMonth])
			->selectRaw('
				SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as total_in,
				SUM(CASE WHEN trx_type IN ("out", "order") THEN amount ELSE 0 END) as total_out
			')->first();

		$cashInThisMonth = $monthlyCashFlow->total_in ?? 0;
		$cashOutThisMonth = $monthlyCashFlow->total_out ?? 0;

		// Low Balance Banks (All Time)
		$lowBalanceThreshold = 1000000;
		$lowBalanceBanks = DB::table('company_depositos')
			->whereNull('deleted_at')
			->select('company_id', DB::raw('
					SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) -
					SUM(CASE WHEN trx_type IN ("out", "order") THEN amount ELSE 0 END) as balance
				'))
			->groupBy('company_id')
			->havingRaw('balance < ?', [$lowBalanceThreshold])
			->count();

		return [
			'netCash' => number_format($netCash, 2, ',', '.'),
			'cashIn' => number_format($cashInThisMonth, 2, ',', '.'),
			'cashOut' => number_format($cashOutThisMonth, 2, ',', '.'),
			'lowBalanceBanks' => $lowBalanceBanks,
		];
	}
}
