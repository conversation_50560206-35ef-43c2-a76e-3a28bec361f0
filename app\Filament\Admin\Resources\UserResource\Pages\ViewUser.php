<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Filament\Admin\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use TomatoPHP\FilamentApi\Traits\InteractWithAPI;

class ViewUser extends ViewRecord
{
    // use InteractWithAPI;

    public static function getFilamentAPIMiddleware(): array
    {
        return [];
    }
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
