<?php

namespace App\Filament\Mc\Resources;

use App\Filament\Mc\Resources\McAgentResource\Pages;
use App\Filament\Mc\Resources\McAgentResource\RelationManagers;
use App\Filament\Mc\Resources\McAgentResource\RelationManagers\MembersRelationManager;
use App\Models\McAgent;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class McAgentResource extends Resource
{
    protected static ?string $model = McAgent::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
	protected static ?string $recordTitleAttribute = 'name';
	protected static ?string $title = 'Agents';
	public static string | Alignment $formActionsAlignment = Alignment::Right;
	protected static bool $shouldRegisterNavigation = false;

	public static function getNavigationLabel(): string
	{
		return 'Agents';
	}

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
				Group::make()
					->columns(3)
					->extraAttributes(['class'=>'mb-6',])
					->schema([
						TextInput::make('name')
							->required()
							->maxLength(191),
						TextInput::make('email')
							->email()
							->maxLength(191),
						TextInput::make('phone')
							->tel()
							->maxLength(191),
					]),
				TableRepeater::make('agentMembers')
					->hidden()
					->label('Members')
					->relationship('members')
					->headers([
						Header::make('Member Code'),
						Header::make('Member Name'),
						Header::make('Phone'),
						Header::make('Note'),
					])
					->schema([
						TextInput::make('customer_code')
							->maxLength(191)
							->readOnly(),
						TextInput::make('name')
							->required()
							->maxLength(191),
						TextInput::make('phone')
							->maxLength(191),
						TextInput::make('note')
							->maxLength(255),
					])
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable(),
                TextColumn::make('agent_code')
                    ->searchable(),
                TextColumn::make('email')
                    ->searchable(),
                TextColumn::make('phone')
                    ->searchable(),

            ])
            ->filters([
                //
            ])
            ->actions([
                EditAction::make()->iconButton(),
                ViewAction::make()->iconButton(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            MembersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMcAgents::route('/'),
            // 'create' => Pages\CreateMcAgent::route('/create'),
            // 'edit' => Pages\EditMcAgent::route('/{record}/edit'),
        ];
    }
}
