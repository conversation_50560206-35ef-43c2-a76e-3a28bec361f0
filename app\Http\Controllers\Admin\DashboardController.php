<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Models\Activity;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Mendapatkan jumlah pengguna
        $userCount = \App\Models\User::count();
        
        // Mendapatkan jumlah tim
        $teamCount = \App\Models\Team::count();
        
        // Mendapatkan aktivitas terbaru
        $recentActivities = Activity::latest()->take(10)->get();
        
        return view('admin.dashboard', compact('userCount', 'teamCount', 'recentActivities'));
    }
}
