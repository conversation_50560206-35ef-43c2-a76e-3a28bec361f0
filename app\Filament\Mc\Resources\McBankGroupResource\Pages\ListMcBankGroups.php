<?php

namespace App\Filament\Mc\Resources\McBankGroupResource\Pages;

use App\Filament\Mc\Resources\McBankGroupResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMcBankGroups extends ListRecords
{
    protected static string $resource = McBankGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('New Bank Group')->modalHeading('New Bank Group'),
        ];
    }
}
